const { Pool } = require('pg');

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'agentic_talent_pro',
  password: 'password',
  port: 5433,
});

async function addSampleData() {
  try {
    console.log('Adding sample data...');

    // Get first project, user, and resource
    const projectResult = await pool.query('SELECT id FROM projects LIMIT 1');
    const userResult = await pool.query('SELECT id FROM users LIMIT 1');
    const resourceResult = await pool.query('SELECT id FROM resources LIMIT 1');

    if (projectResult.rows.length === 0) {
      console.log('No projects found. Please create a project first.');
      return;
    }

    if (userResult.rows.length === 0) {
      console.log('No users found. Please create a user first.');
      return;
    }

    if (resourceResult.rows.length === 0) {
      console.log('No resources found. Please create a resource first.');
      return;
    }

    const projectId = projectResult.rows[0].id;
    const userId = userResult.rows[0].id;
    const resourceId = resourceResult.rows[0].id;

    console.log(`Using project: ${projectId}, user: ${userId}, resource: ${resourceId}`);

    // Add sample tasks
    const tasks = [
      {
        title: 'Setup Development Environment',
        description: 'Configure development environment with necessary tools and dependencies',
        priority: 'HIGH',
        estimatedHours: 8,
        status: 'COMPLETED'
      },
      {
        title: 'Database Schema Design',
        description: 'Design and implement the database schema for the application',
        priority: 'HIGH',
        estimatedHours: 16,
        status: 'IN_PROGRESS'
      },
      {
        title: 'API Development',
        description: 'Develop REST APIs for core functionality',
        priority: 'MEDIUM',
        estimatedHours: 24,
        status: 'IN_PROGRESS'
      },
      {
        title: 'Frontend Components',
        description: 'Create reusable React components for the user interface',
        priority: 'MEDIUM',
        estimatedHours: 20,
        status: 'TODO'
      },
      {
        title: 'Authentication System',
        description: 'Implement user authentication and authorization',
        priority: 'HIGH',
        estimatedHours: 12,
        status: 'TODO'
      },
      {
        title: 'Testing & QA',
        description: 'Write unit tests and perform quality assurance testing',
        priority: 'MEDIUM',
        estimatedHours: 16,
        status: 'TODO'
      }
    ];

    for (const task of tasks) {
      const result = await pool.query(`
        INSERT INTO tasks (id, title, description, "projectId", "assignedToId", priority, "estimatedHours", status, "startDate", "dueDate", "createdAt", "updatedAt")
        VALUES (gen_random_uuid(), $1, $2, $3, $4, $5, $6, $7, NOW(), NOW() + INTERVAL '7 days', NOW(), NOW())
        RETURNING id
      `, [
        task.title,
        task.description,
        projectId,
        userId,
        task.priority,
        task.estimatedHours,
        task.status
      ]);
      
      console.log(`Created task: ${task.title} (ID: ${result.rows[0].id})`);
    }

    // Create a sample timesheet first for invoices (or get existing one)
    let timesheetResult = await pool.query(`
      SELECT id FROM timesheets WHERE "resourceId" = $1 AND "projectId" = $2 LIMIT 1
    `, [resourceId, projectId]);

    let timesheetId;
    if (timesheetResult.rows.length > 0) {
      timesheetId = timesheetResult.rows[0].id;
      console.log(`Using existing timesheet: ${timesheetId}`);
    } else {
      timesheetResult = await pool.query(`
        INSERT INTO timesheets (id, "resourceId", "projectId", "weekStarting", "weekEnding", status, "createdAt", "updatedAt")
        VALUES (gen_random_uuid(), $1, $2, '2024-01-01', '2024-01-07', 'APPROVED', NOW(), NOW())
        RETURNING id
      `, [resourceId, projectId]);

      timesheetId = timesheetResult.rows[0].id;
      console.log(`Created timesheet: ${timesheetId}`);
    }

    // Add sample invoices
    const invoices = [
      {
        invoiceNumber: 'INV-2024-001',
        total: 15000,
        currency: 'USD',
        status: 'SENT',
        issueDate: '2024-01-01',
        dueDate: '2024-01-31'
      },
      {
        invoiceNumber: 'INV-2024-002',
        total: 25000,
        currency: 'USD',
        status: 'PAID',
        issueDate: '2024-01-15',
        dueDate: '2024-02-14'
      },
      {
        invoiceNumber: 'INV-2024-003',
        total: 8500,
        currency: 'USD',
        status: 'OVERDUE',
        issueDate: '2024-01-10',
        dueDate: '2024-02-09'
      }
    ];

    for (const invoice of invoices) {
      // Check if invoice already exists
      const existingInvoice = await pool.query('SELECT id FROM invoices WHERE "invoiceNumber" = $1', [invoice.invoiceNumber]);

      if (existingInvoice.rows.length > 0) {
        console.log(`Invoice ${invoice.invoiceNumber} already exists, skipping...`);
        continue;
      }

      const subtotal = invoice.total * 0.9; // 90% of total
      const tax = invoice.total * 0.1; // 10% tax

      const result = await pool.query(`
        INSERT INTO invoices (id, "invoiceNumber", total, subtotal, tax, status, "issueDate", "dueDate", "projectId", "resourceId", "timesheetId", "createdAt", "updatedAt")
        VALUES (gen_random_uuid(), $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, NOW(), NOW())
        RETURNING id
      `, [
        invoice.invoiceNumber,
        invoice.total,
        subtotal,
        tax,
        invoice.status,
        invoice.issueDate,
        invoice.dueDate,
        projectId,
        resourceId,
        timesheetId
      ]);

      console.log(`Created invoice: ${invoice.invoiceNumber} (ID: ${result.rows[0].id})`);
    }

    // Add sample vendors
    const vendors = [
      {
        name: 'TechCorp Solutions',
        contactPerson: 'Sarah Johnson',
        email: '<EMAIL>',
        phone: '******-0123',
        address: '123 Tech Street, San Francisco, CA 94105',
        category: 'SOFTWARE_DEVELOPMENT',
        status: 'ACTIVE'
      },
      {
        name: 'Design Studio Pro',
        contactPerson: 'Mike Chen',
        email: '<EMAIL>',
        phone: '******-0456',
        address: '456 Creative Ave, New York, NY 10001',
        category: 'DESIGN',
        status: 'ACTIVE'
      },
      {
        name: 'CloudOps Consulting',
        contactPerson: 'Alex Rodriguez',
        email: '<EMAIL>',
        phone: '******-0789',
        address: '789 Cloud Lane, Austin, TX 78701',
        category: 'CONSULTING',
        status: 'ACTIVE'
      }
    ];

    for (const vendor of vendors) {
      // Check if vendor already exists
      const existingVendor = await pool.query('SELECT id FROM vendors WHERE name = $1', [vendor.name]);

      if (existingVendor.rows.length > 0) {
        console.log(`Vendor ${vendor.name} already exists, skipping...`);
        continue;
      }

      const result = await pool.query(`
        INSERT INTO vendors (id, name, "contactPerson", email, phone, address, status, "panNumber", "bankDetails", "createdAt", "updatedAt")
        VALUES (gen_random_uuid(), $1, $2, $3, $4, $5, $6, $7, $8, NOW(), NOW())
        RETURNING id
      `, [
        vendor.name,
        vendor.contactPerson,
        vendor.email,
        vendor.phone,
        vendor.address,
        vendor.status,
        `PAN${Math.random().toString(36).substr(2, 8).toUpperCase()}`, // Generate random PAN
        JSON.stringify({
          bankName: 'Sample Bank',
          accountNumber: `ACC${Math.random().toString(36).substr(2, 10)}`,
          routingNumber: '*********',
          accountType: 'CHECKING'
        })
      ]);

      console.log(`Created vendor: ${vendor.name} (ID: ${result.rows[0].id})`);
    }

    console.log('Sample data added successfully!');
    
  } catch (error) {
    console.error('Error adding sample data:', error);
  } finally {
    await pool.end();
  }
}

addSampleData();
