import express from 'express';
import cors from 'cors';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { Pool } from 'pg';

// Extend Express Request type to include user
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        email: string;
        role: string;
      };
    }
  }
}

const app = express();
const PORT = process.env.PORT || 3003;

// Database connection
const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5433/agentic_talent_pro',
});

// Middleware
app.use(cors({
  origin: ['http://localhost:3002', 'http://localhost:3000'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  preflightContinue: false,
  optionsSuccessStatus: 204
}));
app.use(express.json());

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Login endpoint
app.post('/api/auth/login', async (req, res) => {
  try {
    const { email, password } = req.body;

    // Find user
    const userResult = await pool.query('SELECT * FROM users WHERE email = $1', [email]);
    const user = userResult.rows[0];

    if (!user) {
      return res.status(401).json({ success: false, error: 'Invalid credentials' });
    }

    // Check password
    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      return res.status(401).json({ success: false, error: 'Invalid credentials' });
    }

    // Check if user is active
    if (user.status !== 'ACTIVE') {
      return res.status(401).json({ success: false, error: 'Account is not active' });
    }

    // Generate token
    const jwtSecret = process.env.JWT_SECRET || 'dev-jwt-secret-key-for-development-only';
    const refreshSecret = process.env.JWT_REFRESH_SECRET || 'dev-refresh-secret-key-for-development-only';

    const token = jwt.sign(
      { id: user.id, email: user.email, role: user.role },
      jwtSecret,
      { expiresIn: '7d' }
    );

    const refreshToken = jwt.sign(
      { id: user.id, email: user.email, role: user.role },
      refreshSecret,
      { expiresIn: '30d' }
    );

    // Remove password from response
    const { password: _, ...userWithoutPassword } = user;

    res.json({
      success: true,
      data: {
        user: userWithoutPassword,
        token,
        refreshToken,
      },
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Authentication middleware
const authenticateToken = async (req: any, res: any, next: any) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ success: false, error: 'Access token required' });
    }

    const token = authHeader.substring(7);
    const jwtSecret = process.env.JWT_SECRET || 'dev-jwt-secret-key-for-development-only';
    const decoded = jwt.verify(token, jwtSecret) as any;

    const userResult = await pool.query('SELECT * FROM users WHERE id = $1', [decoded.id]);
    const user = userResult.rows[0];

    if (!user) {
      return res.status(404).json({ success: false, error: 'User not found' });
    }

    req.user = { id: user.id, email: user.email, role: user.role };
    next();
  } catch (error) {
    console.error('Auth middleware error:', error);
    res.status(401).json({ success: false, error: 'Invalid token' });
  }
};

// Get profile endpoint
app.get('/api/auth/profile', async (req, res) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ success: false, error: 'Access token required' });
    }

    const token = authHeader.substring(7);
    const jwtSecret = process.env.JWT_SECRET || 'dev-jwt-secret-key-for-development-only';
    const decoded = jwt.verify(token, jwtSecret) as any;

    const userResult = await pool.query('SELECT * FROM users WHERE id = $1', [decoded.id]);
    const user = userResult.rows[0];

    if (!user) {
      return res.status(404).json({ success: false, error: 'User not found' });
    }

    const { password: _, ...userWithoutPassword } = user;
    res.json({ success: true, data: userWithoutPassword });
  } catch (error) {
    console.error('Profile error:', error);
    res.status(401).json({ success: false, error: 'Invalid token' });
  }
});

// Dashboard stats endpoint
app.get('/api/dashboard/stats', async (req, res) => {
  try {
    const [contractsResult, projectsResult, resourcesResult, timesheetsResult, invoicesResult] = await Promise.all([
      pool.query('SELECT COUNT(*) FROM contracts'),
      pool.query('SELECT COUNT(*) FROM projects WHERE status = $1', ['ACTIVE']),
      pool.query('SELECT COUNT(*) FROM resources WHERE status = $1', ['AVAILABLE']),
      pool.query('SELECT COUNT(*) FROM timesheets WHERE status = $1', ['SUBMITTED']),
      pool.query('SELECT COUNT(*) FROM invoices WHERE status IN ($1, $2)', ['SENT', 'OVERDUE']),
    ]);

    const stats = {
      overview: {
        totalContracts: parseInt(contractsResult.rows[0].count),
        activeProjects: parseInt(projectsResult.rows[0].count),
        totalResources: parseInt(resourcesResult.rows[0].count),
        pendingTimesheets: parseInt(timesheetsResult.rows[0].count),
        unpaidInvoices: parseInt(invoicesResult.rows[0].count),
      },
    };

    res.json({ success: true, data: stats });
  } catch (error) {
    console.error('Dashboard stats error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Dashboard charts endpoint
app.get('/api/dashboard/charts', async (req, res) => {
  try {
    const [projectStatusResult, resourceUtilizationResult] = await Promise.all([
      pool.query('SELECT status, COUNT(*) as count FROM projects GROUP BY status'),
      pool.query('SELECT status, COUNT(*) as count FROM resources GROUP BY status'),
    ]);

    const charts = {
      projectStatus: projectStatusResult.rows,
      resourceUtilization: resourceUtilizationResult.rows,
      monthlyInvoices: [], // Placeholder for now
    };

    res.json({ success: true, data: charts });
  } catch (error) {
    console.error('Dashboard charts error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Get Contracts endpoint
app.get('/api/contracts', async (req, res) => {
  try {
    const { page = 1, limit = 10, status, search } = req.query;

    let query = `
      SELECT c.*, u."firstName", u."lastName", u.email
      FROM contracts c
      JOIN users u ON c."clientId" = u.id
      WHERE 1=1
    `;

    const params = [];
    let paramCount = 0;

    if (status) {
      paramCount++;
      query += ` AND c.status = $${paramCount}`;
      params.push(status);
    }

    if (search) {
      paramCount++;
      query += ` AND (c.title ILIKE $${paramCount} OR c.description ILIKE $${paramCount} OR u."firstName" ILIKE $${paramCount} OR u."lastName" ILIKE $${paramCount})`;
      params.push(`%${search}%`);
    }

    query += ` ORDER BY c."createdAt" DESC`;

    const offset = (parseInt(page as string) - 1) * parseInt(limit as string);
    paramCount++;
    query += ` LIMIT $${paramCount}`;
    params.push(parseInt(limit as string));

    paramCount++;
    query += ` OFFSET $${paramCount}`;
    params.push(offset);

    const result = await pool.query(query, params);

    // Get total count
    let countQuery = `
      SELECT COUNT(*)
      FROM contracts c
      JOIN users u ON c."clientId" = u.id
      WHERE 1=1
    `;
    const countParams = [];
    let countParamCount = 0;

    if (status) {
      countParamCount++;
      countQuery += ` AND c.status = $${countParamCount}`;
      countParams.push(status);
    }

    if (search) {
      countParamCount++;
      countQuery += ` AND (c.title ILIKE $${countParamCount} OR c.description ILIKE $${countParamCount} OR u."firstName" ILIKE $${countParamCount} OR u."lastName" ILIKE $${countParamCount})`;
      countParams.push(`%${search}%`);
    }

    const countResult = await pool.query(countQuery, countParams);
    const total = parseInt(countResult.rows[0].count);

    res.json({
      success: true,
      data: result.rows,
      pagination: {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        total,
        totalPages: Math.ceil(total / parseInt(limit as string)),
      },
    });
  } catch (error) {
    console.error('Get contracts error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Create Contract endpoint
app.post('/api/contracts', authenticateToken, async (req, res) => {
  try {
    if (req.user!.role !== 'ADMIN' && req.user!.role !== 'PROJECT_MANAGER') {
      return res.status(403).json({ success: false, error: 'Insufficient permissions' });
    }

    const {
      title,
      description,
      clientId,
      type,
      contractCategory = 'DEVELOPMENT',
      priority = 'MEDIUM',
      riskLevel = 'LOW',
      complexityLevel = 'MEDIUM',
      startDate,
      endDate,
      value,
      currency = 'USD',
      paymentTerms = 'NET30',
      estimatedEffort = 0,
      terms,
      securityLevel = 'STANDARD',
      confidentialityLevel = 'STANDARD',
      governingLaw = '',
      disputeResolution = '',
      ipRights = '',
      technologyStack = '[]',
      deliverables = '[]',
      milestones = '[]',
      qualityStandards = '[]',
      testingRequirements = '[]',
      resourceRequirements = '[]',
      skillRequirements = '[]',
      projectTemplate = '',
      autoGenerateProject = false,
      slaRequirements = '[]',
      performanceMetrics = '[]',
      successCriteria = '[]',
      kpis = '[]',
      supportLevel = 'STANDARD',
      maintenanceTerms = '[]',
      trainingRequirements = '[]',
      documentationRequirements = '[]',
      clientContact = '{}',
      stakeholders = '[]',
      communicationPlan = '{}',
      escalationMatrix = '[]',
      riskAssessment = '[]',
      complianceRequirements = '[]',
      contingencyPlan = '',
      penaltyClause = '',
      bonusClause = '',
      terminationClause = '',
      renewalTerms = '',
      changeManagement = '{}',
      dependencies = '[]',
      assumptions = '[]',
      constraints = '[]',
      exclusions = '[]',
    } = req.body;

    if (!title || !description || !clientId || !type || !startDate || !endDate || !value || !terms) {
      return res.status(400).json({
        success: false,
        error: 'Title, description, client, type, dates, value, and terms are required'
      });
    }

    // Verify client exists
    const clientCheck = await pool.query('SELECT id, role FROM users WHERE id = $1', [clientId]);
    if (clientCheck.rows.length === 0 || clientCheck.rows[0].role !== 'CLIENT') {
      return res.status(400).json({ success: false, error: 'Invalid client ID' });
    }

    // Validate dates
    if (new Date(startDate) >= new Date(endDate)) {
      return res.status(400).json({ success: false, error: 'End date must be after start date' });
    }

    // Generate contract number
    const contractNumber = await pool.query('SELECT generate_contract_number()');
    const contractId = `contract-${Date.now()}`;

    const result = await pool.query(`
      INSERT INTO contracts (
        id, "contractNumber", title, description, "clientId", type, "contractCategory",
        priority, "riskLevel", "complexityLevel", status, "startDate", "endDate",
        value, currency, "paymentTerms", "estimatedEffort", terms, "securityLevel",
        "confidentialityLevel", "governingLaw", "disputeResolution", "ipRights",
        "technologyStack", deliverables, milestones, "qualityStandards", "testingRequirements",
        "resourceRequirements", "skillRequirements", "projectTemplate", "autoGenerateProject",
        "slaRequirements", "performanceMetrics", "successCriteria", kpis, "supportLevel",
        "maintenanceTerms", "trainingRequirements", "documentationRequirements",
        "clientContact", stakeholders, "communicationPlan", "escalationMatrix",
        "riskAssessment", "complianceRequirements", "contingencyPlan",
        "penaltyClause", "bonusClause", "terminationClause", "renewalTerms", "changeManagement",
        dependencies, assumptions, constraints, exclusions,
        "createdAt", "updatedAt"
      )
      VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, 'DRAFT', $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22,
        $23, $24, $25, $26, $27, $28, $29, $30, $31, $32, $33, $34, $35, $36, $37, $38, $39, $40, $41, $42, $43, $44, $45,
        $46, $47, $48, $49, $50, $51, $52, $53, $54, $55, NOW(), NOW()
      )
      RETURNING *
    `, [
      contractId, contractNumber.rows[0].generate_contract_number, title, description, clientId, type, contractCategory,
      priority, riskLevel, complexityLevel, new Date(startDate).toISOString(), new Date(endDate).toISOString(),
      value, currency, paymentTerms, estimatedEffort, terms, securityLevel, confidentialityLevel, governingLaw,
      disputeResolution, ipRights, technologyStack, deliverables, milestones, qualityStandards, testingRequirements,
      resourceRequirements, skillRequirements, projectTemplate, autoGenerateProject, slaRequirements,
      performanceMetrics, successCriteria, kpis, supportLevel, maintenanceTerms, trainingRequirements,
      documentationRequirements, clientContact, stakeholders, communicationPlan, escalationMatrix,
      riskAssessment, complianceRequirements, contingencyPlan, penaltyClause, bonusClause, terminationClause,
      renewalTerms, changeManagement, dependencies, assumptions, constraints, exclusions
    ]);

    // Get contract with client info
    const contractWithClient = await pool.query(`
      SELECT c.*, u."firstName", u."lastName", u.email
      FROM contracts c
      JOIN users u ON c."clientId" = u.id
      WHERE c.id = $1
    `, [contractId]);

    res.status(201).json({
      success: true,
      data: contractWithClient.rows[0],
      message: 'Contract created successfully',
    });
  } catch (error) {
    console.error('Create contract error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Update Contract endpoint
app.put('/api/contracts/:id', authenticateToken, async (req, res) => {
  try {
    if (req.user!.role !== 'ADMIN' && req.user!.role !== 'PROJECT_MANAGER') {
      return res.status(403).json({ success: false, error: 'Insufficient permissions' });
    }

    const { id } = req.params;
    const updateData = req.body;

    // Check if contract exists
    const existingContract = await pool.query('SELECT * FROM contracts WHERE id = $1', [id]);
    if (existingContract.rows.length === 0) {
      return res.status(404).json({ success: false, error: 'Contract not found' });
    }

    // Validate dates if provided
    if (updateData.startDate && updateData.endDate) {
      if (new Date(updateData.startDate) >= new Date(updateData.endDate)) {
        return res.status(400).json({ success: false, error: 'End date must be after start date' });
      }
    }

    // Build dynamic update query
    const updateFields = [];
    const updateValues = [];
    let paramCount = 0;

    const allowedFields = [
      'title', 'description', 'type', 'status', 'value', 'currency', 'terms',
      'contractCategory', 'priority', 'riskLevel', 'complexityLevel', 'paymentTerms', 'estimatedEffort',
      'securityLevel', 'confidentialityLevel', 'governingLaw', 'disputeResolution', 'ipRights',
      'technologyStack', 'deliverables', 'milestones', 'qualityStandards', 'testingRequirements',
      'resourceRequirements', 'skillRequirements', 'projectTemplate', 'autoGenerateProject',
      'slaRequirements', 'performanceMetrics', 'successCriteria', 'kpis', 'supportLevel',
      'maintenanceTerms', 'trainingRequirements', 'documentationRequirements',
      'clientContact', 'stakeholders', 'communicationPlan', 'escalationMatrix',
      'riskAssessment', 'complianceRequirements', 'contingencyPlan',
      'penaltyClause', 'bonusClause', 'terminationClause', 'renewalTerms', 'changeManagement',
      'dependencies', 'assumptions', 'constraints', 'exclusions'
    ];

    Object.keys(updateData).forEach(key => {
      if (allowedFields.includes(key)) {
        paramCount++;
        updateFields.push(`"${key}" = $${paramCount}`);
        updateValues.push(updateData[key]);
      } else if (key === 'startDate' || key === 'endDate') {
        paramCount++;
        updateFields.push(`"${key}" = $${paramCount}`);
        updateValues.push(new Date(updateData[key]).toISOString());
      }
    });

    if (updateFields.length === 0) {
      return res.status(400).json({ success: false, error: 'No valid fields to update' });
    }

    paramCount++;
    updateFields.push(`"updatedAt" = NOW()`);
    updateValues.push(id);

    const updateQuery = `
      UPDATE contracts
      SET ${updateFields.join(', ')}
      WHERE id = $${paramCount}
      RETURNING *
    `;

    const result = await pool.query(updateQuery, updateValues);

    // Get contract with client info
    const contractWithClient = await pool.query(`
      SELECT c.*, u."firstName", u."lastName", u.email
      FROM contracts c
      JOIN users u ON c."clientId" = u.id
      WHERE c.id = $1
    `, [id]);

    res.json({
      success: true,
      data: contractWithClient.rows[0],
      message: 'Contract updated successfully',
    });
  } catch (error) {
    console.error('Update contract error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Delete Contract endpoint
app.delete('/api/contracts/:id', authenticateToken, async (req, res) => {
  try {
    if (req.user!.role !== 'ADMIN') {
      return res.status(403).json({ success: false, error: 'Only admins can delete contracts' });
    }

    const { id } = req.params;

    // Check if contract exists
    const existingContract = await pool.query('SELECT * FROM contracts WHERE id = $1', [id]);
    if (existingContract.rows.length === 0) {
      return res.status(404).json({ success: false, error: 'Contract not found' });
    }

    // Check if contract has associated projects
    const projectsCheck = await pool.query('SELECT COUNT(*) FROM projects WHERE "contractId" = $1', [id]);
    if (parseInt(projectsCheck.rows[0].count) > 0) {
      return res.status(400).json({
        success: false,
        error: 'Cannot delete contract with associated projects'
      });
    }

    await pool.query('DELETE FROM contracts WHERE id = $1', [id]);

    res.json({
      success: true,
      message: 'Contract deleted successfully',
    });
  } catch (error) {
    console.error('Delete contract error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Get Contract by ID endpoint
app.get('/api/contracts/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const result = await pool.query(`
      SELECT c.*, u."firstName", u."lastName", u.email, u.phone
      FROM contracts c
      JOIN users u ON c."clientId" = u.id
      WHERE c.id = $1
    `, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ success: false, error: 'Contract not found' });
    }

    res.json({
      success: true,
      data: result.rows[0],
    });
  } catch (error) {
    console.error('Get contract by ID error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Projects endpoint
app.get('/api/projects', async (req, res) => {
  try {
    const { search, status, page = 1, limit = 10 } = req.query;
    const offset = (parseInt(page as string) - 1) * parseInt(limit as string);

    let query = `
      SELECT p.*, c.title as contract_title, u."firstName", u."lastName"
      FROM projects p
      JOIN contracts c ON p."contractId" = c.id
      JOIN users u ON p."managerId" = u.id
      WHERE 1=1
    `;
    const params: any[] = [];
    let paramCount = 0;

    if (search) {
      paramCount++;
      query += ` AND (p.name ILIKE $${paramCount} OR p.description ILIKE $${paramCount} OR c.title ILIKE $${paramCount})`;
      params.push(`%${search}%`);
    }

    if (status) {
      paramCount++;
      query += ` AND p.status = $${paramCount}`;
      params.push(status);
    }

    query += ` ORDER BY p."createdAt" DESC`;

    if (limit) {
      paramCount++;
      query += ` LIMIT $${paramCount}`;
      params.push(parseInt(limit as string));

      if (offset) {
        paramCount++;
        query += ` OFFSET $${paramCount}`;
        params.push(offset);
      }
    }

    const result = await pool.query(query, params);

    // Get total count for pagination
    let countQuery = `
      SELECT COUNT(*) as total
      FROM projects p
      JOIN contracts c ON p."contractId" = c.id
      JOIN users u ON p."managerId" = u.id
      WHERE 1=1
    `;
    const countParams: any[] = [];
    let countParamCount = 0;

    if (search) {
      countParamCount++;
      countQuery += ` AND (p.name ILIKE $${countParamCount} OR p.description ILIKE $${countParamCount} OR c.title ILIKE $${countParamCount})`;
      countParams.push(`%${search}%`);
    }

    if (status) {
      countParamCount++;
      countQuery += ` AND p.status = $${countParamCount}`;
      countParams.push(status);
    }

    const countResult = await pool.query(countQuery, countParams);
    const total = parseInt(countResult.rows[0].total);

    res.json({
      success: true,
      data: result.rows,
      pagination: {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        total,
        totalPages: Math.ceil(total / parseInt(limit as string)),
      },
    });
  } catch (error) {
    console.error('Projects error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Resources endpoint
app.get('/api/resources', async (req, res) => {
  try {
    const { search, status, page = 1, limit = 10 } = req.query;
    const offset = (parseInt(page as string) - 1) * parseInt(limit as string);

    let query = `
      SELECT r.*, u."firstName", u."lastName", u.email, u.phone
      FROM resources r
      JOIN users u ON r."userId" = u.id
      WHERE 1=1
    `;
    const params: any[] = [];
    let paramCount = 0;

    if (search) {
      paramCount++;
      query += ` AND (u."firstName" ILIKE $${paramCount} OR u."lastName" ILIKE $${paramCount} OR u.email ILIKE $${paramCount} OR r.designation ILIKE $${paramCount})`;
      params.push(`%${search}%`);
    }

    if (status) {
      paramCount++;
      query += ` AND r.status = $${paramCount}`;
      params.push(status);
    }

    query += ` ORDER BY r."createdAt" DESC`;

    paramCount++;
    query += ` LIMIT $${paramCount}`;
    params.push(parseInt(limit as string));

    paramCount++;
    query += ` OFFSET $${paramCount}`;
    params.push(offset);

    const result = await pool.query(query, params);

    // Get total count for pagination
    let countQuery = `
      SELECT COUNT(*) as total
      FROM resources r
      JOIN users u ON r."userId" = u.id
      WHERE 1=1
    `;
    const countParams: any[] = [];
    let countParamCount = 0;

    if (search) {
      countParamCount++;
      countQuery += ` AND (u."firstName" ILIKE $${countParamCount} OR u."lastName" ILIKE $${countParamCount} OR u.email ILIKE $${countParamCount} OR r.designation ILIKE $${countParamCount})`;
      countParams.push(`%${search}%`);
    }

    if (status) {
      countParamCount++;
      countQuery += ` AND r.status = $${countParamCount}`;
      countParams.push(status);
    }

    const countResult = await pool.query(countQuery, countParams);
    const total = parseInt(countResult.rows[0].total);

    res.json({
      success: true,
      data: result.rows,
      pagination: {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        total,
        totalPages: Math.ceil(total / parseInt(limit as string)),
      },
    });
  } catch (error) {
    console.error('Resources error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Tasks endpoint
app.get('/api/tasks', async (req, res) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const offset = (page - 1) * limit;

    const result = await pool.query(`
      SELECT t.*, p.name as project_name, u."firstName", u."lastName"
      FROM tasks t
      JOIN projects p ON t."projectId" = p.id
      LEFT JOIN users u ON t."assignedToId" = u.id
      ORDER BY t."createdAt" DESC
      LIMIT $1 OFFSET $2
    `, [limit, offset]);

    const countResult = await pool.query('SELECT COUNT(*) FROM tasks');
    const total = parseInt(countResult.rows[0].count);

    res.json({
      success: true,
      data: result.rows,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Tasks error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Timesheets endpoint
app.get('/api/timesheets', async (req, res) => {
  try {
    const { search, status, page = 1, limit = 10 } = req.query;
    const offset = (parseInt(page as string) - 1) * parseInt(limit as string);

    let query = `
      SELECT ts.*, p.name as project_name, u."firstName", u."lastName"
      FROM timesheets ts
      JOIN resources r ON ts."resourceId" = r.id
      JOIN users u ON r."userId" = u.id
      JOIN projects p ON ts."projectId" = p.id
      WHERE 1=1
    `;
    const params: any[] = [];
    let paramCount = 0;

    if (search) {
      paramCount++;
      query += ` AND (p.name ILIKE $${paramCount} OR u."firstName" ILIKE $${paramCount} OR u."lastName" ILIKE $${paramCount})`;
      params.push(`%${search}%`);
    }

    if (status) {
      paramCount++;
      query += ` AND ts.status = $${paramCount}`;
      params.push(status);
    }

    query += ` ORDER BY ts."weekEnding" DESC`;

    paramCount++;
    query += ` LIMIT $${paramCount}`;
    params.push(parseInt(limit as string));

    paramCount++;
    query += ` OFFSET $${paramCount}`;
    params.push(offset);

    const result = await pool.query(query, params);

    // Get total count for pagination
    let countQuery = `
      SELECT COUNT(*) as total
      FROM timesheets ts
      JOIN resources r ON ts."resourceId" = r.id
      JOIN users u ON r."userId" = u.id
      JOIN projects p ON ts."projectId" = p.id
      WHERE 1=1
    `;
    const countParams: any[] = [];
    let countParamCount = 0;

    if (search) {
      countParamCount++;
      countQuery += ` AND (p.name ILIKE $${countParamCount} OR u."firstName" ILIKE $${countParamCount} OR u."lastName" ILIKE $${countParamCount})`;
      countParams.push(`%${search}%`);
    }

    if (status) {
      countParamCount++;
      countQuery += ` AND ts.status = $${countParamCount}`;
      countParams.push(status);
    }

    const countResult = await pool.query(countQuery, countParams);
    const total = parseInt(countResult.rows[0].total);

    res.json({
      success: true,
      data: result.rows,
      pagination: {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        total,
        totalPages: Math.ceil(total / parseInt(limit as string)),
      },
    });
  } catch (error) {
    console.error('Timesheets error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Invoices endpoint
app.get('/api/invoices', async (req, res) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const offset = (page - 1) * limit;

    const result = await pool.query(`
      SELECT i.*, p.name as project_name, c.title as contract_title
      FROM invoices i
      JOIN projects p ON i."projectId" = p.id
      JOIN contracts c ON p."contractId" = c.id
      ORDER BY i."createdAt" DESC
      LIMIT $1 OFFSET $2
    `, [limit, offset]);

    const countResult = await pool.query('SELECT COUNT(*) FROM invoices');
    const total = parseInt(countResult.rows[0].count);

    res.json({
      success: true,
      data: result.rows,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Invoices error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Vendors endpoint
app.get('/api/vendors', async (req, res) => {
  try {
    const { search, category, page = 1, limit = 10 } = req.query;
    const offset = (parseInt(page as string) - 1) * parseInt(limit as string);

    let query = `SELECT * FROM vendors WHERE 1=1`;
    const params: any[] = [];
    let paramCount = 0;

    if (search) {
      paramCount++;
      query += ` AND (name ILIKE $${paramCount} OR email ILIKE $${paramCount} OR "contactPerson" ILIKE $${paramCount})`;
      params.push(`%${search}%`);
    }

    if (category) {
      paramCount++;
      query += ` AND category = $${paramCount}`;
      params.push(category);
    }

    query += ` ORDER BY "createdAt" DESC`;

    paramCount++;
    query += ` LIMIT $${paramCount}`;
    params.push(parseInt(limit as string));

    paramCount++;
    query += ` OFFSET $${paramCount}`;
    params.push(offset);

    const result = await pool.query(query, params);

    // Get total count for pagination
    let countQuery = `SELECT COUNT(*) as total FROM vendors WHERE 1=1`;
    const countParams: any[] = [];
    let countParamCount = 0;

    if (search) {
      countParamCount++;
      countQuery += ` AND (name ILIKE $${countParamCount} OR email ILIKE $${countParamCount} OR "contactPerson" ILIKE $${countParamCount})`;
      countParams.push(`%${search}%`);
    }

    if (category) {
      countParamCount++;
      countQuery += ` AND category = $${countParamCount}`;
      countParams.push(category);
    }

    const countResult = await pool.query(countQuery, countParams);
    const total = parseInt(countResult.rows[0].total);

    res.json({
      success: true,
      data: result.rows,
      pagination: {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        total,
        totalPages: Math.ceil(total / parseInt(limit as string)),
      },
    });
  } catch (error) {
    console.error('Vendors error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Create Vendor endpoint
app.post('/api/vendors', authenticateToken, async (req, res) => {
  try {
    if (req.user!.role !== 'ADMIN' && req.user!.role !== 'HR_MANAGER') {
      return res.status(403).json({ success: false, error: 'Insufficient permissions' });
    }

    const {
      name, contactPerson, email, phone, alternatePhone,
      address, city, state, country, pincode,
      companyType, incorporationDate, registrationNumber, gstNumber, panNumber, tanNumber,
      bankDetails, msmeCertificate, iso27001, iso9001, cmmiLevel,
      contractType, paymentTerms, currency, rating, status
    } = req.body;

    if (!name || !contactPerson || !email || !phone || !address || !panNumber || !bankDetails) {
      return res.status(400).json({
        success: false,
        error: 'Name, contact person, email, phone, address, PAN number, and bank details are required'
      });
    }

    // Check if email already exists
    const existingVendor = await pool.query('SELECT id FROM vendors WHERE email = $1', [email]);
    if (existingVendor.rows.length > 0) {
      return res.status(400).json({ success: false, error: 'Vendor with this email already exists' });
    }

    const vendorId = `vendor-${Date.now()}`;
    const result = await pool.query(`
      INSERT INTO vendors (
        id, name, "contactPerson", email, phone, "alternatePhone",
        address, city, state, country, pincode,
        "companyType", "incorporationDate", "registrationNumber", "gstNumber", "panNumber", "tanNumber",
        "bankDetails", "msmeCertificate", "iso27001", "iso9001", "cmmiLevel",
        "contractType", "paymentTerms", currency, rating, status,
        "onboardedBy", "onboardedAt", "createdAt", "updatedAt"
      )
      VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23, $24, $25, $26, $27, $28, NOW(), NOW(), NOW()
      )
      RETURNING *
    `, [
      vendorId, name, contactPerson, email, phone, alternatePhone,
      address, city, state, country, pincode,
      companyType, incorporationDate ? new Date(incorporationDate).toISOString() : null,
      registrationNumber, gstNumber, panNumber, tanNumber,
      bankDetails, msmeCertificate || false, iso27001 || false, iso9001 || false, cmmiLevel,
      contractType, paymentTerms || 'NET30', currency || 'INR', rating || 0, status || 'ACTIVE',
      req.user!.id
    ]);

    res.json({
      success: true,
      data: result.rows[0],
      message: 'Vendor created successfully',
    });
  } catch (error) {
    console.error('Create vendor error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Update Vendor endpoint
app.put('/api/vendors/:id', authenticateToken, async (req, res) => {
  try {
    if (req.user!.role !== 'ADMIN' && req.user!.role !== 'HR_MANAGER') {
      return res.status(403).json({ success: false, error: 'Insufficient permissions' });
    }

    const { id } = req.params;
    const {
      name, contactPerson, email, phone, alternatePhone,
      address, city, state, country, pincode,
      companyType, incorporationDate, registrationNumber, gstNumber, panNumber, tanNumber,
      bankDetails, msmeCertificate, iso27001, iso9001, cmmiLevel,
      contractType, paymentTerms, currency, rating, status
    } = req.body;

    // Check if vendor exists
    const existingVendor = await pool.query('SELECT * FROM vendors WHERE id = $1', [id]);
    if (existingVendor.rows.length === 0) {
      return res.status(404).json({ success: false, error: 'Vendor not found' });
    }

    // Check if email is being changed and if it conflicts with another vendor
    if (email && email !== existingVendor.rows[0].email) {
      const emailCheck = await pool.query('SELECT id FROM vendors WHERE email = $1 AND id != $2', [email, id]);
      if (emailCheck.rows.length > 0) {
        return res.status(400).json({ success: false, error: 'Another vendor with this email already exists' });
      }
    }

    // Build update query dynamically
    const updateFields = [];
    const updateParams = [];
    let paramCount = 0;

    if (name !== undefined) {
      paramCount++;
      updateFields.push(`name = $${paramCount}`);
      updateParams.push(name);
    }
    if (contactPerson !== undefined) {
      paramCount++;
      updateFields.push(`"contactPerson" = $${paramCount}`);
      updateParams.push(contactPerson);
    }
    if (email !== undefined) {
      paramCount++;
      updateFields.push(`email = $${paramCount}`);
      updateParams.push(email);
    }
    if (phone !== undefined) {
      paramCount++;
      updateFields.push(`phone = $${paramCount}`);
      updateParams.push(phone);
    }
    if (alternatePhone !== undefined) {
      paramCount++;
      updateFields.push(`"alternatePhone" = $${paramCount}`);
      updateParams.push(alternatePhone);
    }
    if (address !== undefined) {
      paramCount++;
      updateFields.push(`address = $${paramCount}`);
      updateParams.push(address);
    }
    if (city !== undefined) {
      paramCount++;
      updateFields.push(`city = $${paramCount}`);
      updateParams.push(city);
    }
    if (state !== undefined) {
      paramCount++;
      updateFields.push(`state = $${paramCount}`);
      updateParams.push(state);
    }
    if (country !== undefined) {
      paramCount++;
      updateFields.push(`country = $${paramCount}`);
      updateParams.push(country);
    }
    if (pincode !== undefined) {
      paramCount++;
      updateFields.push(`pincode = $${paramCount}`);
      updateParams.push(pincode);
    }
    if (companyType !== undefined) {
      paramCount++;
      updateFields.push(`"companyType" = $${paramCount}`);
      updateParams.push(companyType);
    }
    if (incorporationDate !== undefined) {
      paramCount++;
      updateFields.push(`"incorporationDate" = $${paramCount}`);
      updateParams.push(incorporationDate ? new Date(incorporationDate).toISOString() : null);
    }
    if (registrationNumber !== undefined) {
      paramCount++;
      updateFields.push(`"registrationNumber" = $${paramCount}`);
      updateParams.push(registrationNumber);
    }
    if (gstNumber !== undefined) {
      paramCount++;
      updateFields.push(`"gstNumber" = $${paramCount}`);
      updateParams.push(gstNumber);
    }
    if (panNumber !== undefined) {
      paramCount++;
      updateFields.push(`"panNumber" = $${paramCount}`);
      updateParams.push(panNumber);
    }
    if (tanNumber !== undefined) {
      paramCount++;
      updateFields.push(`"tanNumber" = $${paramCount}`);
      updateParams.push(tanNumber);
    }
    if (bankDetails !== undefined) {
      paramCount++;
      updateFields.push(`"bankDetails" = $${paramCount}`);
      updateParams.push(bankDetails);
    }
    if (msmeCertificate !== undefined) {
      paramCount++;
      updateFields.push(`"msmeCertificate" = $${paramCount}`);
      updateParams.push(msmeCertificate);
    }
    if (iso27001 !== undefined) {
      paramCount++;
      updateFields.push(`"iso27001" = $${paramCount}`);
      updateParams.push(iso27001);
    }
    if (iso9001 !== undefined) {
      paramCount++;
      updateFields.push(`"iso9001" = $${paramCount}`);
      updateParams.push(iso9001);
    }
    if (cmmiLevel !== undefined) {
      paramCount++;
      updateFields.push(`"cmmiLevel" = $${paramCount}`);
      updateParams.push(cmmiLevel);
    }
    if (contractType !== undefined) {
      paramCount++;
      updateFields.push(`"contractType" = $${paramCount}`);
      updateParams.push(contractType);
    }
    if (paymentTerms !== undefined) {
      paramCount++;
      updateFields.push(`"paymentTerms" = $${paramCount}`);
      updateParams.push(paymentTerms);
    }
    if (currency !== undefined) {
      paramCount++;
      updateFields.push(`currency = $${paramCount}`);
      updateParams.push(currency);
    }
    if (rating !== undefined) {
      paramCount++;
      updateFields.push(`rating = $${paramCount}`);
      updateParams.push(rating);
    }
    if (status !== undefined) {
      paramCount++;
      updateFields.push(`status = $${paramCount}`);
      updateParams.push(status);
    }

    if (updateFields.length === 0) {
      return res.json({ success: true, message: 'No changes to update' });
    }

    updateFields.push(`"updatedAt" = NOW()`);
    updateParams.push(id);

    const updateQuery = `
      UPDATE vendors
      SET ${updateFields.join(', ')}
      WHERE id = $${paramCount + 1}
      RETURNING *
    `;

    const result = await pool.query(updateQuery, updateParams);

    res.json({
      success: true,
      data: result.rows[0],
      message: 'Vendor updated successfully',
    });
  } catch (error) {
    console.error('Update vendor error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Get Vendor by ID endpoint
app.get('/api/vendors/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    const result = await pool.query('SELECT * FROM vendors WHERE id = $1', [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ success: false, error: 'Vendor not found' });
    }

    res.json({
      success: true,
      data: result.rows[0],
    });
  } catch (error) {
    console.error('Get vendor error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Get Payments endpoint
app.get('/api/payments', authenticateToken, async (req, res) => {
  try {
    const { search, status, limit = 50, offset = 0 } = req.query;

    let query = `
      SELECT p.*,
             r.id as resource_id, r."employmentType", r."hourlyRate",
             u."firstName", u."lastName", u.email as resource_email,
             v.id as vendor_id, v.name as vendor_name, v."contactPerson" as vendor_contact,
             i."invoiceNumber", i."projectId",
             pr.name as project_name
      FROM payments p
      LEFT JOIN resources r ON p."resourceId" = r.id
      LEFT JOIN users u ON r."userId" = u.id
      LEFT JOIN vendors v ON p."vendorId" = v.id
      LEFT JOIN invoices i ON p."invoiceId" = i.id
      LEFT JOIN projects pr ON i."projectId" = pr.id
      WHERE 1=1
    `;

    const params = [];
    let paramCount = 0;

    if (search) {
      paramCount++;
      query += ` AND (
        u."firstName" ILIKE $${paramCount} OR
        u."lastName" ILIKE $${paramCount} OR
        v.name ILIKE $${paramCount} OR
        p."referenceNumber" ILIKE $${paramCount} OR
        i."invoiceNumber" ILIKE $${paramCount}
      )`;
      params.push(`%${search}%`);
    }

    if (status) {
      paramCount++;
      query += ` AND p.status = $${paramCount}`;
      params.push(status);
    }

    query += ` ORDER BY p."createdAt" DESC`;

    if (limit) {
      paramCount++;
      query += ` LIMIT $${paramCount}`;
      params.push(parseInt(limit as string));
    }

    if (offset) {
      paramCount++;
      query += ` OFFSET $${paramCount}`;
      params.push(parseInt(offset as string));
    }

    const result = await pool.query(query, params);

    // Transform the results to include nested objects
    const payments = result.rows.map(row => ({
      id: row.id,
      invoiceId: row.invoiceId,
      resourceId: row.resourceId,
      vendorId: row.vendorId,
      paymentType: row.paymentType,
      paymentMethod: row.paymentMethod,
      amount: row.amount,
      currency: row.currency,
      grossAmount: row.grossAmount,
      taxDeducted: row.taxDeducted,
      netAmount: row.netAmount,
      paymentDate: row.paymentDate,
      dueDate: row.dueDate,
      status: row.status,
      referenceNumber: row.referenceNumber,
      bankDetails: row.bankDetails,
      notes: row.notes,
      processedBy: row.processedBy,
      processedAt: row.processedAt,
      createdAt: row.createdAt,
      updatedAt: row.updatedAt,
      resource: row.resource_id ? {
        id: row.resource_id,
        employmentType: row.employmentType,
        hourlyRate: row.hourlyRate,
        user: {
          firstName: row.firstName,
          lastName: row.lastName,
          email: row.resource_email,
        }
      } : null,
      vendor: row.vendor_id ? {
        id: row.vendor_id,
        name: row.vendor_name,
        contactPerson: row.vendor_contact,
      } : null,
      invoice: row.invoiceNumber ? {
        invoiceNumber: row.invoiceNumber,
        projectId: row.projectId,
      } : null,
      project: row.project_name ? {
        name: row.project_name,
      } : null,
    }));

    res.json({
      success: true,
      data: payments,
      pagination: {
        total: payments.length,
        limit: parseInt(limit as string),
        offset: parseInt(offset as string),
      },
    });
  } catch (error) {
    console.error('Get payments error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Process Payment endpoint
app.post('/api/payments/process', authenticateToken, async (req, res) => {
  try {
    if (req.user!.role !== 'ADMIN' && req.user!.role !== 'BILLING_MANAGER') {
      return res.status(403).json({ success: false, error: 'Insufficient permissions' });
    }

    const {
      invoiceId, resourceId, vendorId, paymentType, paymentMethod,
      amount, grossAmount, taxDeducted, netAmount, currency,
      paymentDate, notes, bankDetails
    } = req.body;

    if (!invoiceId || !resourceId || !paymentType || !amount) {
      return res.status(400).json({
        success: false,
        error: 'Invoice ID, resource ID, payment type, and amount are required'
      });
    }

    // Check if payment already exists for this invoice
    const existingPayment = await pool.query(
      'SELECT id FROM payments WHERE "invoiceId" = $1',
      [invoiceId]
    );

    if (existingPayment.rows.length > 0) {
      return res.status(400).json({
        success: false,
        error: 'Payment already exists for this invoice'
      });
    }

    // Generate reference number
    const paymentCount = await pool.query('SELECT COUNT(*) FROM payments');
    const count = parseInt(paymentCount.rows[0].count) + 1;
    const referenceNumber = `PAY-${new Date().getFullYear()}-${String(count).padStart(6, '0')}`;

    const paymentId = `payment-${Date.now()}`;
    const result = await pool.query(`
      INSERT INTO payments (
        id, "invoiceId", "resourceId", "vendorId", "paymentType", "paymentMethod",
        amount, currency, "grossAmount", "taxDeducted", "netAmount",
        "paymentDate", "dueDate", status, "referenceNumber", "bankDetails", notes,
        "processedBy", "processedAt", "createdAt", "updatedAt"
      )
      VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, 'PROCESSING', $14, $15, $16, $17, NOW(), NOW(), NOW()
      )
      RETURNING *
    `, [
      paymentId, invoiceId, resourceId, vendorId, paymentType, paymentMethod,
      amount, currency, grossAmount, taxDeducted, netAmount,
      new Date(paymentDate).toISOString(),
      new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // Due in 7 days
      referenceNumber, bankDetails, notes, req.user!.id
    ]);

    // Update invoice as paid
    await pool.query(
      'UPDATE invoices SET "paidAt" = NOW(), status = \'PAID\', "updatedAt" = NOW() WHERE id = $1',
      [invoiceId]
    );

    // Simulate payment processing (in real system, this would integrate with payment gateway)
    setTimeout(async () => {
      try {
        await pool.query(
          'UPDATE payments SET status = \'COMPLETED\', "updatedAt" = NOW() WHERE id = $1',
          [paymentId]
        );
      } catch (error) {
        console.error('Payment completion error:', error);
      }
    }, 2000); // Simulate 2 second processing time

    res.json({
      success: true,
      data: result.rows[0],
      message: 'Payment processing initiated successfully',
    });
  } catch (error) {
    console.error('Process payment error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Get Payment Statistics endpoint
app.get('/api/payments/stats', authenticateToken, async (req, res) => {
  try {
    const stats = await pool.query(`
      SELECT
        COALESCE(SUM(amount), 0) as total_payments,
        COUNT(*) FILTER (WHERE status = 'PENDING') as pending_count,
        COALESCE(SUM(amount) FILTER (WHERE status = 'PENDING'), 0) as pending_amount,
        COUNT(*) FILTER (WHERE status = 'COMPLETED' AND "paymentDate" >= date_trunc('month', CURRENT_DATE)) as monthly_count,
        COALESCE(SUM(amount) FILTER (WHERE status = 'COMPLETED' AND "paymentDate" >= date_trunc('month', CURRENT_DATE)), 0) as monthly_amount,
        COUNT(*) FILTER (WHERE status = 'FAILED') as failed_count,
        COALESCE(SUM(amount) FILTER (WHERE status = 'FAILED'), 0) as failed_amount
      FROM payments
    `);

    res.json({
      success: true,
      data: {
        totalPayments: parseFloat(stats.rows[0].total_payments) || 0,
        pendingCount: parseInt(stats.rows[0].pending_count) || 0,
        pendingAmount: parseFloat(stats.rows[0].pending_amount) || 0,
        monthlyCount: parseInt(stats.rows[0].monthly_count) || 0,
        monthlyAmount: parseFloat(stats.rows[0].monthly_amount) || 0,
        failedCount: parseInt(stats.rows[0].failed_count) || 0,
        failedAmount: parseFloat(stats.rows[0].failed_amount) || 0,
      },
    });
  } catch (error) {
    console.error('Get payment stats error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Generate Project from Contract endpoint
app.post('/api/projects/generate-from-contract', authenticateToken, async (req, res) => {
  try {
    if (req.user!.role !== 'ADMIN' && req.user!.role !== 'PROJECT_MANAGER') {
      return res.status(403).json({ success: false, error: 'Insufficient permissions' });
    }

    const { contractId, resourcePlan, skillRequirements, autoCreateResourcePlans = true } = req.body;

    if (!contractId) {
      return res.status(400).json({ success: false, error: 'Contract ID is required' });
    }

    // Get contract details
    const contractResult = await pool.query(`
      SELECT c.*, u."firstName", u."lastName", u.email
      FROM contracts c
      JOIN users u ON c."clientId" = u.id
      WHERE c.id = $1
    `, [contractId]);

    if (contractResult.rows.length === 0) {
      return res.status(404).json({ success: false, error: 'Contract not found' });
    }

    const contract = contractResult.rows[0];

    // Generate project from contract
    const projectId = `project-${Date.now()}`;
    const projectName = `${contract.title} - Project`;
    const projectDescription = `Auto-generated project from contract: ${contract.contractNumber}\n\n${contract.description}`;

    // Create project
    const projectResult = await pool.query(`
      INSERT INTO projects (
        id, name, description, "contractId", "clientId", status, "startDate", "endDate",
        budget, currency, "createdAt", "updatedAt"
      )
      VALUES ($1, $2, $3, $4, $5, 'PLANNING', $6, $7, $8, $9, NOW(), NOW())
      RETURNING *
    `, [
      projectId, projectName, projectDescription, contractId, contract.clientId,
      contract.startDate, contract.endDate, contract.value, contract.currency
    ]);

    const project = projectResult.rows[0];

    // Create resource plans if requested
    const createdResourcePlans = [];
    if (autoCreateResourcePlans && resourcePlan && resourcePlan.length > 0) {
      for (const role of resourcePlan) {
        const resourcePlanId = `rp-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

        const resourcePlanResult = await pool.query(`
          INSERT INTO resource_plans (
            id, "projectId", role, "requiredCount", "allocationPercentage",
            "minExperience", "maxBudget", description, status, "createdAt", "updatedAt"
          )
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8, 'OPEN', NOW(), NOW())
          RETURNING *
        `, [
          resourcePlanId, projectId, role.role, role.requiredCount, role.allocationPercent,
          role.minExperience, role.maxBudget, role.description
        ]);

        createdResourcePlans.push(resourcePlanResult.rows[0]);

        // Add skill requirements to resource plan
        if (skillRequirements && skillRequirements.length > 0) {
          for (const skill of skillRequirements) {
            if (skill.mandatory) {
              await pool.query(`
                INSERT INTO resource_plan_skills (
                  "resourcePlanId", "skillId", level, mandatory, "createdAt"
                )
                VALUES ($1, $2, $3, $4, NOW())
              `, [resourcePlanId, skill.skillId, skill.level, skill.mandatory]);
            }
          }
        }
      }
    }

    // Create milestones from contract milestones
    const createdMilestones = [];
    if (contract.milestones) {
      try {
        const milestones = JSON.parse(contract.milestones);
        for (let i = 0; i < milestones.length; i++) {
          const milestone = milestones[i];
          const milestoneId = `milestone-${Date.now()}-${i}`;

          const milestoneResult = await pool.query(`
            INSERT INTO project_milestones (
              id, "projectId", title, description, "dueDate", status, "createdAt", "updatedAt"
            )
            VALUES ($1, $2, $3, $4, $5, 'PENDING', NOW(), NOW())
            RETURNING *
          `, [
            milestoneId, projectId, milestone.title || `Milestone ${i + 1}`,
            milestone.description || '', milestone.dueDate || contract.endDate
          ]);

          createdMilestones.push(milestoneResult.rows[0]);
        }
      } catch (error) {
        console.log('Error parsing milestones:', error);
      }
    }

    // Create basic tasks from deliverables
    const createdTasks = [];
    if (contract.deliverables) {
      try {
        const deliverables = JSON.parse(contract.deliverables);
        for (let i = 0; i < deliverables.length; i++) {
          const deliverable = deliverables[i];
          const taskId = `task-${Date.now()}-${i}`;

          const taskResult = await pool.query(`
            INSERT INTO tasks (
              id, "projectId", title, description, status, priority, "dueDate", "createdAt", "updatedAt"
            )
            VALUES ($1, $2, $3, $4, 'TODO', 'MEDIUM', $5, NOW(), NOW())
            RETURNING *
          `, [
            taskId, projectId, deliverable.title || `Deliverable ${i + 1}`,
            deliverable.description || '', deliverable.dueDate || contract.endDate
          ]);

          createdTasks.push(taskResult.rows[0]);
        }
      } catch (error) {
        console.log('Error parsing deliverables:', error);
      }
    }

    // Update contract status to indicate project has been generated
    await pool.query(`
      UPDATE contracts
      SET status = 'ACTIVE', "updatedAt" = NOW()
      WHERE id = $1
    `, [contractId]);

    res.status(201).json({
      success: true,
      data: {
        project,
        resourcePlans: createdResourcePlans,
        milestones: createdMilestones,
        tasks: createdTasks,
        contract: { ...contract, status: 'ACTIVE' }
      },
      message: 'Project and resource plans generated successfully from contract',
    });
  } catch (error) {
    console.error('Generate project from contract error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Create task endpoint
app.post('/api/tasks', authenticateToken, async (req, res) => {
  try {
    const { title, description, projectId, assignedToId, priority, estimatedHours, dueDate, startDate } = req.body;

    if (!title || !description || !projectId || !assignedToId) {
      return res.status(400).json({ success: false, error: 'Title, description, project ID, and assigned to ID are required' });
    }

    // Check permissions
    if (req.user!.role !== 'ADMIN' && req.user!.role !== 'PROJECT_MANAGER') {
      return res.status(403).json({ success: false, error: 'Insufficient permissions' });
    }

    // Verify project exists
    const projectResult = await pool.query('SELECT id, "managerId" FROM projects WHERE id = $1', [projectId]);
    if (projectResult.rows.length === 0) {
      return res.status(404).json({ success: false, error: 'Project not found' });
    }

    const project = projectResult.rows[0];

    // Check if user can create tasks for this project
    if (req.user!.role === 'PROJECT_MANAGER' && project.managerId !== req.user!.id) {
      return res.status(403).json({ success: false, error: 'Access denied' });
    }

    // Verify assignee exists
    const assigneeResult = await pool.query('SELECT id FROM users WHERE id = $1', [assignedToId]);
    if (assigneeResult.rows.length === 0) {
      return res.status(404).json({ success: false, error: 'Assignee not found' });
    }

    const taskId = `task-${Date.now()}`;
    const taskStartDate = startDate || new Date().toISOString();
    const taskDueDate = dueDate || new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(); // Default to 1 week from now
    const taskPriority = priority || 'MEDIUM';
    const taskEstimatedHours = estimatedHours || 8;

    const result = await pool.query(`
      INSERT INTO tasks (id, title, description, "projectId", "assignedToId", priority, "estimatedHours", "startDate", "dueDate", status, "createdAt", "updatedAt")
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, 'TODO', NOW(), NOW())
      RETURNING *
    `, [taskId, title, description, projectId, assignedToId, taskPriority, taskEstimatedHours, taskStartDate, taskDueDate]);

    res.json({
      success: true,
      data: result.rows[0],
      message: 'Task created successfully',
    });
  } catch (error) {
    console.error('Create task error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Get tasks endpoint
app.get('/api/tasks', authenticateToken, async (req, res) => {
  try {
    const { projectId, assignedToId, status, search, page = 1, limit = 10 } = req.query;
    const offset = (parseInt(page as string) - 1) * parseInt(limit as string);

    let query = `
      SELECT t.*, p.name as project_name, u."firstName", u."lastName", u.email as assignee_email
      FROM tasks t
      JOIN projects p ON t."projectId" = p.id
      JOIN users u ON t."assignedToId" = u.id
      WHERE 1=1
    `;
    const params: any[] = [];
    let paramCount = 0;

    if (search) {
      paramCount++;
      query += ` AND (t.title ILIKE $${paramCount} OR t.description ILIKE $${paramCount})`;
      params.push(`%${search}%`);
    }

    if (projectId) {
      paramCount++;
      query += ` AND t."projectId" = $${paramCount}`;
      params.push(projectId);
    }

    if (assignedToId) {
      paramCount++;
      query += ` AND t."assignedToId" = $${paramCount}`;
      params.push(assignedToId);
    }

    if (status) {
      paramCount++;
      query += ` AND t.status = $${paramCount}`;
      params.push(status);
    }

    // Filter based on user role
    if (req.user!.role === 'RESOURCE') {
      paramCount++;
      query += ` AND t."assignedToId" = $${paramCount}`;
      params.push(req.user!.id);
    }

    query += ` ORDER BY t."createdAt" DESC`;

    // Add pagination
    paramCount++;
    query += ` LIMIT $${paramCount}`;
    params.push(parseInt(limit as string));

    paramCount++;
    query += ` OFFSET $${paramCount}`;
    params.push(offset);

    const result = await pool.query(query, params);

    // Get total count for pagination
    let countQuery = `
      SELECT COUNT(*) as total
      FROM tasks t
      WHERE 1=1
    `;
    const countParams: any[] = [];
    let countParamCount = 0;

    if (search) {
      countParamCount++;
      countQuery += ` AND (t.title ILIKE $${countParamCount} OR t.description ILIKE $${countParamCount})`;
      countParams.push(`%${search}%`);
    }

    if (projectId) {
      countParamCount++;
      countQuery += ` AND t."projectId" = $${countParamCount}`;
      countParams.push(projectId);
    }

    if (assignedToId) {
      countParamCount++;
      countQuery += ` AND t."assignedToId" = $${countParamCount}`;
      countParams.push(assignedToId);
    }

    if (status) {
      countParamCount++;
      countQuery += ` AND t.status = $${countParamCount}`;
      countParams.push(status);
    }

    if (req.user!.role === 'RESOURCE') {
      countParamCount++;
      countQuery += ` AND t."assignedToId" = $${countParamCount}`;
      countParams.push(req.user!.id);
    }

    const countResult = await pool.query(countQuery, countParams);
    const total = parseInt(countResult.rows[0].total);

    const tasks = result.rows.map(row => ({
      id: row.id,
      title: row.title,
      description: row.description,
      projectId: row.projectId,
      assignedToId: row.assignedToId,
      status: row.status,
      priority: row.priority,
      estimatedHours: row.estimatedHours,
      actualHours: row.actualHours,
      startDate: row.startDate,
      dueDate: row.dueDate,
      createdAt: row.createdAt,
      updatedAt: row.updatedAt,
      project: {
        id: row.projectId,
        name: row.project_name,
      },
      assignedTo: {
        id: row.assignedToId,
        firstName: row.firstName,
        lastName: row.lastName,
        email: row.assignee_email,
      },
    }));

    res.json({
      success: true,
      data: tasks,
      pagination: {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        total,
        totalPages: Math.ceil(total / parseInt(limit as string)),
      },
    });
  } catch (error) {
    console.error('Get tasks error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Create timesheet endpoint
app.post('/api/timesheets', authenticateToken, async (req, res) => {
  try {
    const { resourceId, projectId, weekStarting, weekEnding, regularHours, overtimeHours = 0 } = req.body;

    if (!projectId || !weekStarting) {
      return res.status(400).json({ success: false, error: 'Project ID and week starting date are required' });
    }

    let targetResourceId = resourceId;

    // If no resourceId provided, use current user's resource
    if (!targetResourceId) {
      const resourceResult = await pool.query('SELECT id FROM resources WHERE "userId" = $1', [req.user!.id]);
      if (resourceResult.rows.length === 0) {
        return res.status(404).json({ success: false, error: 'Resource not found for user' });
      }
      targetResourceId = resourceResult.rows[0].id;
    } else {
      // If resourceId is provided, check if user has permission
      if (req.user!.role !== 'ADMIN') {
        return res.status(403).json({ success: false, error: 'Only admins can create timesheets for other resources' });
      }

      // Verify the resource exists
      const targetResourceResult = await pool.query('SELECT id FROM resources WHERE id = $1', [targetResourceId]);
      if (targetResourceResult.rows.length === 0) {
        return res.status(404).json({ success: false, error: 'Target resource not found' });
      }
    }

    const timesheetId = `timesheet-${Date.now()}`;

    // Calculate week ending date if not provided
    let weekEndingDate = weekEnding;
    if (!weekEndingDate) {
      const startDate = new Date(weekStarting);
      const endDate = new Date(startDate);
      endDate.setDate(startDate.getDate() + 6);
      weekEndingDate = endDate.toISOString().split('T')[0];
    }

    // Check if timesheet already exists for this week and project
    const existingResult = await pool.query(`
      SELECT id FROM timesheets
      WHERE "resourceId" = $1 AND "projectId" = $2 AND "weekStarting" = $3
    `, [targetResourceId, projectId, weekStarting]);

    if (existingResult.rows.length > 0) {
      return res.status(400).json({ success: false, error: 'Timesheet already exists for this week and project' });
    }

    const result = await pool.query(`
      INSERT INTO timesheets (id, "resourceId", "projectId", "weekStarting", "weekEnding", "totalHours", status, "createdAt", "updatedAt")
      VALUES ($1, $2, $3, $4, $5, $6, 'DRAFT', NOW(), NOW())
      RETURNING *
    `, [timesheetId, targetResourceId, projectId, weekStarting, weekEndingDate, 0]);

    res.json({
      success: true,
      data: result.rows[0],
      message: 'Timesheet created successfully',
    });
  } catch (error) {
    console.error('Create timesheet error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Users endpoint for dropdowns
app.get('/api/users', authenticateToken, async (req, res) => {
  try {
    const { role } = req.query;
    let query = 'SELECT id, "firstName", "lastName", email, role FROM users WHERE status = \'ACTIVE\'';
    const params = [];

    if (role) {
      query += ' AND role = $1';
      params.push(role);
    }

    query += ' ORDER BY "firstName", "lastName"';

    const result = await pool.query(query, params);

    res.json({
      success: true,
      data: result.rows,
      pagination: {
        page: 1,
        limit: 100,
        total: result.rows.length,
        totalPages: 1,
      },
    });
  } catch (error) {
    console.error('Users error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Admin endpoint to get all resources
app.get('/api/timesheets/all-resources', authenticateToken, async (req, res) => {
  try {
    if (req.user!.role !== 'ADMIN') {
      return res.status(403).json({ success: false, error: 'Admin access required' });
    }

    const result = await pool.query(`
      SELECT r.*, u.id as user_id, u."firstName", u."lastName", u.email
      FROM resources r
      JOIN users u ON r."userId" = u.id
      WHERE u.status = 'ACTIVE'
      ORDER BY u."firstName", u."lastName"
    `);

    const resources = result.rows.map(row => ({
      id: row.id,
      userId: row.user_id,
      user: {
        id: row.user_id,
        firstName: row.firstName,
        lastName: row.lastName,
        email: row.email,
      },
      // Include other resource fields as needed
      status: row.status,
      createdAt: row.createdAt,
      updatedAt: row.updatedAt,
    }));

    res.json({
      success: true,
      data: resources,
    });
  } catch (error) {
    console.error('All resources error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Admin endpoint to get all projects
app.get('/api/timesheets/all-projects', authenticateToken, async (req, res) => {
  try {
    if (req.user!.role !== 'ADMIN') {
      return res.status(403).json({ success: false, error: 'Admin access required' });
    }

    const result = await pool.query(`
      SELECT id, name, description, "startDate", "endDate"
      FROM projects
      ORDER BY name
    `);

    res.json({
      success: true,
      data: result.rows,
    });
  } catch (error) {
    console.error('All projects error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Get user's assigned projects
app.get('/api/timesheets/my-projects', authenticateToken, async (req, res) => {
  try {
    // Get the resource for the current user
    const resourceResult = await pool.query('SELECT id FROM resources WHERE "userId" = $1', [req.user!.id]);

    if (resourceResult.rows.length === 0) {
      return res.status(404).json({ success: false, error: 'Resource not found for user' });
    }

    const resourceId = resourceResult.rows[0].id;

    // Get projects where the user is assigned
    const result = await pool.query(`
      SELECT DISTINCT p.id, p.name, p.description, p."startDate", p."endDate"
      FROM projects p
      JOIN resource_assignments ra ON p.id = ra."projectId"
      WHERE ra."resourceId" = $1
      ORDER BY p.name
    `, [resourceId]);

    res.json({
      success: true,
      data: result.rows,
    });
  } catch (error) {
    console.error('My projects error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Get user's assigned tasks
app.get('/api/timesheets/my-tasks', authenticateToken, async (req, res) => {
  try {
    const { projectId } = req.query;

    // Get the resource for the current user
    const resourceResult = await pool.query('SELECT id FROM resources WHERE "userId" = $1', [req.user!.id]);

    if (resourceResult.rows.length === 0) {
      return res.status(404).json({ success: false, error: 'Resource not found for user' });
    }

    const resourceId = resourceResult.rows[0].id;

    let query = `
      SELECT t.id, t.title, t.description, t."projectId", t.priority, t."estimatedHours", t."actualHours",
             p.id as project_id, p.name as project_name,
             pr."allocationPercent", pr."startDate" as allocation_start, pr."endDate" as allocation_end
      FROM tasks t
      JOIN projects p ON t."projectId" = p.id
      LEFT JOIN project_resources pr ON p.id = pr."projectId" AND pr."resourceId" = $2
      WHERE t."assignedToId" = $1
    `;
    const params = [req.user!.id, resourceId];

    if (projectId) {
      query += ' AND t."projectId" = $3';
      params.push(projectId as string);
    }

    query += ' ORDER BY t."createdAt" DESC';

    const result = await pool.query(query, params);

    const tasks = result.rows.map(row => ({
      id: row.id,
      title: row.title,
      description: row.description,
      projectId: row.projectId,
      priority: row.priority,
      estimatedHours: row.estimatedHours,
      actualHours: row.actualHours || 0,
      project: {
        id: row.project_id,
        name: row.project_name,
      },
      allocation: {
        allocationPercent: row.allocationPercent || 0,
        startDate: row.allocation_start,
        endDate: row.allocation_end,
      },
    }));

    res.json({
      success: true,
      data: tasks,
    });
  } catch (error) {
    console.error('My tasks error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Admin endpoint to get tasks for a specific resource
app.get('/api/timesheets/resource-tasks', authenticateToken, async (req, res) => {
  try {
    if (req.user!.role !== 'ADMIN') {
      return res.status(403).json({ success: false, error: 'Admin access required' });
    }

    const { resourceId, projectId } = req.query;

    if (!resourceId) {
      return res.status(400).json({ success: false, error: 'Resource ID is required' });
    }

    // Get the user ID for the resource
    const resourceUserResult = await pool.query('SELECT "userId" FROM resources WHERE id = $1', [resourceId]);
    if (resourceUserResult.rows.length === 0) {
      return res.status(404).json({ success: false, error: 'Resource not found' });
    }
    const userId = resourceUserResult.rows[0].userId;

    let query = `
      SELECT t.id, t.title, t.description, t."projectId", t.priority, t."estimatedHours", t."actualHours",
             p.id as project_id, p.name as project_name,
             pr."allocationPercent", pr."startDate" as allocation_start, pr."endDate" as allocation_end
      FROM tasks t
      JOIN projects p ON t."projectId" = p.id
      LEFT JOIN project_resources pr ON p.id = pr."projectId" AND pr."resourceId" = $1
      WHERE t."assignedToId" = $2
    `;
    const params = [resourceId as string, userId];

    if (projectId) {
      query += ' AND t."projectId" = $3';
      params.push(projectId as string);
    }

    query += ' ORDER BY t."createdAt" DESC';

    const result = await pool.query(query, params);

    const tasks = result.rows.map(row => ({
      id: row.id,
      title: row.title,
      description: row.description,
      projectId: row.projectId,
      priority: row.priority,
      estimatedHours: row.estimatedHours,
      actualHours: row.actualHours || 0,
      project: {
        id: row.project_id,
        name: row.project_name,
      },
      allocation: {
        allocationPercent: row.allocationPercent || 0,
        startDate: row.allocation_start,
        endDate: row.allocation_end,
      },
    }));

    res.json({
      success: true,
      data: tasks,
    });
  } catch (error) {
    console.error('Resource tasks error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Get timesheet allocation and hour calculation data
app.get('/api/timesheets/:id/allocation-data', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    // Get timesheet with project and resource info
    const timesheetResult = await pool.query(`
      SELECT ts.*, ts."weekStarting", ts."weekEnding",
             p.id as project_id, p.name as project_name,
             r.id as resource_id, r."userId",
             pr."allocationPercent", pr."startDate" as allocation_start, pr."endDate" as allocation_end
      FROM timesheets ts
      JOIN projects p ON ts."projectId" = p.id
      JOIN resources r ON ts."resourceId" = r.id
      LEFT JOIN project_resources pr ON p.id = pr."projectId" AND r.id = pr."resourceId"
      WHERE ts.id = $1
    `, [id]);

    if (timesheetResult.rows.length === 0) {
      return res.status(404).json({ success: false, error: 'Timesheet not found' });
    }

    const timesheet = timesheetResult.rows[0];

    // Check permissions
    if (timesheet.userId !== req.user!.id && req.user!.role !== 'ADMIN' && req.user!.role !== 'PROJECT_MANAGER') {
      return res.status(403).json({ success: false, error: 'Access denied' });
    }

    // Get all tasks for this project assigned to this resource
    const tasksResult = await pool.query(`
      SELECT t.id, t.title, t."estimatedHours", t."actualHours",
             COALESCE(SUM(te.hours), 0) as logged_hours
      FROM tasks t
      LEFT JOIN timesheet_entries te ON t.id = te."taskId"
      WHERE t."projectId" = $1 AND t."assignedToId" = $2
      GROUP BY t.id, t.title, t."estimatedHours", t."actualHours"
      ORDER BY t."createdAt" DESC
    `, [timesheet.project_id, timesheet.userId]);

    // Calculate allocation-based available hours for the week
    const allocationPercent = timesheet.allocationPercent || 0;
    const standardWorkHoursPerWeek = 40; // Standard work week
    const allocatedHoursPerWeek = (standardWorkHoursPerWeek * allocationPercent) / 100;

    // Get existing timesheet entries for this week
    const entriesResult = await pool.query(`
      SELECT te.*, t.title as task_title
      FROM timesheet_entries te
      JOIN tasks t ON te."taskId" = t.id
      WHERE te."timesheetId" = $1
      ORDER BY te.date, t.title
    `, [id]);

    // Calculate daily breakdown
    const weekStart = new Date(timesheet.weekStarting);
    const dailyBreakdown = [];

    for (let i = 0; i < 7; i++) {
      const currentDate = new Date(weekStart);
      currentDate.setDate(weekStart.getDate() + i);
      const dateStr = currentDate.toISOString().split('T')[0];

      const dayEntries = entriesResult.rows.filter(entry =>
        entry.date.toISOString().split('T')[0] === dateStr
      );

      const dailyTotal = dayEntries.reduce((sum, entry) => sum + parseFloat(entry.hours), 0);
      const dailyAllocated = allocatedHoursPerWeek / 7; // Assuming 7-day week for allocation
      const dailyStandard = 8; // Standard 8-hour workday

      dailyBreakdown.push({
        date: dateStr,
        dayName: currentDate.toLocaleDateString('en-US', { weekday: 'short' }),
        allocatedHours: dailyAllocated,
        standardHours: dailyStandard,
        loggedHours: dailyTotal,
        isOvertime: dailyTotal > dailyStandard,
        isOverAllocated: dailyTotal > dailyAllocated,
        entries: dayEntries.map(entry => ({
          taskId: entry.taskId,
          taskTitle: entry.task_title,
          hours: parseFloat(entry.hours),
          description: entry.description,
        })),
      });
    }

    // Calculate totals
    const totalLoggedHours = entriesResult.rows.reduce((sum, entry) => sum + parseFloat(entry.hours), 0);
    const totalEstimatedHours = tasksResult.rows.reduce((sum, task) => sum + (parseFloat(task.estimatedHours) || 0), 0);

    const allocationData = {
      timesheet: {
        id: timesheet.id,
        weekStarting: timesheet.weekStarting,
        weekEnding: timesheet.weekEnding,
        status: timesheet.status,
      },
      project: {
        id: timesheet.project_id,
        name: timesheet.project_name,
      },
      allocation: {
        allocationPercent: allocationPercent,
        allocatedHoursPerWeek: allocatedHoursPerWeek,
        standardHoursPerWeek: standardWorkHoursPerWeek,
        allocationStartDate: timesheet.allocation_start,
        allocationEndDate: timesheet.allocation_end,
      },
      summary: {
        totalLoggedHours: totalLoggedHours,
        totalEstimatedHours: totalEstimatedHours,
        totalAllocatedHours: allocatedHoursPerWeek,
        isOvertime: totalLoggedHours > standardWorkHoursPerWeek,
        isOverAllocated: totalLoggedHours > allocatedHoursPerWeek,
        utilizationPercent: allocatedHoursPerWeek > 0 ? (totalLoggedHours / allocatedHoursPerWeek) * 100 : 0,
      },
      tasks: tasksResult.rows.map(task => ({
        id: task.id,
        title: task.title,
        estimatedHours: parseFloat(task.estimatedHours) || 0,
        actualHours: parseFloat(task.actualHours) || 0,
        loggedHours: parseFloat(task.logged_hours) || 0,
        remainingHours: Math.max(0, (parseFloat(task.estimatedHours) || 0) - (parseFloat(task.logged_hours) || 0)),
        isOverEstimate: (parseFloat(task.logged_hours) || 0) > (parseFloat(task.estimatedHours) || 0),
      })),
      dailyBreakdown: dailyBreakdown,
    };

    res.json({
      success: true,
      data: allocationData,
    });
  } catch (error) {
    console.error('Get allocation data error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Add timesheet entry endpoint
app.post('/api/timesheets/:id/entries', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const { taskId, date, hours, description } = req.body;

    if (!taskId || !date || !hours) {
      return res.status(400).json({ success: false, error: 'Task ID, date, and hours are required' });
    }

    // Get timesheet and check permissions
    const timesheetResult = await pool.query(`
      SELECT ts.*, r."userId"
      FROM timesheets ts
      JOIN resources r ON ts."resourceId" = r.id
      WHERE ts.id = $1
    `, [id]);

    if (timesheetResult.rows.length === 0) {
      return res.status(404).json({ success: false, error: 'Timesheet not found' });
    }

    const timesheet = timesheetResult.rows[0];

    // Check if user owns this timesheet or is admin
    if (timesheet.userId !== req.user!.id && req.user!.role !== 'ADMIN') {
      return res.status(403).json({ success: false, error: 'Access denied' });
    }

    if (timesheet.status !== 'DRAFT') {
      return res.status(400).json({ success: false, error: 'Can only add entries to draft timesheets' });
    }

    // Check if entry already exists for this date and task
    const existingEntryResult = await pool.query(`
      SELECT id FROM timesheet_entries
      WHERE "timesheetId" = $1 AND "taskId" = $2 AND date = $3
    `, [id, taskId, date]);

    if (existingEntryResult.rows.length > 0) {
      // Update existing entry
      const entryId = existingEntryResult.rows[0].id;
      const result = await pool.query(`
        UPDATE timesheet_entries
        SET hours = $1, description = $2, "updatedAt" = NOW()
        WHERE id = $3
        RETURNING *
      `, [parseFloat(hours), description || '', entryId]);

      res.json({
        success: true,
        data: result.rows[0],
        message: 'Timesheet entry updated successfully',
      });
    } else {
      // Create new entry
      const entryId = `entry-${Date.now()}`;
      const result = await pool.query(`
        INSERT INTO timesheet_entries (id, "timesheetId", "taskId", date, hours, description, "createdAt", "updatedAt")
        VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW())
        RETURNING *
      `, [entryId, id, taskId, date, parseFloat(hours), description || '']);

      res.json({
        success: true,
        data: result.rows[0],
        message: 'Timesheet entry created successfully',
      });
    }
  } catch (error) {
    console.error('Add timesheet entry error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Submit timesheet endpoint
app.patch('/api/timesheets/:id/submit', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    // Get timesheet and check permissions
    const timesheetResult = await pool.query(`
      SELECT ts.*, r."userId"
      FROM timesheets ts
      JOIN resources r ON ts."resourceId" = r.id
      WHERE ts.id = $1
    `, [id]);

    if (timesheetResult.rows.length === 0) {
      return res.status(404).json({ success: false, error: 'Timesheet not found' });
    }

    const timesheet = timesheetResult.rows[0];

    // Check if user owns this timesheet or is admin
    if (timesheet.userId !== req.user!.id && req.user!.role !== 'ADMIN') {
      return res.status(403).json({ success: false, error: 'Access denied' });
    }

    if (timesheet.status !== 'DRAFT') {
      return res.status(400).json({ success: false, error: 'Only draft timesheets can be submitted' });
    }

    // Calculate total hours from entries
    const entriesResult = await pool.query(`
      SELECT SUM(hours) as total_hours
      FROM timesheet_entries
      WHERE "timesheetId" = $1
    `, [id]);

    const totalHours = parseFloat(entriesResult.rows[0].total_hours) || 0;

    if (totalHours === 0) {
      return res.status(400).json({ success: false, error: 'Cannot submit timesheet with zero hours' });
    }

    // Update timesheet status
    const result = await pool.query(`
      UPDATE timesheets
      SET status = 'SUBMITTED', "submittedAt" = NOW(), "totalHours" = $1, "updatedAt" = NOW()
      WHERE id = $2
      RETURNING *
    `, [totalHours, id]);

    res.json({
      success: true,
      data: result.rows[0],
      message: 'Timesheet submitted successfully',
    });
  } catch (error) {
    console.error('Submit timesheet error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Approve timesheet endpoint
app.patch('/api/timesheets/:id/approve', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    if (req.user!.role !== 'ADMIN' && req.user!.role !== 'PROJECT_MANAGER') {
      return res.status(403).json({ success: false, error: 'Insufficient permissions' });
    }

    // Get timesheet
    const timesheetResult = await pool.query(`
      SELECT ts.*, p."managerId"
      FROM timesheets ts
      JOIN projects p ON ts."projectId" = p.id
      WHERE ts.id = $1
    `, [id]);

    if (timesheetResult.rows.length === 0) {
      return res.status(404).json({ success: false, error: 'Timesheet not found' });
    }

    const timesheet = timesheetResult.rows[0];

    if (timesheet.status !== 'SUBMITTED') {
      return res.status(400).json({ success: false, error: 'Only submitted timesheets can be approved' });
    }

    // Check if user can approve this timesheet
    if (req.user!.role === 'PROJECT_MANAGER' && timesheet.managerId !== req.user!.id) {
      return res.status(403).json({ success: false, error: 'Access denied' });
    }

    // Update timesheet status
    const result = await pool.query(`
      UPDATE timesheets
      SET status = 'APPROVED', "approvedAt" = NOW(), "approvedBy" = $1, "updatedAt" = NOW()
      WHERE id = $2
      RETURNING *
    `, [req.user!.id, id]);

    res.json({
      success: true,
      data: result.rows[0],
      message: 'Timesheet approved successfully',
    });
  } catch (error) {
    console.error('Approve timesheet error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Reject timesheet endpoint
app.patch('/api/timesheets/:id/reject', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const { reason } = req.body;

    if (!reason) {
      return res.status(400).json({ success: false, error: 'Rejection reason is required' });
    }

    if (req.user!.role !== 'ADMIN' && req.user!.role !== 'PROJECT_MANAGER') {
      return res.status(403).json({ success: false, error: 'Insufficient permissions' });
    }

    // Get timesheet
    const timesheetResult = await pool.query(`
      SELECT ts.*, p."managerId"
      FROM timesheets ts
      JOIN projects p ON ts."projectId" = p.id
      WHERE ts.id = $1
    `, [id]);

    if (timesheetResult.rows.length === 0) {
      return res.status(404).json({ success: false, error: 'Timesheet not found' });
    }

    const timesheet = timesheetResult.rows[0];

    if (timesheet.status !== 'SUBMITTED') {
      return res.status(400).json({ success: false, error: 'Only submitted timesheets can be rejected' });
    }

    // Check if user can reject this timesheet
    if (req.user!.role === 'PROJECT_MANAGER' && timesheet.managerId !== req.user!.id) {
      return res.status(403).json({ success: false, error: 'Access denied' });
    }

    // Update timesheet status
    const result = await pool.query(`
      UPDATE timesheets
      SET status = 'REJECTED', "rejectionReason" = $1, "updatedAt" = NOW()
      WHERE id = $2
      RETURNING *
    `, [reason, id]);

    res.json({
      success: true,
      data: result.rows[0],
      message: 'Timesheet rejected successfully',
    });
  } catch (error) {
    console.error('Reject timesheet error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Project by ID endpoint
app.get('/api/projects/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    const result = await pool.query(`
      SELECT p.*,
             c.title as contract_title, c.value as contract_value,
             u."firstName" as manager_firstName, u."lastName" as manager_lastName, u.email as manager_email,
             cl."firstName" as client_firstName, cl."lastName" as client_lastName, cl.email as client_email
      FROM projects p
      JOIN contracts c ON p."contractId" = c.id
      JOIN users u ON p."managerId" = u.id
      JOIN users cl ON c."clientId" = cl.id
      WHERE p.id = $1
    `, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ success: false, error: 'Project not found' });
    }

    const project = result.rows[0];

    // Get project resources
    const resourcesResult = await pool.query(`
      SELECT pr.*, r.*, u."firstName", u."lastName", u.email
      FROM project_resources pr
      JOIN resources r ON pr."resourceId" = r.id
      JOIN users u ON r."userId" = u.id
      WHERE pr."projectId" = $1
    `, [id]);

    // Get project tasks
    const tasksResult = await pool.query(`
      SELECT t.*, u."firstName", u."lastName"
      FROM tasks t
      LEFT JOIN users u ON t."assignedToId" = u.id
      WHERE t."projectId" = $1
      ORDER BY t."createdAt" DESC
    `, [id]);

    // Structure the response to match frontend expectations
    const projectData = {
      ...project,
      contract: {
        title: project.contract_title,
        value: project.contract_value,
        client: {
          firstName: project.client_firstName,
          lastName: project.client_lastName,
          email: project.client_email,
        },
      },
      manager: {
        firstName: project.manager_firstName,
        lastName: project.manager_lastName,
        email: project.manager_email,
      },
      projectResources: resourcesResult.rows.map(pr => ({
        id: pr.id,
        allocationPercent: pr.allocationPercent,
        resource: {
          user: {
            firstName: pr.firstName,
            lastName: pr.lastName,
            email: pr.email,
          },
        },
      })),
      tasks: tasksResult.rows,
    };

    res.json({
      success: true,
      data: projectData,
    });
  } catch (error) {
    console.error('Project by ID error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Create project endpoint
app.post('/api/projects', authenticateToken, async (req, res) => {
  try {
    const { name, description, contractId, managerId, startDate, endDate, budget } = req.body;
    const projectId = `project-${Date.now()}`;

    const result = await pool.query(`
      INSERT INTO projects (id, name, description, "contractId", "managerId", "startDate", "endDate", budget, status, "createdAt", "updatedAt")
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, 'PLANNING', NOW(), NOW())
      RETURNING *
    `, [projectId, name, description, contractId, managerId, startDate, endDate, budget]);

    res.json({
      success: true,
      data: result.rows[0],
    });
  } catch (error) {
    console.error('Create project error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Update project endpoint
app.put('/api/projects/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, contractId, managerId, startDate, endDate, budget, status } = req.body;

    const result = await pool.query(`
      UPDATE projects
      SET name = $1, description = $2, "contractId" = $3, "managerId" = $4,
          "startDate" = $5, "endDate" = $6, budget = $7, status = $8, "updatedAt" = NOW()
      WHERE id = $9
      RETURNING *
    `, [name, description, contractId, managerId, startDate, endDate, budget, status, id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ success: false, error: 'Project not found' });
    }

    res.json({
      success: true,
      data: result.rows[0],
    });
  } catch (error) {
    console.error('Update project error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Skills endpoint
app.get('/api/skills', async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT * FROM skills
      ORDER BY category, name
    `);

    res.json({
      success: true,
      data: result.rows,
      pagination: {
        page: 1,
        limit: 100,
        total: result.rows.length,
        totalPages: 1,
      },
    });
  } catch (error) {
    console.error('Skills error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Resource Plans endpoint
app.get('/api/resource-plans', authenticateToken, async (req, res) => {
  try {
    const { projectId } = req.query;
    let query = `
      SELECT rp.*, s.name as skill_name, s.category as skill_category,
             p.name as project_name
      FROM resource_plans rp
      JOIN skills s ON rp."skillId" = s.id
      JOIN projects p ON rp."projectId" = p.id
    `;
    const params = [];

    if (projectId) {
      query += ' WHERE rp."projectId" = $1';
      params.push(projectId);
    }

    query += ' ORDER BY rp."createdAt" DESC';

    const result = await pool.query(query, params);

    // Transform the data to match expected structure
    const resourcePlans = result.rows.map(row => ({
      ...row,
      skill: {
        id: row.skillId,
        name: row.skill_name,
        category: row.skill_category,
      },
      project: {
        id: row.projectId,
        name: row.project_name,
      },
      planAllocations: [], // Will be populated separately if needed
      resourceRequests: [], // Will be populated separately if needed
    }));

    res.json({
      success: true,
      data: resourcePlans,
      pagination: {
        page: 1,
        limit: 100,
        total: resourcePlans.length,
        totalPages: 1,
      },
    });
  } catch (error) {
    console.error('Resource plans error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Create Resource Plan endpoint
app.post('/api/resource-plans', authenticateToken, async (req, res) => {
  try {
    const {
      projectId,
      skillId,
      role,
      allocationPercent,
      requiredCount,
      startDate,
      endDate,
      minExperience = 0,
      maxBudget,
      description,
    } = req.body;

    const resourcePlanId = `rp-${Date.now()}`;

    const result = await pool.query(`
      INSERT INTO resource_plans (
        id, "projectId", "skillId", role, "allocationPercent", "requiredCount",
        "startDate", "endDate", "minExperience", "maxBudget", description,
        status, "createdAt", "updatedAt"
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, 'DRAFT', NOW(), NOW())
      RETURNING *
    `, [
      resourcePlanId, projectId, skillId, role, allocationPercent, requiredCount,
      startDate, endDate, minExperience, maxBudget, description
    ]);

    res.json({
      success: true,
      data: result.rows[0],
      message: 'Resource plan created successfully',
    });
  } catch (error) {
    console.error('Create resource plan error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Update Resource Plan endpoint
app.put('/api/resource-plans/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const {
      role,
      allocationPercent,
      requiredCount,
      startDate,
      endDate,
      minExperience,
      maxBudget,
      description,
      status,
    } = req.body;

    // Check if resource plan exists
    const existingPlan = await pool.query('SELECT * FROM resource_plans WHERE id = $1', [id]);
    if (existingPlan.rows.length === 0) {
      return res.status(404).json({ success: false, error: 'Resource plan not found' });
    }

    // Build update query dynamically
    const updateFields = [];
    const params = [];
    let paramIndex = 1;

    if (role !== undefined) {
      updateFields.push(`role = $${paramIndex}`);
      params.push(role);
      paramIndex++;
    }
    if (allocationPercent !== undefined) {
      updateFields.push(`"allocationPercent" = $${paramIndex}`);
      params.push(allocationPercent);
      paramIndex++;
    }
    if (requiredCount !== undefined) {
      updateFields.push(`"requiredCount" = $${paramIndex}`);
      params.push(requiredCount);
      paramIndex++;
    }
    if (startDate !== undefined) {
      updateFields.push(`"startDate" = $${paramIndex}`);
      params.push(startDate);
      paramIndex++;
    }
    if (endDate !== undefined) {
      updateFields.push(`"endDate" = $${paramIndex}`);
      params.push(endDate);
      paramIndex++;
    }
    if (minExperience !== undefined) {
      updateFields.push(`"minExperience" = $${paramIndex}`);
      params.push(minExperience);
      paramIndex++;
    }
    if (maxBudget !== undefined) {
      updateFields.push(`"maxBudget" = $${paramIndex}`);
      params.push(maxBudget);
      paramIndex++;
    }
    if (description !== undefined) {
      updateFields.push(`description = $${paramIndex}`);
      params.push(description);
      paramIndex++;
    }
    if (status !== undefined) {
      updateFields.push(`status = $${paramIndex}`);
      params.push(status);
      paramIndex++;
    }

    updateFields.push(`"updatedAt" = NOW()`);
    params.push(id);

    const query = `
      UPDATE resource_plans
      SET ${updateFields.join(', ')}
      WHERE id = $${paramIndex}
      RETURNING *
    `;

    const result = await pool.query(query, params);

    res.json({
      success: true,
      data: result.rows[0],
      message: 'Resource plan updated successfully',
    });
  } catch (error) {
    console.error('Update resource plan error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Resource Matching endpoint
app.post('/api/resource-plans/:id/match-resources', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const { limit = 10 } = req.body;

    // Get resource plan details
    const planResult = await pool.query(`
      SELECT rp.*, s.name as skill_name, s.category as skill_category
      FROM resource_plans rp
      JOIN skills s ON rp."skillId" = s.id
      WHERE rp.id = $1
    `, [id]);

    if (planResult.rows.length === 0) {
      return res.status(404).json({ success: false, error: 'Resource plan not found' });
    }

    const plan = planResult.rows[0];

    // Find resources with matching skills
    const resourcesResult = await pool.query(`
      SELECT DISTINCT r.*, u."firstName", u."lastName", u.email, u.phone,
             rs."proficiencyLevel", rs."yearsOfExperience", rs.certified
      FROM resources r
      JOIN users u ON r."userId" = u.id
      JOIN resource_skills rs ON r.id = rs."resourceId"
      WHERE r.status = 'AVAILABLE'
        AND rs."skillId" = $1
        AND rs."yearsOfExperience" >= $2
        AND ($3::numeric IS NULL OR r."hourlyRate" <= $3)
      ORDER BY rs."proficiencyLevel" DESC, rs."yearsOfExperience" DESC
      LIMIT $4
    `, [plan.skillId, plan.minExperience, plan.maxBudget, limit]);

    // Calculate match scores for each resource
    const matches = resourcesResult.rows.map(resource => {
      // Skill match (0-100) based on proficiency level (1-5)
      const skillMatch = (resource.proficiencyLevel / 5) * 100;

      // Experience match (0-100)
      const experienceMatch = resource.yearsOfExperience >= plan.minExperience
        ? Math.min(100, 80 + ((resource.yearsOfExperience - plan.minExperience) / plan.minExperience) * 20)
        : (resource.yearsOfExperience / plan.minExperience) * 60;

      // Budget match (0-100)
      const budgetMatch = plan.maxBudget
        ? (resource.hourlyRate <= plan.maxBudget ? 100 - ((resource.hourlyRate / plan.maxBudget) * 20) : 60)
        : 100;

      // Certification bonus
      const certificationBonus = resource.certified ? 10 : 0;

      // Overall match score (weighted average)
      const matchScore = Math.min(100,
        (skillMatch * 0.4) +
        (experienceMatch * 0.3) +
        (budgetMatch * 0.2) +
        (90 * 0.1) + // Availability (assuming 90% for now)
        certificationBonus
      );

      return {
        resourceId: resource.id,
        resource: {
          id: resource.id,
          user: {
            firstName: resource.firstName,
            lastName: resource.lastName,
            email: resource.email,
            phone: resource.phone,
          },
          designation: resource.designation,
          department: resource.department,
          hourlyRate: resource.hourlyRate,
          skills: [{
            skill: { name: plan.skill_name },
            proficiencyLevel: resource.proficiencyLevel,
            yearsOfExperience: resource.yearsOfExperience,
            certified: resource.certified,
          }],
        },
        matchScore: Math.round(matchScore),
        availability: 90, // Simplified for now
        skillMatch: Math.round(skillMatch),
        experienceMatch: Math.round(experienceMatch),
        budgetMatch: Math.round(budgetMatch),
        availabilityDetails: {
          currentAllocations: 10, // Simplified
          availableCapacity: 90,
          conflictingProjects: [],
        },
      };
    });

    // Sort by match score
    matches.sort((a, b) => b.matchScore - a.matchScore);

    res.json({
      success: true,
      data: {
        resourcePlan: {
          id: plan.id,
          role: plan.role,
          skill: { name: plan.skill_name, category: plan.skill_category },
          requirements: {
            allocationPercent: plan.allocationPercent,
            minExperience: plan.minExperience,
            maxBudget: plan.maxBudget,
            startDate: plan.startDate,
            endDate: plan.endDate,
          },
        },
        matches,
        summary: {
          totalMatches: matches.length,
          averageMatchScore: matches.length > 0
            ? Math.round(matches.reduce((sum, m) => sum + m.matchScore, 0) / matches.length)
            : 0,
          perfectMatches: matches.filter(m => m.matchScore >= 90).length,
          goodMatches: matches.filter(m => m.matchScore >= 70 && m.matchScore < 90).length,
        },
      },
    });
  } catch (error) {
    console.error('Resource matching error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Allocate Resource to Plan endpoint
app.post('/api/resource-plans/:id/allocate-resource', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const { resourceId, allocationPercent, startDate, endDate } = req.body;

    // Verify resource plan exists
    const planResult = await pool.query('SELECT * FROM resource_plans WHERE id = $1', [id]);
    if (planResult.rows.length === 0) {
      return res.status(404).json({ success: false, error: 'Resource plan not found' });
    }

    const plan = planResult.rows[0];

    // Verify resource exists
    const resourceResult = await pool.query('SELECT * FROM resources WHERE id = $1', [resourceId]);
    if (resourceResult.rows.length === 0) {
      return res.status(404).json({ success: false, error: 'Resource not found' });
    }

    // Create plan allocation
    const allocationId = `pa-${Date.now()}`;
    const allocation = await pool.query(`
      INSERT INTO plan_allocations (
        id, "resourcePlanId", "resourceId", "allocationPercent",
        "startDate", "endDate", "isConfirmed", "createdAt", "updatedAt"
      )
      VALUES ($1, $2, $3, $4, $5, $6, false, NOW(), NOW())
      RETURNING *
    `, [
      allocationId, id, resourceId,
      allocationPercent || plan.allocationPercent,
      startDate || plan.startDate,
      endDate || plan.endDate
    ]);

    res.json({
      success: true,
      data: allocation.rows[0],
      message: 'Resource allocated to plan successfully',
    });
  } catch (error) {
    console.error('Resource allocation error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Resource Requests endpoints
app.get('/api/resource-requests', authenticateToken, async (req, res) => {
  try {
    const { status, priority } = req.query;
    let query = `
      SELECT rr.*, rp.role, rp."allocationPercent",
             p.name as project_name, s.name as skill_name,
             u1."firstName" as requester_firstName, u1."lastName" as requester_lastName,
             u2."firstName" as assignee_firstName, u2."lastName" as assignee_lastName
      FROM resource_requests rr
      JOIN resource_plans rp ON rr."resourcePlanId" = rp.id
      JOIN projects p ON rp."projectId" = p.id
      JOIN skills s ON rp."skillId" = s.id
      JOIN users u1 ON rr."requestedBy" = u1.id
      LEFT JOIN users u2 ON rr."assignedTo" = u2.id
    `;
    const params = [];
    const conditions = [];

    if (status) {
      conditions.push(`rr.status = $${params.length + 1}`);
      params.push(status);
    }

    if (priority) {
      conditions.push(`rr.priority = $${params.length + 1}`);
      params.push(priority);
    }

    if (conditions.length > 0) {
      query += ' WHERE ' + conditions.join(' AND ');
    }

    query += ' ORDER BY rr.priority DESC, rr."createdAt" DESC';

    const result = await pool.query(query, params);

    const resourceRequests = result.rows.map(row => ({
      ...row,
      resourcePlan: {
        role: row.role,
        allocationPercent: row.allocationPercent,
        project: { name: row.project_name },
        skill: { name: row.skill_name },
      },
      requester: {
        firstName: row.requester_firstName,
        lastName: row.requester_lastName,
      },
      assignee: row.assignee_firstName ? {
        firstName: row.assignee_firstName,
        lastName: row.assignee_lastName,
      } : null,
    }));

    res.json({
      success: true,
      data: resourceRequests,
      pagination: {
        page: 1,
        limit: 100,
        total: resourceRequests.length,
        totalPages: 1,
      },
    });
  } catch (error) {
    console.error('Resource requests error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Update Resource Allocation endpoint
app.put('/api/resource-plans/:id/allocations/:allocationId', authenticateToken, async (req, res) => {
  try {
    const { id, allocationId } = req.params;
    const { allocationPercent, startDate, endDate } = req.body;

    // Check if allocation exists and belongs to the resource plan
    const allocationResult = await pool.query(`
      SELECT pa.*, rp.id as plan_id
      FROM plan_allocations pa
      JOIN resource_plans rp ON pa."resourcePlanId" = rp.id
      WHERE pa.id = $1 AND rp.id = $2
    `, [allocationId, id]);

    if (allocationResult.rows.length === 0) {
      return res.status(404).json({ success: false, error: 'Allocation not found' });
    }

    // Build update query dynamically
    const updateFields = [];
    const params = [];
    let paramIndex = 1;

    if (allocationPercent !== undefined) {
      updateFields.push(`"allocationPercent" = $${paramIndex}`);
      params.push(allocationPercent);
      paramIndex++;
    }
    if (startDate !== undefined) {
      updateFields.push(`"startDate" = $${paramIndex}`);
      params.push(startDate);
      paramIndex++;
    }
    if (endDate !== undefined) {
      updateFields.push(`"endDate" = $${paramIndex}`);
      params.push(endDate);
      paramIndex++;
    }

    updateFields.push(`"updatedAt" = NOW()`);
    params.push(allocationId);

    const query = `
      UPDATE plan_allocations
      SET ${updateFields.join(', ')}
      WHERE id = $${paramIndex}
      RETURNING *
    `;

    const result = await pool.query(query, params);

    res.json({
      success: true,
      data: result.rows[0],
      message: 'Resource allocation updated successfully',
    });
  } catch (error) {
    console.error('Update allocation error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Remove Resource Allocation endpoint (Deallocate)
app.delete('/api/resource-plans/:id/allocations/:allocationId', authenticateToken, async (req, res) => {
  try {
    const { id, allocationId } = req.params;

    // Check if allocation exists and belongs to the resource plan
    const allocationResult = await pool.query(`
      SELECT pa.*, rp.id as plan_id, u."firstName", u."lastName"
      FROM plan_allocations pa
      JOIN resource_plans rp ON pa."resourcePlanId" = rp.id
      JOIN resources r ON pa."resourceId" = r.id
      JOIN users u ON r."userId" = u.id
      WHERE pa.id = $1 AND rp.id = $2
    `, [allocationId, id]);

    if (allocationResult.rows.length === 0) {
      return res.status(404).json({ success: false, error: 'Allocation not found' });
    }

    // Delete the allocation
    await pool.query('DELETE FROM plan_allocations WHERE id = $1', [allocationId]);

    res.json({
      success: true,
      message: 'Resource allocation removed successfully',
    });
  } catch (error) {
    console.error('Remove allocation error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Global Search endpoint
app.get('/api/search', authenticateToken, async (req, res) => {
  try {
    const { q: searchTerm, limit = 5 } = req.query;

    if (!searchTerm) {
      return res.status(400).json({ success: false, error: 'Search term is required' });
    }

    const searchPattern = `%${searchTerm}%`;
    const searchLimit = parseInt(limit as string);

    // Search across multiple entities
    const [projects, tasks, resources, vendors] = await Promise.all([
      // Projects
      pool.query(`
        SELECT 'project' as type, p.id, p.name as title, p.description,
               c.title as subtitle, p.status
        FROM projects p
        JOIN contracts c ON p."contractId" = c.id
        WHERE p.name ILIKE $1 OR p.description ILIKE $1 OR c.title ILIKE $1
        ORDER BY p."createdAt" DESC
        LIMIT $2
      `, [searchPattern, searchLimit]),

      // Tasks
      pool.query(`
        SELECT 'task' as type, t.id, t.title, t.description,
               p.name as subtitle, t.status
        FROM tasks t
        JOIN projects p ON t."projectId" = p.id
        WHERE t.title ILIKE $1 OR t.description ILIKE $1
        ORDER BY t."createdAt" DESC
        LIMIT $2
      `, [searchPattern, searchLimit]),

      // Resources
      pool.query(`
        SELECT 'resource' as type, r.id,
               CONCAT(u."firstName", ' ', u."lastName") as title,
               r.designation as description,
               u.email as subtitle, r.status
        FROM resources r
        JOIN users u ON r."userId" = u.id
        WHERE u."firstName" ILIKE $1 OR u."lastName" ILIKE $1
           OR u.email ILIKE $1 OR r.designation ILIKE $1
        ORDER BY r."createdAt" DESC
        LIMIT $2
      `, [searchPattern, searchLimit]),

      // Vendors
      pool.query(`
        SELECT 'vendor' as type, v.id, v.name as title,
               v."contactPerson" as description, v.email as subtitle,
               COALESCE(v.status, 'ACTIVE') as status
        FROM vendors v
        WHERE v.name ILIKE $1 OR v.email ILIKE $1 OR v."contactPerson" ILIKE $1
        ORDER BY v."createdAt" DESC
        LIMIT $2
      `, [searchPattern, searchLimit])
    ]);

    const results = {
      projects: projects.rows,
      tasks: tasks.rows,
      resources: resources.rows,
      vendors: vendors.rows,
      total: projects.rows.length + tasks.rows.length + resources.rows.length + vendors.rows.length
    };

    res.json({
      success: true,
      data: results,
      query: searchTerm,
    });
  } catch (error) {
    console.error('Global search error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Create Resource endpoint
app.post('/api/resources', authenticateToken, async (req, res) => {
  try {
    if (req.user!.role !== 'ADMIN' && req.user!.role !== 'HR_MANAGER') {
      return res.status(403).json({ success: false, error: 'Insufficient permissions' });
    }

    const {
      // Personal Information
      firstName,
      lastName,
      email,
      phone,
      dateOfBirth,
      gender,
      maritalStatus,
      nationality,
      emergencyContact,

      // Professional Information
      designation,
      department,
      employmentType,
      employeeId,
      hourlyRate,
      salary,
      location,
      workLocation,
      joiningDate,
      confirmationDate,
      totalExperience,
      previousCompany,
      reportingManager,
      vendorId,
      status = 'AVAILABLE',

      // Identity & Documents
      panNumber,
      aadharNumber,
      passportNumber,
      drivingLicense,
      documentsVerified,

      // Banking Information
      bankDetails,

      // Security & Clearance
      backgroundCheck,
      backgroundCheckDate,
      securityClearance,
      clearanceExpiry,
      entryPass,

      // Related data
      educations,
      certifications,
      languages,
      skills
    } = req.body;

    if (!firstName || !lastName || !email || !designation || !department) {
      return res.status(400).json({
        success: false,
        error: 'First name, last name, email, designation, and department are required'
      });
    }

    // Check if user already exists
    const existingUser = await pool.query('SELECT id FROM users WHERE email = $1', [email]);
    let userId;

    if (existingUser.rows.length > 0) {
      userId = existingUser.rows[0].id;
    } else {
      // Create new user with default password
      const bcrypt = require('bcrypt');
      const hashedPassword = await bcrypt.hash('defaultPassword123', 10);
      const userResult = await pool.query(`
        INSERT INTO users (id, "firstName", "lastName", email, phone, password, role, status, "createdAt", "updatedAt")
        VALUES ($1, $2, $3, $4, $5, $6, 'RESOURCE', 'ACTIVE', NOW(), NOW())
        RETURNING id
      `, [`user-${Date.now()}`, firstName, lastName, email, phone, hashedPassword]);
      userId = userResult.rows[0].id;
    }

    // Create resource
    const resourceId = `resource-${Date.now()}`;
    const generatedEmployeeId = employeeId || `EMP${Date.now()}`;
    const result = await pool.query(`
      INSERT INTO resources (
        id, "userId", designation, department, "employmentType", "employeeId",
        "hourlyRate", salary, location, "workLocation", "joiningDate", "confirmationDate",
        "totalExperience", "previousCompany", "reportingManager", "vendorId", status,
        "dateOfBirth", gender, "maritalStatus", nationality, "emergencyContact",
        "panNumber", "aadharNumber", "passportNumber", "drivingLicense", "documentsVerified",
        "bankDetails", "backgroundCheck", "backgroundCheckDate", "securityClearance",
        "clearanceExpiry", "entryPass", "createdAt", "updatedAt"
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23, $24, $25, $26, $27, $28, $29, $30, $31, $32, $33, NOW(), NOW())
      RETURNING *
    `, [
      resourceId, userId, designation, department, employmentType || 'FULL_TIME',
      generatedEmployeeId, hourlyRate || 0, salary, location || 'Remote', workLocation,
      joiningDate ? new Date(joiningDate).toISOString() : new Date().toISOString(),
      confirmationDate ? new Date(confirmationDate).toISOString() : null,
      totalExperience, previousCompany, reportingManager, vendorId, status,
      dateOfBirth ? new Date(dateOfBirth).toISOString() : null, gender, maritalStatus, nationality, emergencyContact,
      panNumber, aadharNumber, passportNumber, drivingLicense, documentsVerified || false,
      bankDetails, backgroundCheck || false,
      backgroundCheckDate ? new Date(backgroundCheckDate).toISOString() : null,
      securityClearance, clearanceExpiry ? new Date(clearanceExpiry).toISOString() : null, entryPass
    ]);

    // Add educations if provided
    if (educations && educations.length > 0) {
      for (const education of educations) {
        await pool.query(`
          INSERT INTO educations (id, "resourceId", degree, "fieldOfStudy", institution, university, "startYear", "endYear", percentage, cgpa, "createdAt", "updatedAt")
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, NOW(), NOW())
        `, [
          `edu-${Date.now()}-${Math.random()}`, resourceId, education.degree, education.fieldOfStudy,
          education.institution, education.university, education.startYear, education.endYear,
          education.percentage, education.cgpa
        ]);
      }
    }

    // Add certifications if provided
    if (certifications && certifications.length > 0) {
      for (const cert of certifications) {
        await pool.query(`
          INSERT INTO certifications (id, "resourceId", name, "issuingOrg", "issueDate", "expiryDate", "credentialId", "credentialUrl", verified, "createdAt", "updatedAt")
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, NOW(), NOW())
        `, [
          `cert-${Date.now()}-${Math.random()}`, resourceId, cert.name, cert.issuingOrg,
          cert.issueDate ? new Date(cert.issueDate).toISOString() : null,
          cert.expiryDate ? new Date(cert.expiryDate).toISOString() : null,
          cert.credentialId, cert.credentialUrl, cert.verified || false
        ]);
      }
    }

    // Add languages if provided
    if (languages && languages.length > 0) {
      for (const lang of languages) {
        await pool.query(`
          INSERT INTO language_proficiencies (id, "resourceId", language, speaking, reading, writing, "createdAt", "updatedAt")
          VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW())
        `, [
          `lang-${Date.now()}-${Math.random()}`, resourceId, lang.language, lang.speaking, lang.reading, lang.writing
        ]);
      }
    }

    // Add skills if provided
    if (skills && skills.length > 0) {
      for (const skill of skills) {
        await pool.query(`
          INSERT INTO resource_skills (id, "resourceId", "skillId", "proficiencyLevel", "yearsOfExperience", certified, "createdAt", "updatedAt")
          VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW())
        `, [
          `rs-${Date.now()}-${Math.random()}`, resourceId, skill.skillId,
          skill.proficiencyLevel || 1, skill.yearsOfExperience || 0, skill.certified || false
        ]);
      }
    }

    res.json({
      success: true,
      data: result.rows[0],
      message: 'Resource created successfully',
    });
  } catch (error) {
    console.error('Create resource error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Get Resource by ID endpoint
app.get('/api/resources/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    const result = await pool.query(`
      SELECT r.*, u."firstName", u."lastName", u.email, u.phone,
             v.name as vendor_name, v."contactPerson" as vendor_contact
      FROM resources r
      JOIN users u ON r."userId" = u.id
      LEFT JOIN vendors v ON r."vendorId" = v.id
      WHERE r.id = $1
    `, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ success: false, error: 'Resource not found' });
    }

    // Get educations
    const educationsResult = await pool.query(`
      SELECT * FROM educations WHERE "resourceId" = $1 ORDER BY "startYear" DESC
    `, [id]);

    // Get certifications
    const certificationsResult = await pool.query(`
      SELECT * FROM certifications WHERE "resourceId" = $1 ORDER BY "issueDate" DESC
    `, [id]);

    // Get languages
    const languagesResult = await pool.query(`
      SELECT * FROM language_proficiencies WHERE "resourceId" = $1
    `, [id]);

    // Get skills
    const skillsResult = await pool.query(`
      SELECT rs.*, s.name as skill_name, s.category as skill_category
      FROM resource_skills rs
      JOIN skills s ON rs."skillId" = s.id
      WHERE rs."resourceId" = $1
    `, [id]);

    const resource = {
      ...result.rows[0],
      user: {
        firstName: result.rows[0].firstName,
        lastName: result.rows[0].lastName,
        email: result.rows[0].email,
        phone: result.rows[0].phone,
      },
      vendor: result.rows[0].vendor_name ? {
        name: result.rows[0].vendor_name,
        contactPerson: result.rows[0].vendor_contact,
      } : null,
      educations: educationsResult.rows,
      certifications: certificationsResult.rows,
      languages: languagesResult.rows,
      skills: skillsResult.rows.map(skill => ({
        id: skill.id,
        skillId: skill.skillId,
        name: skill.skill_name,
        category: skill.skill_category,
        proficiencyLevel: skill.proficiencyLevel,
        yearsOfExperience: skill.yearsOfExperience,
        certified: skill.certified,
      })),
    };

    res.json({
      success: true,
      data: resource,
    });
  } catch (error) {
    console.error('Get resource error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Update Resource endpoint
app.put('/api/resources/:id', authenticateToken, async (req, res) => {
  try {
    if (req.user!.role !== 'ADMIN' && req.user!.role !== 'HR_MANAGER') {
      return res.status(403).json({ success: false, error: 'Insufficient permissions' });
    }

    const { id } = req.params;
    const {
      firstName,
      lastName,
      email,
      phone,
      designation,
      department,
      employmentType,
      employeeId,
      hourlyRate,
      location,
      skills,
      vendorId,
      status
    } = req.body;

    // Check if resource exists
    const existingResource = await pool.query('SELECT "userId" FROM resources WHERE id = $1', [id]);
    if (existingResource.rows.length === 0) {
      return res.status(404).json({ success: false, error: 'Resource not found' });
    }

    const userId = existingResource.rows[0].userId;

    // Update user info
    if (firstName || lastName || email || phone) {
      const userUpdateFields = [];
      const userParams = [];
      let userParamCount = 0;

      if (firstName) {
        userParamCount++;
        userUpdateFields.push(`"firstName" = $${userParamCount}`);
        userParams.push(firstName);
      }
      if (lastName) {
        userParamCount++;
        userUpdateFields.push(`"lastName" = $${userParamCount}`);
        userParams.push(lastName);
      }
      if (email) {
        userParamCount++;
        userUpdateFields.push(`email = $${userParamCount}`);
        userParams.push(email);
      }
      if (phone) {
        userParamCount++;
        userUpdateFields.push(`phone = $${userParamCount}`);
        userParams.push(phone);
      }

      userUpdateFields.push(`"updatedAt" = NOW()`);
      userParams.push(userId);

      const userQuery = `
        UPDATE users
        SET ${userUpdateFields.join(', ')}
        WHERE id = $${userParamCount + 1}
      `;

      await pool.query(userQuery, userParams);
    }

    // Update resource info
    const resourceUpdateFields = [];
    const resourceParams = [];
    let resourceParamCount = 0;

    if (designation) {
      resourceParamCount++;
      resourceUpdateFields.push(`designation = $${resourceParamCount}`);
      resourceParams.push(designation);
    }
    if (department) {
      resourceParamCount++;
      resourceUpdateFields.push(`department = $${resourceParamCount}`);
      resourceParams.push(department);
    }
    if (employmentType) {
      resourceParamCount++;
      resourceUpdateFields.push(`"employmentType" = $${resourceParamCount}`);
      resourceParams.push(employmentType);
    }
    if (employeeId) {
      resourceParamCount++;
      resourceUpdateFields.push(`"employeeId" = $${resourceParamCount}`);
      resourceParams.push(employeeId);
    }
    if (hourlyRate !== undefined) {
      resourceParamCount++;
      resourceUpdateFields.push(`"hourlyRate" = $${resourceParamCount}`);
      resourceParams.push(hourlyRate);
    }
    if (location) {
      resourceParamCount++;
      resourceUpdateFields.push(`location = $${resourceParamCount}`);
      resourceParams.push(location);
    }
    if (vendorId !== undefined) {
      resourceParamCount++;
      resourceUpdateFields.push(`"vendorId" = $${resourceParamCount}`);
      resourceParams.push(vendorId);
    }
    if (status) {
      resourceParamCount++;
      resourceUpdateFields.push(`status = $${resourceParamCount}`);
      resourceParams.push(status);
    }

    if (resourceUpdateFields.length > 0) {
      resourceUpdateFields.push(`"updatedAt" = NOW()`);
      resourceParams.push(id);

      const resourceQuery = `
        UPDATE resources
        SET ${resourceUpdateFields.join(', ')}
        WHERE id = $${resourceParamCount + 1}
        RETURNING *
      `;

      const result = await pool.query(resourceQuery, resourceParams);

      res.json({
        success: true,
        data: result.rows[0],
        message: 'Resource updated successfully',
      });
    } else {
      res.json({
        success: true,
        message: 'No changes to update',
      });
    }
  } catch (error) {
    console.error('Update resource error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Create Invoice endpoint
app.post('/api/invoices', authenticateToken, async (req, res) => {
  try {
    if (req.user!.role !== 'ADMIN' && req.user!.role !== 'BILLING_MANAGER') {
      return res.status(403).json({ success: false, error: 'Insufficient permissions' });
    }

    const {
      projectId,
      resourceId,
      timesheetId,
      invoiceType = 'MONTHLY',
      billingPeriodStart,
      billingPeriodEnd,
      hourlyRate,
      totalHours,
      subtotal,
      taxRate = 0,
      tax,
      penalties = 0,
      penaltyReason,
      arrears = 0,
      arrearsReason,
      total,
      dueDate,
      notes,
      currency = 'USD'
    } = req.body;

    if (!projectId || !resourceId || !billingPeriodStart || !billingPeriodEnd || !totalHours || !hourlyRate) {
      return res.status(400).json({
        success: false,
        error: 'Project, resource, billing period, hours, and hourly rate are required'
      });
    }

    // Generate invoice number
    const invoiceCount = await pool.query('SELECT COUNT(*) FROM invoices');
    const count = parseInt(invoiceCount.rows[0].count) + 1;
    const invoiceNumber = `INV-${new Date().getFullYear()}-${String(count).padStart(4, '0')}`;

    // Calculate amounts if not provided
    const calculatedSubtotal = subtotal || (totalHours * hourlyRate);
    const calculatedTax = tax || (calculatedSubtotal * (taxRate / 100));
    const calculatedTotal = total || (calculatedSubtotal + calculatedTax + penalties + arrears);

    // Create invoice
    const invoiceId = `invoice-${Date.now()}`;
    const result = await pool.query(`
      INSERT INTO invoices (
        id, "invoiceNumber", "projectId", "resourceId", "timesheetId", "invoiceType",
        "billingPeriodStart", "billingPeriodEnd", "hourlyRate", "totalHours",
        subtotal, "taxRate", tax, penalties, "penaltyReason", arrears, "arrearsReason",
        total, "dueDate", notes, currency, status, "issueDate", "createdAt", "updatedAt"
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, 'DRAFT', NOW(), NOW(), NOW())
      RETURNING *
    `, [
      invoiceId, invoiceNumber, projectId, resourceId, timesheetId, invoiceType,
      new Date(billingPeriodStart).toISOString(), new Date(billingPeriodEnd).toISOString(),
      hourlyRate, totalHours, calculatedSubtotal, taxRate, calculatedTax,
      penalties, penaltyReason, arrears, arrearsReason, calculatedTotal,
      dueDate ? new Date(dueDate).toISOString() : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
      notes, currency
    ]);

    res.json({
      success: true,
      data: result.rows[0],
      message: 'Invoice created successfully',
    });
  } catch (error) {
    console.error('Create invoice error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Update Invoice endpoint
app.put('/api/invoices/:id', authenticateToken, async (req, res) => {
  try {
    if (req.user!.role !== 'ADMIN' && req.user!.role !== 'BILLING_MANAGER') {
      return res.status(403).json({ success: false, error: 'Insufficient permissions' });
    }

    const { id } = req.params;
    const {
      projectId,
      resourceId,
      timesheetId,
      invoiceType,
      billingPeriodStart,
      billingPeriodEnd,
      hourlyRate,
      totalHours,
      subtotal,
      taxRate,
      tax,
      penalties,
      penaltyReason,
      arrears,
      arrearsReason,
      total,
      dueDate,
      notes,
      currency,
      status
    } = req.body;

    // Check if invoice exists
    const existingInvoice = await pool.query('SELECT * FROM invoices WHERE id = $1', [id]);
    if (existingInvoice.rows.length === 0) {
      return res.status(404).json({ success: false, error: 'Invoice not found' });
    }

    // Build update query dynamically
    const updateFields = [];
    const updateParams = [];
    let paramCount = 0;

    if (projectId !== undefined) {
      paramCount++;
      updateFields.push(`"projectId" = $${paramCount}`);
      updateParams.push(projectId);
    }
    if (resourceId !== undefined) {
      paramCount++;
      updateFields.push(`"resourceId" = $${paramCount}`);
      updateParams.push(resourceId);
    }
    if (timesheetId !== undefined) {
      paramCount++;
      updateFields.push(`"timesheetId" = $${paramCount}`);
      updateParams.push(timesheetId);
    }
    if (invoiceType !== undefined) {
      paramCount++;
      updateFields.push(`"invoiceType" = $${paramCount}`);
      updateParams.push(invoiceType);
    }
    if (billingPeriodStart !== undefined) {
      paramCount++;
      updateFields.push(`"billingPeriodStart" = $${paramCount}`);
      updateParams.push(new Date(billingPeriodStart).toISOString());
    }
    if (billingPeriodEnd !== undefined) {
      paramCount++;
      updateFields.push(`"billingPeriodEnd" = $${paramCount}`);
      updateParams.push(new Date(billingPeriodEnd).toISOString());
    }
    if (hourlyRate !== undefined) {
      paramCount++;
      updateFields.push(`"hourlyRate" = $${paramCount}`);
      updateParams.push(hourlyRate);
    }
    if (totalHours !== undefined) {
      paramCount++;
      updateFields.push(`"totalHours" = $${paramCount}`);
      updateParams.push(totalHours);
    }
    if (subtotal !== undefined) {
      paramCount++;
      updateFields.push(`subtotal = $${paramCount}`);
      updateParams.push(subtotal);
    }
    if (taxRate !== undefined) {
      paramCount++;
      updateFields.push(`"taxRate" = $${paramCount}`);
      updateParams.push(taxRate);
    }
    if (tax !== undefined) {
      paramCount++;
      updateFields.push(`tax = $${paramCount}`);
      updateParams.push(tax);
    }
    if (penalties !== undefined) {
      paramCount++;
      updateFields.push(`penalties = $${paramCount}`);
      updateParams.push(penalties);
    }
    if (penaltyReason !== undefined) {
      paramCount++;
      updateFields.push(`"penaltyReason" = $${paramCount}`);
      updateParams.push(penaltyReason);
    }
    if (arrears !== undefined) {
      paramCount++;
      updateFields.push(`arrears = $${paramCount}`);
      updateParams.push(arrears);
    }
    if (arrearsReason !== undefined) {
      paramCount++;
      updateFields.push(`"arrearsReason" = $${paramCount}`);
      updateParams.push(arrearsReason);
    }
    if (total !== undefined) {
      paramCount++;
      updateFields.push(`total = $${paramCount}`);
      updateParams.push(total);
    }
    if (dueDate !== undefined) {
      paramCount++;
      updateFields.push(`"dueDate" = $${paramCount}`);
      updateParams.push(new Date(dueDate).toISOString());
    }
    if (notes !== undefined) {
      paramCount++;
      updateFields.push(`notes = $${paramCount}`);
      updateParams.push(notes);
    }
    if (currency !== undefined) {
      paramCount++;
      updateFields.push(`currency = $${paramCount}`);
      updateParams.push(currency);
    }
    if (status !== undefined) {
      paramCount++;
      updateFields.push(`status = $${paramCount}`);
      updateParams.push(status);
    }

    if (updateFields.length === 0) {
      return res.json({ success: true, message: 'No changes to update' });
    }

    updateFields.push(`"updatedAt" = NOW()`);
    updateParams.push(id);

    const updateQuery = `
      UPDATE invoices
      SET ${updateFields.join(', ')}
      WHERE id = $${paramCount + 1}
      RETURNING *
    `;

    const result = await pool.query(updateQuery, updateParams);

    res.json({
      success: true,
      data: result.rows[0],
      message: 'Invoice updated successfully',
    });
  } catch (error) {
    console.error('Update invoice error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Create Resource Request endpoint
app.post('/api/resource-requests', authenticateToken, async (req: any, res) => {
  try {
    const {
      resourcePlanId,
      title,
      description,
      jobDescription,
      requiredSkills,
      minExperience = 0,
      maxBudget,
      priority = 'MEDIUM',
      expectedDate,
    } = req.body;

    // Verify resource plan exists
    const planResult = await pool.query(`
      SELECT rp.*, p.name as project_name, s.name as skill_name
      FROM resource_plans rp
      JOIN projects p ON rp."projectId" = p.id
      JOIN skills s ON rp."skillId" = s.id
      WHERE rp.id = $1
    `, [resourcePlanId]);

    if (planResult.rows.length === 0) {
      return res.status(404).json({ success: false, error: 'Resource plan not found' });
    }

    const plan = planResult.rows[0];
    const requestId = `rr-${Date.now()}`;

    const result = await pool.query(`
      INSERT INTO resource_requests (
        id, "resourcePlanId", title, description, "jobDescription",
        "requiredSkills", "minExperience", "maxBudget", priority,
        "requestedBy", "expectedDate", status, "createdAt", "updatedAt"
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, 'OPEN', NOW(), NOW())
      RETURNING *
    `, [
      requestId, resourcePlanId, title, description, jobDescription,
      JSON.stringify(requiredSkills), minExperience, maxBudget, priority,
      req.user!.id, expectedDate
    ]);

    res.json({
      success: true,
      data: {
        ...result.rows[0],
        resourcePlan: {
          role: plan.role,
          project: { name: plan.project_name },
          skill: { name: plan.skill_name },
        },
      },
      message: 'Resource request created successfully',
    });
  } catch (error) {
    console.error('Create resource request error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Update Resource Request endpoint
app.put('/api/resource-requests/:id', authenticateToken, async (req: any, res) => {
  try {
    const { id } = req.params;
    const { status, assignedTo, rejectionReason } = req.body;

    let updateFields = [];
    let params = [];
    let paramIndex = 1;

    if (status) {
      updateFields.push(`status = $${paramIndex}`);
      params.push(status);
      paramIndex++;
    }

    if (assignedTo) {
      updateFields.push(`"assignedTo" = $${paramIndex}`);
      params.push(assignedTo);
      paramIndex++;

      if (status !== 'FULFILLED') {
        updateFields.push(`status = $${paramIndex}`);
        params.push('IN_PROGRESS');
        paramIndex++;
      }
    }

    if (rejectionReason) {
      updateFields.push(`"rejectionReason" = $${paramIndex}`);
      params.push(rejectionReason);
      paramIndex++;
    }

    if (status === 'FULFILLED') {
      updateFields.push(`"fulfilledDate" = NOW()`);
    }

    updateFields.push(`"updatedAt" = NOW()`);
    params.push(id);

    const query = `
      UPDATE resource_requests
      SET ${updateFields.join(', ')}
      WHERE id = $${paramIndex}
      RETURNING *
    `;

    const result = await pool.query(query, params);

    if (result.rows.length === 0) {
      return res.status(404).json({ success: false, error: 'Resource request not found' });
    }

    res.json({
      success: true,
      data: result.rows[0],
      message: 'Resource request updated successfully',
    });
  } catch (error) {
    console.error('Update resource request error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Comprehensive HRMS Endpoints

// Workforce Management - Get comprehensive workforce details
app.get('/api/workforce', authenticateToken, async (req: any, res) => {
  try {
    const { department, location, status, employmentType } = req.query;

    let query = `
      SELECT r.*, u."firstName", u."lastName", u.email, u.phone,
             v.name as vendor_name, v."contactPerson" as vendor_contact
      FROM resources r
      JOIN users u ON r."userId" = u.id
      LEFT JOIN vendors v ON r."vendorId" = v.id
    `;
    const params = [];
    const conditions = [];

    if (department) {
      conditions.push(`r.department ILIKE $${params.length + 1}`);
      params.push(`%${department}%`);
    }
    if (location) {
      conditions.push(`r.location ILIKE $${params.length + 1}`);
      params.push(`%${location}%`);
    }
    if (status) {
      conditions.push(`r.status = $${params.length + 1}`);
      params.push(status);
    }
    if (employmentType) {
      conditions.push(`r."employmentType" = $${params.length + 1}`);
      params.push(employmentType);
    }

    if (conditions.length > 0) {
      query += ' WHERE ' + conditions.join(' AND ');
    }

    query += ' ORDER BY r."createdAt" DESC';

    const result = await pool.query(query, params);

    res.json({
      success: true,
      data: result.rows,
      pagination: {
        page: 1,
        limit: 100,
        total: result.rows.length,
        totalPages: 1,
      },
    });
  } catch (error) {
    console.error('Workforce error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Workforce 360 View - Get comprehensive resource details
app.get('/api/workforce/:id', authenticateToken, async (req: any, res) => {
  try {
    const { id } = req.params;

    // Get basic resource info
    const resourceResult = await pool.query(`
      SELECT r.*, u."firstName", u."lastName", u.email, u.phone,
             v.name as vendor_name, v."contactPerson" as vendor_contact
      FROM resources r
      JOIN users u ON r."userId" = u.id
      LEFT JOIN vendors v ON r."vendorId" = v.id
      WHERE r.id = $1
    `, [id]);

    if (resourceResult.rows.length === 0) {
      return res.status(404).json({ success: false, error: 'Resource not found' });
    }

    const resource = resourceResult.rows[0];

    // Get skills
    const skillsResult = await pool.query(`
      SELECT rs.*, s.name as skill_name, s.category
      FROM resource_skills rs
      JOIN skills s ON rs."skillId" = s.id
      WHERE rs."resourceId" = $1
      ORDER BY rs."proficiencyLevel" DESC
    `, [id]);

    // Get current project allocations
    const projectsResult = await pool.query(`
      SELECT pr.*, p.name as project_name, p.status as project_status,
             p."startDate", p."endDate"
      FROM project_resources pr
      JOIN projects p ON pr."projectId" = p.id
      WHERE pr."resourceId" = $1
      ORDER BY pr."startDate" DESC
    `, [id]);

    // Get recent timesheets
    const timesheetsResult = await pool.query(`
      SELECT t.*, p.name as project_name
      FROM timesheets t
      JOIN projects p ON t."projectId" = p.id
      WHERE t."resourceId" = $1
      ORDER BY t."weekStarting" DESC
      LIMIT 10
    `, [id]);

    // Calculate utilization metrics
    const currentAllocations = projectsResult.rows
      .filter(pr => !pr.endDate || new Date(pr.endDate) >= new Date())
      .reduce((sum, pr) => sum + pr.allocationPercent, 0);

    const utilizationMetrics = {
      currentAllocation: Math.min(currentAllocations, 100),
      availableCapacity: Math.max(0, 100 - currentAllocations),
      activeProjects: projectsResult.rows.filter(pr =>
        !pr.endDate || new Date(pr.endDate) >= new Date()
      ).length,
      totalTimesheets: timesheetsResult.rows.length,
    };

    res.json({
      success: true,
      data: {
        ...resource,
        skills: skillsResult.rows,
        projects: projectsResult.rows,
        recentTimesheets: timesheetsResult.rows,
        utilizationMetrics,
      },
    });
  } catch (error) {
    console.error('Workforce 360 view error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Enhanced Vendor Management
app.get('/api/vendors/comprehensive', authenticateToken, async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT v.*, COUNT(r.id) as total_resources
      FROM vendors v
      LEFT JOIN resources r ON v.id = r."vendorId"
      GROUP BY v.id
      ORDER BY v."createdAt" DESC
    `);

    res.json({
      success: true,
      data: result.rows,
      pagination: {
        page: 1,
        limit: 100,
        total: result.rows.length,
        totalPages: 1,
      },
    });
  } catch (error) {
    console.error('Comprehensive vendors error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Bulk Hiring Management

// Get all bulk hiring jobs
app.get('/api/bulk-hiring/jobs', authenticateToken, async (req: any, res) => {
  try {
    const { status, jobType, hiringType, department } = req.query;

    let query = `
      SELECT bhj.*, u."firstName" as creator_firstName, u."lastName" as creator_lastName,
             COUNT(ja.id) as total_applications
      FROM bulk_hiring_jobs bhj
      JOIN users u ON bhj."createdBy" = u.id
      LEFT JOIN job_applications ja ON bhj.id = ja."jobId"
    `;
    const params = [];
    const conditions = [];

    if (status) {
      conditions.push(`bhj.status = $${params.length + 1}`);
      params.push(status);
    }
    if (jobType) {
      conditions.push(`bhj."jobType" = $${params.length + 1}`);
      params.push(jobType);
    }
    if (hiringType) {
      conditions.push(`bhj."hiringType" = $${params.length + 1}`);
      params.push(hiringType);
    }
    if (department) {
      conditions.push(`bhj.department ILIKE $${params.length + 1}`);
      params.push(`%${department}%`);
    }

    if (conditions.length > 0) {
      query += ' WHERE ' + conditions.join(' AND ');
    }

    query += ' GROUP BY bhj.id, u."firstName", u."lastName" ORDER BY bhj."createdAt" DESC';

    const result = await pool.query(query, params);

    res.json({
      success: true,
      data: result.rows,
      pagination: {
        page: 1,
        limit: 100,
        total: result.rows.length,
        totalPages: 1,
      },
    });
  } catch (error) {
    console.error('Bulk hiring jobs error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Create bulk hiring job
app.post('/api/bulk-hiring/jobs', authenticateToken, async (req: any, res) => {
  try {
    const {
      title,
      description,
      jobType,
      hiringType,
      department,
      location,
      positions,
      employmentType,
      minExperience = 0,
      maxExperience,
      requiredSkills,
      preferredSkills,
      minSalary,
      maxSalary,
      benefits,
      workShift,
      workMode,
      applicationDeadline,
      expectedJoiningDate,
      sourceVendor = false,
      directHiring = true,
      priority = 'MEDIUM',
    } = req.body;

    const jobId = `bhj-${Date.now()}`;

    const result = await pool.query(`
      INSERT INTO bulk_hiring_jobs (
        id, title, description, "jobType", "hiringType", department, location,
        positions, "employmentType", "minExperience", "maxExperience",
        "requiredSkills", "preferredSkills", "minSalary", "maxSalary",
        benefits, "workShift", "workMode", "applicationDeadline", "expectedJoiningDate",
        "sourceVendor", "directHiring", priority, "createdBy", "createdAt", "updatedAt"
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23, $24, NOW(), NOW())
      RETURNING *
    `, [
      jobId, title, description, jobType, hiringType, department, location,
      positions, employmentType, minExperience, maxExperience,
      JSON.stringify(requiredSkills), JSON.stringify(preferredSkills || []),
      minSalary, maxSalary, JSON.stringify(benefits || []),
      workShift, workMode, applicationDeadline, expectedJoiningDate,
      sourceVendor, directHiring, priority, req.user!.id
    ]);

    res.status(201).json({
      success: true,
      data: result.rows[0],
      message: 'Bulk hiring job created successfully',
    });
  } catch (error) {
    console.error('Create bulk hiring job error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Get bulk hiring job details
app.get('/api/bulk-hiring/jobs/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    const jobResult = await pool.query(`
      SELECT bhj.*,
             u1."firstName" as creator_firstName, u1."lastName" as creator_lastName,
             u2."firstName" as approver_firstName, u2."lastName" as approver_lastName
      FROM bulk_hiring_jobs bhj
      JOIN users u1 ON bhj."createdBy" = u1.id
      LEFT JOIN users u2 ON bhj."approvedBy" = u2.id
      WHERE bhj.id = $1
    `, [id]);

    if (jobResult.rows.length === 0) {
      return res.status(404).json({ success: false, error: 'Job not found' });
    }

    const job = jobResult.rows[0];

    // Get applications
    const applicationsResult = await pool.query(`
      SELECT ja.*, u."firstName" as selector_firstName, u."lastName" as selector_lastName
      FROM job_applications ja
      LEFT JOIN users u ON ja."selectedBy" = u.id
      WHERE ja."jobId" = $1
      ORDER BY ja."createdAt" DESC
    `, [id]);

    // Parse JSON fields
    const jobWithData = {
      ...job,
      requiredSkills: JSON.parse(job.requiredSkills || '[]'),
      preferredSkills: JSON.parse(job.preferredSkills || '[]'),
      benefits: JSON.parse(job.benefits || '[]'),
      applications: applicationsResult.rows,
    };

    res.json({
      success: true,
      data: jobWithData,
    });
  } catch (error) {
    console.error('Get bulk hiring job error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Publish bulk hiring job
app.patch('/api/bulk-hiring/jobs/:id/publish', authenticateToken, async (req: any, res) => {
  try {
    const { id } = req.params;

    const result = await pool.query(`
      UPDATE bulk_hiring_jobs
      SET status = 'PUBLISHED', "publishedBy" = $1, "publishedAt" = NOW(), "updatedAt" = NOW()
      WHERE id = $2 AND status = 'DRAFT'
      RETURNING *
    `, [req.user!.id, id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ success: false, error: 'Job not found or not in draft status' });
    }

    res.json({
      success: true,
      data: result.rows[0],
      message: 'Job published successfully',
    });
  } catch (error) {
    console.error('Publish job error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Production server running on port ${PORT}`);
  console.log(`📊 Dashboard: http://localhost:3002`);
  console.log(`🔗 API: http://localhost:${PORT}`);
  console.log(`🎯 Features: Complete HRMS with Workforce Management, Resource Planning & Bulk Hiring`);
  console.log(`👥 HRMS: Comprehensive workforce profiles, education, certifications, leaves`);
  console.log(`🏢 Vendor: Enhanced vendor onboarding with compliance tracking`);
  console.log(`📋 Bulk Hiring: Blue collar & white collar job management with application tracking`);
});

export default app;
