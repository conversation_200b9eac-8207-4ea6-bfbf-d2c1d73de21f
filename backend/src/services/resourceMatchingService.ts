import { prisma } from '../config/database';
import { logger } from '../utils/logger';

export interface ResourceMatchCriteria {
  skillId: string;
  role: string;
  minExperience: number;
  allocationPercent: number;
  startDate: Date;
  endDate: Date;
  maxBudget?: number;
}

export interface ResourceMatch {
  resourceId: string;
  resource: any;
  matchScore: number;
  availability: number;
  skillMatch: number;
  experienceMatch: number;
  budgetMatch: number;
  availabilityDetails: {
    currentAllocations: number;
    availableCapacity: number;
    conflictingProjects: string[];
  };
}

export interface ResourceAvailability {
  resourceId: string;
  totalAllocation: number;
  availableCapacity: number;
  allocations: Array<{
    projectId: string;
    projectName: string;
    allocationPercent: number;
    startDate: Date;
    endDate: Date;
  }>;
}

export class ResourceMatchingService {
  /**
   * Find best matching resources for a resource plan
   */
  async findMatchingResources(
    criteria: ResourceMatchCriteria,
    limit: number = 10
  ): Promise<ResourceMatch[]> {
    try {
      logger.info('Finding matching resources', { criteria, limit });

      // Get all resources with the required skill
      const resourcesWithSkill = await prisma.resource.findMany({
        where: {
          status: 'AVAILABLE',
          skills: {
            some: {
              skillId: criteria.skillId,
            },
          },
        },
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          skills: {
            include: {
              skill: true,
            },
          },
          projectResources: {
            where: {
              OR: [
                {
                  endDate: {
                    gte: criteria.startDate,
                  },
                },
                {
                  endDate: null,
                },
              ],
              startDate: {
                lte: criteria.endDate,
              },
            },
            include: {
              project: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
          planAllocations: {
            where: {
              OR: [
                {
                  endDate: {
                    gte: criteria.startDate,
                  },
                },
                {
                  endDate: null,
                },
              ],
              startDate: {
                lte: criteria.endDate,
              },
            },
            include: {
              resourcePlan: {
                include: {
                  project: {
                    select: {
                      id: true,
                      name: true,
                    },
                  },
                },
              },
            },
          },
        },
      });

      // Calculate match scores for each resource
      const matches: ResourceMatch[] = [];

      for (const resource of resourcesWithSkill) {
        const availability = this.calculateResourceAvailability(
          resource,
          criteria.startDate,
          criteria.endDate
        );

        // Skip if resource doesn't have enough availability
        if (availability.availableCapacity < criteria.allocationPercent) {
          continue;
        }

        const skillMatch = this.calculateSkillMatch(resource, criteria);
        const experienceMatch = this.calculateExperienceMatch(resource, criteria);
        const budgetMatch = this.calculateBudgetMatch(resource, criteria);

        const matchScore = this.calculateOverallMatchScore(
          skillMatch,
          experienceMatch,
          budgetMatch,
          availability.availableCapacity / 100
        );

        matches.push({
          resourceId: resource.id,
          resource: {
            id: resource.id,
            user: resource.user,
            designation: resource.designation,
            department: resource.department,
            hourlyRate: resource.hourlyRate,
            skills: resource.skills,
          },
          matchScore,
          availability: availability.availableCapacity,
          skillMatch,
          experienceMatch,
          budgetMatch,
          availabilityDetails: {
            currentAllocations: availability.totalAllocation,
            availableCapacity: availability.availableCapacity,
            conflictingProjects: availability.allocations.map(a => a.projectName),
          },
        });
      }

      // Sort by match score and return top matches
      return matches
        .sort((a, b) => b.matchScore - a.matchScore)
        .slice(0, limit);
    } catch (error) {
      logger.error('Error finding matching resources:', error);
      throw error;
    }
  }

  /**
   * Calculate resource availability for a given time period
   */
  calculateResourceAvailability(
    resource: any,
    startDate: Date,
    endDate: Date
  ): ResourceAvailability {
    let totalAllocation = 0;
    const allocations: any[] = [];

    // Calculate allocations from project resources
    for (const projectResource of resource.projectResources) {
      const overlap = this.calculateDateOverlap(
        startDate,
        endDate,
        projectResource.startDate,
        projectResource.endDate || new Date('2099-12-31')
      );

      if (overlap > 0) {
        totalAllocation += projectResource.allocationPercent;
        allocations.push({
          projectId: projectResource.project.id,
          projectName: projectResource.project.name,
          allocationPercent: projectResource.allocationPercent,
          startDate: projectResource.startDate,
          endDate: projectResource.endDate,
        });
      }
    }

    // Calculate allocations from plan allocations
    for (const planAllocation of resource.planAllocations) {
      const overlap = this.calculateDateOverlap(
        startDate,
        endDate,
        planAllocation.startDate,
        planAllocation.endDate || new Date('2099-12-31')
      );

      if (overlap > 0) {
        totalAllocation += planAllocation.allocationPercent;
        allocations.push({
          projectId: planAllocation.resourcePlan.project.id,
          projectName: planAllocation.resourcePlan.project.name,
          allocationPercent: planAllocation.allocationPercent,
          startDate: planAllocation.startDate,
          endDate: planAllocation.endDate,
        });
      }
    }

    return {
      resourceId: resource.id,
      totalAllocation: Math.min(totalAllocation, 100),
      availableCapacity: Math.max(0, 100 - totalAllocation),
      allocations,
    };
  }

  /**
   * Calculate skill match score (0-100)
   */
  private calculateSkillMatch(resource: any, criteria: ResourceMatchCriteria): number {
    const resourceSkill = resource.skills.find(
      (rs: any) => rs.skillId === criteria.skillId
    );

    if (!resourceSkill) return 0;

    // Base score from proficiency level (1-5 scale -> 0-100)
    const proficiencyScore = (resourceSkill.proficiencyLevel / 5) * 100;

    // Bonus for certification
    const certificationBonus = resourceSkill.certified ? 10 : 0;

    return Math.min(100, proficiencyScore + certificationBonus);
  }

  /**
   * Calculate experience match score (0-100)
   */
  private calculateExperienceMatch(resource: any, criteria: ResourceMatchCriteria): number {
    const resourceSkill = resource.skills.find(
      (rs: any) => rs.skillId === criteria.skillId
    );

    if (!resourceSkill) return 0;

    const experience = resourceSkill.yearsOfExperience;
    const required = criteria.minExperience;

    if (experience >= required) {
      // Perfect match if experience is exactly what's needed
      // Bonus for additional experience (up to 2x required)
      const bonus = Math.min(experience - required, required) / required * 20;
      return Math.min(100, 80 + bonus);
    } else {
      // Penalty for insufficient experience
      return (experience / required) * 60;
    }
  }

  /**
   * Calculate budget match score (0-100)
   */
  private calculateBudgetMatch(resource: any, criteria: ResourceMatchCriteria): number {
    if (!criteria.maxBudget) return 100; // No budget constraint

    const hourlyRate = resource.hourlyRate;
    if (hourlyRate <= criteria.maxBudget) {
      // Better score for lower rates within budget
      return 100 - ((hourlyRate / criteria.maxBudget) * 20);
    } else {
      // Penalty for exceeding budget
      const overage = (hourlyRate - criteria.maxBudget) / criteria.maxBudget;
      return Math.max(0, 60 - (overage * 60));
    }
  }

  /**
   * Calculate overall match score
   */
  private calculateOverallMatchScore(
    skillMatch: number,
    experienceMatch: number,
    budgetMatch: number,
    availabilityRatio: number
  ): number {
    // Weighted scoring
    const weights = {
      skill: 0.35,
      experience: 0.25,
      budget: 0.20,
      availability: 0.20,
    };

    return (
      skillMatch * weights.skill +
      experienceMatch * weights.experience +
      budgetMatch * weights.budget +
      (availabilityRatio * 100) * weights.availability
    );
  }

  /**
   * Calculate date overlap percentage
   */
  private calculateDateOverlap(
    start1: Date,
    end1: Date,
    start2: Date,
    end2: Date
  ): number {
    const overlapStart = new Date(Math.max(start1.getTime(), start2.getTime()));
    const overlapEnd = new Date(Math.min(end1.getTime(), end2.getTime()));

    if (overlapStart >= overlapEnd) return 0;

    const overlapDays = (overlapEnd.getTime() - overlapStart.getTime()) / (1000 * 60 * 60 * 24);
    const totalDays = (end1.getTime() - start1.getTime()) / (1000 * 60 * 60 * 24);

    return (overlapDays / totalDays) * 100;
  }

  /**
   * Get resource availability for multiple resources
   */
  async getResourcesAvailability(
    resourceIds: string[],
    startDate: Date,
    endDate: Date
  ): Promise<ResourceAvailability[]> {
    const resources = await prisma.resource.findMany({
      where: {
        id: {
          in: resourceIds,
        },
      },
      include: {
        projectResources: {
          where: {
            OR: [
              {
                endDate: {
                  gte: startDate,
                },
              },
              {
                endDate: null,
              },
            ],
            startDate: {
              lte: endDate,
            },
          },
          include: {
            project: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
        planAllocations: {
          where: {
            OR: [
              {
                endDate: {
                  gte: startDate,
                },
              },
              {
                endDate: null,
              },
            ],
            startDate: {
              lte: endDate,
            },
          },
          include: {
            resourcePlan: {
              include: {
                project: {
                  select: {
                    id: true,
                    name: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    return resources.map(resource =>
      this.calculateResourceAvailability(resource, startDate, endDate)
    );
  }
}

export const resourceMatchingService = new ResourceMatchingService();
