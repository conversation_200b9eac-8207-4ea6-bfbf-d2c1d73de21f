import express from 'express';
import { body, param, query, validationResult } from 'express-validator';
import { prisma } from '../config/database';
import { createError } from '../middleware/errorHandler';
import { authorize, AuthRequest } from '../middleware/auth';
import { UserRole } from '@agentic-talent-pro/shared';
import { resourceMatchingService } from '../services/resourceMatchingService';
import { logger } from '../utils/logger';

const router = express.Router();

/**
 * @swagger
 * /api/resource-plans:
 *   get:
 *     summary: Get all resource plans
 *     tags: [Resource Plans]
 *     security:
 *       - bearerAuth: []
 */
router.get('/', authorize(UserRole.ADMIN, UserRole.PROJECT_MANAGER, UserRole.HR_MANAGER), [
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('projectId').optional().isUUID(),
  query('status').optional().isIn(['DRAFT', 'ACTIVE', 'FULFILLED', 'CANCELLED']),
], async (req: AuthRequest, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createError('Validation failed', 400);
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;
    const { projectId, status } = req.query;

    const where: any = {};
    if (projectId) where.projectId = projectId;
    if (status) where.status = status;

    // Project managers can only see their own project plans
    if (req.user!.role === UserRole.PROJECT_MANAGER) {
      where.project = {
        managerId: req.user!.id,
      };
    }

    const [resourcePlans, total] = await Promise.all([
      prisma.resourcePlan.findMany({
        where,
        skip,
        take: limit,
        include: {
          project: {
            select: {
              id: true,
              name: true,
              manager: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                },
              },
            },
          },
          skill: true,
          planAllocations: {
            include: {
              resource: {
                include: {
                  user: {
                    select: {
                      id: true,
                      firstName: true,
                      lastName: true,
                    },
                  },
                },
              },
            },
          },
          resourceRequests: {
            where: {
              status: {
                in: ['OPEN', 'IN_PROGRESS'],
              },
            },
          },
          _count: {
            select: {
              planAllocations: true,
              resourceRequests: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
      }),
      prisma.resourcePlan.count({ where }),
    ]);

    res.json({
      success: true,
      data: resourcePlans,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/resource-plans/{id}:
 *   get:
 *     summary: Get resource plan by ID
 *     tags: [Resource Plans]
 *     security:
 *       - bearerAuth: []
 */
router.get('/:id', authorize(UserRole.ADMIN, UserRole.PROJECT_MANAGER, UserRole.HR_MANAGER), [
  param('id').isUUID(),
], async (req: AuthRequest, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createError('Validation failed', 400);
    }

    const { id } = req.params;

    const resourcePlan = await prisma.resourcePlan.findUnique({
      where: { id },
      include: {
        project: {
          include: {
            manager: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        },
        skill: true,
        planAllocations: {
          include: {
            resource: {
              include: {
                user: {
                  select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    email: true,
                  },
                },
                skills: {
                  include: {
                    skill: true,
                  },
                },
              },
            },
          },
        },
        resourceRequests: {
          include: {
            requester: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
              },
            },
            assignee: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
              },
            },
          },
        },
      },
    });

    if (!resourcePlan) {
      throw createError('Resource plan not found', 404);
    }

    // Check access permissions
    if (req.user!.role === UserRole.PROJECT_MANAGER && 
        resourcePlan.project.managerId !== req.user!.id) {
      throw createError('Access denied', 403);
    }

    res.json({
      success: true,
      data: resourcePlan,
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/resource-plans:
 *   post:
 *     summary: Create new resource plan
 *     tags: [Resource Plans]
 *     security:
 *       - bearerAuth: []
 */
router.post('/', authorize(UserRole.ADMIN, UserRole.PROJECT_MANAGER), [
  body('projectId').isUUID(),
  body('skillId').isUUID(),
  body('role').isLength({ min: 1 }),
  body('allocationPercent').isInt({ min: 1, max: 100 }),
  body('requiredCount').isInt({ min: 1 }),
  body('startDate').isISO8601(),
  body('endDate').isISO8601(),
  body('minExperience').optional().isInt({ min: 0 }),
  body('maxBudget').optional().isFloat({ min: 0 }),
  body('description').optional().isLength({ min: 1 }),
], async (req: AuthRequest, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createError('Validation failed', 400);
    }

    const {
      projectId,
      skillId,
      role,
      allocationPercent,
      requiredCount,
      startDate,
      endDate,
      minExperience = 0,
      maxBudget,
      description,
    } = req.body;

    // Verify project exists and user has access
    const project = await prisma.project.findUnique({
      where: { id: projectId },
    });

    if (!project) {
      throw createError('Project not found', 404);
    }

    if (req.user!.role === UserRole.PROJECT_MANAGER && project.managerId !== req.user!.id) {
      throw createError('Access denied', 403);
    }

    // Verify skill exists
    const skill = await prisma.skill.findUnique({
      where: { id: skillId },
    });

    if (!skill) {
      throw createError('Skill not found', 404);
    }

    // Validate dates
    if (new Date(startDate) >= new Date(endDate)) {
      throw createError('End date must be after start date', 400);
    }

    const resourcePlan = await prisma.resourcePlan.create({
      data: {
        projectId,
        skillId,
        role,
        allocationPercent,
        requiredCount,
        startDate: new Date(startDate),
        endDate: new Date(endDate),
        minExperience,
        maxBudget,
        description,
      },
      include: {
        project: {
          select: {
            id: true,
            name: true,
          },
        },
        skill: true,
      },
    });

    logger.info('Resource plan created', {
      resourcePlanId: resourcePlan.id,
      projectId,
      createdBy: req.user!.id,
    });

    res.status(201).json({
      success: true,
      data: resourcePlan,
      message: 'Resource plan created successfully',
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/resource-plans/{id}:
 *   put:
 *     summary: Update resource plan
 *     tags: [Resource Plans]
 *     security:
 *       - bearerAuth: []
 */
router.put('/:id', authorize(UserRole.ADMIN, UserRole.PROJECT_MANAGER), [
  param('id').custom((value) => {
    // Accept both UUID and custom format (rp-timestamp)
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    const customRegex = /^rp-\d+$/;
    if (!uuidRegex.test(value) && !customRegex.test(value)) {
      throw new Error('Invalid resource plan ID format');
    }
    return true;
  }),
  body('role').optional().isLength({ min: 1 }),
  body('allocationPercent').optional().isInt({ min: 1, max: 100 }),
  body('requiredCount').optional().isInt({ min: 1 }),
  body('startDate').optional().isISO8601(),
  body('endDate').optional().isISO8601(),
  body('minExperience').optional().isInt({ min: 0 }),
  body('maxBudget').optional().isFloat({ min: 0 }),
  body('description').optional().isLength({ min: 1 }),
  body('status').optional().isIn(['DRAFT', 'ACTIVE', 'FULFILLED', 'CANCELLED']),
], async (req: AuthRequest, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createError('Validation failed', 400);
    }

    const { id } = req.params;
    const updateData = req.body;

    // Verify resource plan exists and user has access
    const existingPlan = await prisma.resourcePlan.findUnique({
      where: { id },
      include: {
        project: true,
      },
    });

    if (!existingPlan) {
      throw createError('Resource plan not found', 404);
    }

    if (req.user!.role === UserRole.PROJECT_MANAGER && 
        existingPlan.project.managerId !== req.user!.id) {
      throw createError('Access denied', 403);
    }

    // Validate dates if provided
    if (updateData.startDate && updateData.endDate) {
      if (new Date(updateData.startDate) >= new Date(updateData.endDate)) {
        throw createError('End date must be after start date', 400);
      }
    }

    // Convert date strings to Date objects
    if (updateData.startDate) updateData.startDate = new Date(updateData.startDate);
    if (updateData.endDate) updateData.endDate = new Date(updateData.endDate);

    const resourcePlan = await prisma.resourcePlan.update({
      where: { id },
      data: updateData,
      include: {
        project: {
          select: {
            id: true,
            name: true,
          },
        },
        skill: true,
        planAllocations: {
          include: {
            resource: {
              include: {
                user: {
                  select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    logger.info('Resource plan updated', {
      resourcePlanId: id,
      updatedBy: req.user!.id,
      changes: Object.keys(updateData),
    });

    res.json({
      success: true,
      data: resourcePlan,
      message: 'Resource plan updated successfully',
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/resource-plans/{id}:
 *   delete:
 *     summary: Delete resource plan
 *     tags: [Resource Plans]
 *     security:
 *       - bearerAuth: []
 */
router.delete('/:id', authorize(UserRole.ADMIN, UserRole.PROJECT_MANAGER), [
  param('id').custom((value) => {
    // Accept both UUID and custom format (rp-timestamp)
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    const customRegex = /^rp-\d+$/;
    if (!uuidRegex.test(value) && !customRegex.test(value)) {
      throw new Error('Invalid resource plan ID format');
    }
    return true;
  }),
], async (req: AuthRequest, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createError('Validation failed', 400);
    }

    const { id } = req.params;

    // Verify resource plan exists and user has access
    const existingPlan = await prisma.resourcePlan.findUnique({
      where: { id },
      include: {
        project: true,
        planAllocations: true,
        resourceRequests: true,
      },
    });

    if (!existingPlan) {
      throw createError('Resource plan not found', 404);
    }

    if (req.user!.role === UserRole.PROJECT_MANAGER &&
        existingPlan.project.managerId !== req.user!.id) {
      throw createError('Access denied', 403);
    }

    // Check if plan has active allocations
    if (existingPlan.planAllocations.length > 0) {
      throw createError('Cannot delete resource plan with active allocations', 400);
    }

    await prisma.resourcePlan.delete({
      where: { id },
    });

    logger.info('Resource plan deleted', {
      resourcePlanId: id,
      deletedBy: req.user!.id,
    });

    res.json({
      success: true,
      message: 'Resource plan deleted successfully',
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/resource-plans/{id}/match-resources:
 *   post:
 *     summary: Find matching resources for a resource plan
 *     tags: [Resource Plans]
 *     security:
 *       - bearerAuth: []
 */
router.post('/:id/match-resources', authorize(UserRole.ADMIN, UserRole.PROJECT_MANAGER, UserRole.HR_MANAGER), [
  param('id').custom((value) => {
    // Accept both UUID and custom format (rp-timestamp)
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    const customRegex = /^rp-\d+$/;
    if (!uuidRegex.test(value) && !customRegex.test(value)) {
      throw new Error('Invalid resource plan ID format');
    }
    return true;
  }),
  body('limit').optional().isInt({ min: 1, max: 50 }),
], async (req: AuthRequest, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createError('Validation failed', 400);
    }

    const { id } = req.params;
    const { limit = 10 } = req.body;

    // Get resource plan
    const resourcePlan = await prisma.resourcePlan.findUnique({
      where: { id },
      include: {
        project: true,
        skill: true,
      },
    });

    if (!resourcePlan) {
      throw createError('Resource plan not found', 404);
    }

    // Check access permissions
    if (req.user!.role === UserRole.PROJECT_MANAGER &&
        resourcePlan.project.managerId !== req.user!.id) {
      throw createError('Access denied', 403);
    }

    // Find matching resources
    const matches = await resourceMatchingService.findMatchingResources({
      skillId: resourcePlan.skillId,
      role: resourcePlan.role,
      minExperience: resourcePlan.minExperience,
      allocationPercent: resourcePlan.allocationPercent,
      startDate: resourcePlan.startDate,
      endDate: resourcePlan.endDate,
      maxBudget: resourcePlan.maxBudget || undefined,
    }, limit);

    logger.info('Resource matching performed', {
      resourcePlanId: id,
      matchesFound: matches.length,
      requestedBy: req.user!.id,
    });

    res.json({
      success: true,
      data: {
        resourcePlan: {
          id: resourcePlan.id,
          role: resourcePlan.role,
          skill: resourcePlan.skill,
          requirements: {
            allocationPercent: resourcePlan.allocationPercent,
            minExperience: resourcePlan.minExperience,
            maxBudget: resourcePlan.maxBudget,
            startDate: resourcePlan.startDate,
            endDate: resourcePlan.endDate,
          },
        },
        matches,
        summary: {
          totalMatches: matches.length,
          averageMatchScore: matches.length > 0
            ? matches.reduce((sum, m) => sum + m.matchScore, 0) / matches.length
            : 0,
          perfectMatches: matches.filter(m => m.matchScore >= 90).length,
          goodMatches: matches.filter(m => m.matchScore >= 70 && m.matchScore < 90).length,
        },
      },
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/resource-plans/{id}/allocate-resource:
 *   post:
 *     summary: Allocate a resource to a resource plan
 *     tags: [Resource Plans]
 *     security:
 *       - bearerAuth: []
 */
router.post('/:id/allocate-resource', authorize(UserRole.ADMIN, UserRole.PROJECT_MANAGER), [
  param('id').custom((value) => {
    // Accept both UUID and custom format (rp-timestamp)
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    const customRegex = /^rp-\d+$/;
    if (!uuidRegex.test(value) && !customRegex.test(value)) {
      throw new Error('Invalid resource plan ID format');
    }
    return true;
  }),
  body('resourceId').isUUID(),
  body('allocationPercent').optional().isInt({ min: 1, max: 100 }),
  body('startDate').optional().isISO8601(),
  body('endDate').optional().isISO8601(),
], async (req: AuthRequest, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createError('Validation failed', 400);
    }

    const { id } = req.params;
    const { resourceId, allocationPercent, startDate, endDate } = req.body;

    // Get resource plan
    const resourcePlan = await prisma.resourcePlan.findUnique({
      where: { id },
      include: {
        project: true,
      },
    });

    if (!resourcePlan) {
      throw createError('Resource plan not found', 404);
    }

    // Check access permissions
    if (req.user!.role === UserRole.PROJECT_MANAGER &&
        resourcePlan.project.managerId !== req.user!.id) {
      throw createError('Access denied', 403);
    }

    // Verify resource exists and is available
    const resource = await prisma.resource.findUnique({
      where: { id: resourceId },
    });

    if (!resource) {
      throw createError('Resource not found', 404);
    }

    // Check if resource is already allocated to this plan
    const existingAllocation = await prisma.planAllocation.findUnique({
      where: {
        resourcePlanId_resourceId: {
          resourcePlanId: id,
          resourceId,
        },
      },
    });

    if (existingAllocation) {
      throw createError('Resource is already allocated to this plan', 409);
    }

    // Use plan dates if not provided
    const allocStartDate = startDate ? new Date(startDate) : resourcePlan.startDate;
    const allocEndDate = endDate ? new Date(endDate) : resourcePlan.endDate;
    const allocPercent = allocationPercent || resourcePlan.allocationPercent;

    // Check resource availability
    const availability = await resourceMatchingService.calculateResourceAvailability(
      { ...resource, projectResources: [], planAllocations: [] },
      allocStartDate,
      allocEndDate
    );

    if (availability.availableCapacity < allocPercent) {
      throw createError(
        `Resource only has ${availability.availableCapacity}% availability, but ${allocPercent}% is required`,
        400
      );
    }

    // Create allocation
    const allocation = await prisma.planAllocation.create({
      data: {
        resourcePlanId: id,
        resourceId,
        allocationPercent: allocPercent,
        startDate: allocStartDate,
        endDate: allocEndDate,
      },
      include: {
        resource: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        },
        resourcePlan: {
          include: {
            project: {
              select: {
                id: true,
                name: true,
              },
            },
            skill: true,
          },
        },
      },
    });

    logger.info('Resource allocated to plan', {
      resourcePlanId: id,
      resourceId,
      allocationPercent: allocPercent,
      allocatedBy: req.user!.id,
    });

    res.status(201).json({
      success: true,
      data: allocation,
      message: 'Resource allocated to plan successfully',
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/resource-plans/{id}/allocations/{allocationId}:
 *   put:
 *     summary: Update resource allocation
 *     tags: [Resource Plans]
 *     security:
 *       - bearerAuth: []
 */
router.put('/:id/allocations/:allocationId', authorize(UserRole.ADMIN, UserRole.PROJECT_MANAGER), [
  param('id').custom((value) => {
    // Accept both UUID and custom format (rp-timestamp)
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    const customRegex = /^rp-\d+$/;
    if (!uuidRegex.test(value) && !customRegex.test(value)) {
      throw new Error('Invalid resource plan ID format');
    }
    return true;
  }),
  param('allocationId').isUUID(),
  body('allocationPercent').optional().isInt({ min: 0, max: 100 }),
  body('startDate').optional().isISO8601(),
  body('endDate').optional().isISO8601(),
], async (req: AuthRequest, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createError('Validation failed', 400);
    }

    const { id, allocationId } = req.params;
    const { allocationPercent, startDate, endDate } = req.body;

    // Verify allocation exists and user has access
    const existingAllocation = await prisma.planAllocation.findUnique({
      where: { id: allocationId },
      include: {
        resourcePlan: {
          include: {
            project: true,
          },
        },
      },
    });

    if (!existingAllocation) {
      throw createError('Allocation not found', 404);
    }

    if (existingAllocation.resourcePlanId !== id) {
      throw createError('Allocation does not belong to this resource plan', 400);
    }

    if (req.user!.role === UserRole.PROJECT_MANAGER &&
        existingAllocation.resourcePlan.project.managerId !== req.user!.id) {
      throw createError('Access denied', 403);
    }

    // Prepare update data
    const updateData: any = {};
    if (allocationPercent !== undefined) updateData.allocationPercent = allocationPercent;
    if (startDate) updateData.startDate = new Date(startDate);
    if (endDate) updateData.endDate = new Date(endDate);

    // Update allocation
    const updatedAllocation = await prisma.planAllocation.update({
      where: { id: allocationId },
      data: updateData,
      include: {
        resource: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        },
        resourcePlan: {
          include: {
            project: {
              select: {
                id: true,
                name: true,
              },
            },
            skill: true,
          },
        },
      },
    });

    logger.info('Resource allocation updated', {
      allocationId,
      resourcePlanId: id,
      updatedBy: req.user!.id,
      changes: updateData,
    });

    res.json({
      success: true,
      data: updatedAllocation,
      message: 'Resource allocation updated successfully',
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/resource-plans/{id}/allocations/{allocationId}:
 *   delete:
 *     summary: Remove resource allocation (deallocate)
 *     tags: [Resource Plans]
 *     security:
 *       - bearerAuth: []
 */
router.delete('/:id/allocations/:allocationId', authorize(UserRole.ADMIN, UserRole.PROJECT_MANAGER), [
  param('id').custom((value) => {
    // Accept both UUID and custom format (rp-timestamp)
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    const customRegex = /^rp-\d+$/;
    if (!uuidRegex.test(value) && !customRegex.test(value)) {
      throw new Error('Invalid resource plan ID format');
    }
    return true;
  }),
  param('allocationId').isUUID(),
], async (req: AuthRequest, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createError('Validation failed', 400);
    }

    const { id, allocationId } = req.params;

    // Verify allocation exists and user has access
    const existingAllocation = await prisma.planAllocation.findUnique({
      where: { id: allocationId },
      include: {
        resourcePlan: {
          include: {
            project: true,
          },
        },
        resource: {
          include: {
            user: {
              select: {
                firstName: true,
                lastName: true,
              },
            },
          },
        },
      },
    });

    if (!existingAllocation) {
      throw createError('Allocation not found', 404);
    }

    if (existingAllocation.resourcePlanId !== id) {
      throw createError('Allocation does not belong to this resource plan', 400);
    }

    if (req.user!.role === UserRole.PROJECT_MANAGER &&
        existingAllocation.resourcePlan.project.managerId !== req.user!.id) {
      throw createError('Access denied', 403);
    }

    // Delete allocation
    await prisma.planAllocation.delete({
      where: { id: allocationId },
    });

    logger.info('Resource allocation removed', {
      allocationId,
      resourcePlanId: id,
      resourceName: `${existingAllocation.resource.user.firstName} ${existingAllocation.resource.user.lastName}`,
      removedBy: req.user!.id,
    });

    res.json({
      success: true,
      message: 'Resource allocation removed successfully',
    });
  } catch (error) {
    next(error);
  }
});

export default router;
