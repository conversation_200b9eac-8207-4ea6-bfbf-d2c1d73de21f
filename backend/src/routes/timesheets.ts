import express from 'express';
import { body, query, validationResult } from 'express-validator';
import { prisma } from '../config/database';
import { createError } from '../middleware/errorHandler';
import { authenticate, authorize, AuthRequest } from '../middleware/auth';
import { UserRole, TimesheetStatus } from '@agentic-talent-pro/shared';

const router = express.Router();

// Apply authentication to all routes
router.use(authenticate);

/**
 * @swagger
 * /api/timesheets:
 *   get:
 *     summary: Get all timesheets
 *     tags: [Timesheets]
 *     security:
 *       - bearerAuth: []
 */
router.get('/', async (req: AuthRequest, res, next) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;
    const resourceId = req.query.resourceId as string;
    const projectId = req.query.projectId as string;
    const status = req.query.status as TimesheetStatus;
    const weekStarting = req.query.weekStarting as string;

    const where: any = {};

    if (resourceId) where.resourceId = resourceId;
    if (projectId) where.projectId = projectId;
    if (status) where.status = status;
    if (weekStarting) where.weekStarting = new Date(weekStarting);

    // Filter based on user role
    if (req.user!.role === UserRole.RESOURCE) {
      const resource = await prisma.resource.findUnique({
        where: { userId: req.user!.id },
      });
      if (resource) {
        where.resourceId = resource.id;
      }
    }

    const [timesheets, total] = await Promise.all([
      prisma.timesheet.findMany({
        where,
        skip,
        take: limit,
        include: {
          resource: {
            include: {
              user: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true,
                },
              },
            },
          },
          project: {
            select: {
              id: true,
              name: true,
            },
          },
          entries: {
            include: {
              task: {
                select: {
                  id: true,
                  title: true,
                },
              },
            },
          },
        },
        orderBy: { weekStarting: 'desc' },
      }),
      prisma.timesheet.count({ where }),
    ]);

    res.json({
      success: true,
      data: timesheets,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/timesheets/{id}/approve:
 *   patch:
 *     summary: Approve timesheet
 *     tags: [Timesheets]
 *     security:
 *       - bearerAuth: []
 */
router.patch('/:id/approve', authorize(UserRole.ADMIN, UserRole.PROJECT_MANAGER), async (req: AuthRequest, res, next) => {
  try {
    const { id } = req.params;

    const timesheet = await prisma.timesheet.findUnique({
      where: { id },
      include: { project: true },
    });

    if (!timesheet) {
      throw createError('Timesheet not found', 404);
    }

    if (timesheet.status !== TimesheetStatus.SUBMITTED) {
      throw createError('Only submitted timesheets can be approved', 400);
    }

    // Check if user can approve this timesheet
    if (req.user!.role === UserRole.PROJECT_MANAGER && timesheet.project.managerId !== req.user!.id) {
      throw createError('Access denied', 403);
    }

    const updatedTimesheet = await prisma.timesheet.update({
      where: { id },
      data: {
        status: TimesheetStatus.APPROVED,
        approvedAt: new Date(),
        approvedBy: req.user!.id,
      },
      include: {
        resource: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        },
        project: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    res.json({
      success: true,
      data: updatedTimesheet,
      message: 'Timesheet approved successfully',
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/timesheets/{id}/reject:
 *   patch:
 *     summary: Reject timesheet
 *     tags: [Timesheets]
 *     security:
 *       - bearerAuth: []
 */
router.patch('/:id/reject', authorize(UserRole.ADMIN, UserRole.PROJECT_MANAGER), async (req: AuthRequest, res, next) => {
  try {
    const { id } = req.params;
    const { reason } = req.body;

    if (!reason) {
      throw createError('Rejection reason is required', 400);
    }

    const timesheet = await prisma.timesheet.findUnique({
      where: { id },
      include: { project: true },
    });

    if (!timesheet) {
      throw createError('Timesheet not found', 404);
    }

    if (timesheet.status !== TimesheetStatus.SUBMITTED) {
      throw createError('Only submitted timesheets can be rejected', 400);
    }

    // Check if user can reject this timesheet
    if (req.user!.role === UserRole.PROJECT_MANAGER && timesheet.project.managerId !== req.user!.id) {
      throw createError('Access denied', 403);
    }

    const updatedTimesheet = await prisma.timesheet.update({
      where: { id },
      data: {
        status: TimesheetStatus.REJECTED,
        rejectionReason: reason,
      },
      include: {
        resource: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        },
        project: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    res.json({
      success: true,
      data: updatedTimesheet,
      message: 'Timesheet rejected successfully',
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/timesheets:
 *   post:
 *     summary: Create a new timesheet
 *     tags: [Timesheets]
 *     security:
 *       - bearerAuth: []
 */
router.post('/', async (req: AuthRequest, res, next) => {
  try {
    const { projectId, weekStarting, resourceId } = req.body;

    if (!projectId || !weekStarting) {
      throw createError('Project ID and week starting date are required', 400);
    }

    let targetResourceId = resourceId;

    // If no resourceId provided, use current user's resource
    if (!targetResourceId) {
      const resource = await prisma.resource.findUnique({
        where: { userId: req.user!.id },
      });

      if (!resource) {
        throw createError('Resource not found for user', 404);
      }
      targetResourceId = resource.id;
    } else {
      // If resourceId is provided, check if user has permission
      if (req.user!.role !== UserRole.ADMIN) {
        throw createError('Only admins can create timesheets for other resources', 403);
      }

      // Verify the resource exists
      const targetResource = await prisma.resource.findUnique({
        where: { id: targetResourceId },
      });

      if (!targetResource) {
        throw createError('Target resource not found', 404);
      }
    }

    // Calculate week ending date (6 days after week starting)
    const weekStartingDate = new Date(weekStarting);
    const weekEndingDate = new Date(weekStartingDate);
    weekEndingDate.setDate(weekEndingDate.getDate() + 6);

    // Check if timesheet already exists for this week and project
    const existingTimesheet = await prisma.timesheet.findFirst({
      where: {
        resourceId: targetResourceId,
        projectId,
        weekStarting: weekStartingDate,
      },
    });

    if (existingTimesheet) {
      throw createError('Timesheet already exists for this week and project', 400);
    }

    const timesheet = await prisma.timesheet.create({
      data: {
        resourceId: targetResourceId,
        projectId,
        weekStarting: weekStartingDate,
        weekEnding: weekEndingDate,
        status: TimesheetStatus.DRAFT,
        totalHours: 0,
      },
      include: {
        resource: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        },
        project: {
          select: {
            id: true,
            name: true,
          },
        },
        entries: {
          include: {
            task: {
              select: {
                id: true,
                title: true,
              },
            },
          },
        },
      },
    });

    res.json({
      success: true,
      data: timesheet,
      message: 'Timesheet created successfully',
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/timesheets/{id}/submit:
 *   patch:
 *     summary: Submit timesheet for approval
 *     tags: [Timesheets]
 *     security:
 *       - bearerAuth: []
 */
router.patch('/:id/submit', async (req: AuthRequest, res, next) => {
  try {
    const { id } = req.params;

    const timesheet = await prisma.timesheet.findUnique({
      where: { id },
      include: { resource: true },
    });

    if (!timesheet) {
      throw createError('Timesheet not found', 404);
    }

    // Check if user owns this timesheet or is admin
    if (timesheet.resource.userId !== req.user!.id && req.user!.role !== UserRole.ADMIN) {
      throw createError('Access denied', 403);
    }

    if (timesheet.status !== TimesheetStatus.DRAFT) {
      throw createError('Only draft timesheets can be submitted', 400);
    }

    // Calculate total hours from entries
    const entries = await prisma.timesheetEntry.findMany({
      where: { timesheetId: id },
    });

    const totalHours = entries.reduce((sum, entry) => sum + entry.hours, 0);

    if (totalHours === 0) {
      throw createError('Cannot submit timesheet with zero hours', 400);
    }

    const updatedTimesheet = await prisma.timesheet.update({
      where: { id },
      data: {
        status: TimesheetStatus.SUBMITTED,
        submittedAt: new Date(),
        totalHours,
      },
      include: {
        resource: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        },
        project: {
          select: {
            id: true,
            name: true,
          },
        },
        entries: {
          include: {
            task: {
              select: {
                id: true,
                title: true,
              },
            },
          },
        },
      },
    });

    res.json({
      success: true,
      data: updatedTimesheet,
      message: 'Timesheet submitted successfully',
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/timesheets/{id}/entries:
 *   post:
 *     summary: Add timesheet entry
 *     tags: [Timesheets]
 *     security:
 *       - bearerAuth: []
 */
router.post('/:id/entries', async (req: AuthRequest, res, next) => {
  try {
    const { id } = req.params;
    const { taskId, date, hours, description } = req.body;

    if (!taskId || !date || !hours) {
      throw createError('Task ID, date, and hours are required', 400);
    }

    const timesheet = await prisma.timesheet.findUnique({
      where: { id },
      include: { resource: true },
    });

    if (!timesheet) {
      throw createError('Timesheet not found', 404);
    }

    // Check if user owns this timesheet or is admin
    if (timesheet.resource.userId !== req.user!.id && req.user!.role !== UserRole.ADMIN) {
      throw createError('Access denied', 403);
    }

    if (timesheet.status !== TimesheetStatus.DRAFT) {
      throw createError('Can only add entries to draft timesheets', 400);
    }

    // Check if entry already exists for this date and task
    const existingEntry = await prisma.timesheetEntry.findFirst({
      where: {
        timesheetId: id,
        taskId,
        date: new Date(date),
      },
    });

    if (existingEntry) {
      // Update existing entry
      const updatedEntry = await prisma.timesheetEntry.update({
        where: { id: existingEntry.id },
        data: {
          hours: parseFloat(hours),
          description: description || '',
        },
        include: {
          task: {
            select: {
              id: true,
              title: true,
            },
          },
        },
      });

      res.json({
        success: true,
        data: updatedEntry,
        message: 'Timesheet entry updated successfully',
      });
    } else {
      // Create new entry
      const entry = await prisma.timesheetEntry.create({
        data: {
          timesheetId: id,
          taskId,
          date: new Date(date),
          hours: parseFloat(hours),
          description: description || '',
        },
        include: {
          task: {
            select: {
              id: true,
              title: true,
            },
          },
        },
      });

      res.json({
        success: true,
        data: entry,
        message: 'Timesheet entry created successfully',
      });
    }
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/timesheets/my-projects:
 *   get:
 *     summary: Get projects assigned to current user
 *     tags: [Timesheets]
 *     security:
 *       - bearerAuth: []
 */
router.get('/my-projects', async (req: AuthRequest, res, next) => {
  try {
    // Get the resource for the current user
    const resource = await prisma.resource.findUnique({
      where: { userId: req.user!.id },
    });

    if (!resource) {
      throw createError('Resource not found for user', 404);
    }

    // Get projects where the user is assigned
    const assignments = await prisma.resourceAssignment.findMany({
      where: { resourceId: resource.id },
      include: {
        project: {
          select: {
            id: true,
            name: true,
            description: true,
            startDate: true,
            endDate: true,
          },
        },
      },
    });

    const projects = assignments.map(assignment => assignment.project);

    res.json({
      success: true,
      data: projects,
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/timesheets/my-tasks:
 *   get:
 *     summary: Get tasks assigned to current user
 *     tags: [Timesheets]
 *     security:
 *       - bearerAuth: []
 */
router.get('/my-tasks', async (req: AuthRequest, res, next) => {
  try {
    const projectId = req.query.projectId as string;

    const where: any = {
      assignedToId: req.user!.id,
    };

    if (projectId) {
      where.projectId = projectId;
    }

    const tasks = await prisma.task.findMany({
      where,
      select: {
        id: true,
        title: true,
        description: true,
        projectId: true,
        priority: true,
        estimatedHours: true,
        project: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    });

    res.json({
      success: true,
      data: tasks,
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/timesheets/all-resources:
 *   get:
 *     summary: Get all resources (Admin only)
 *     tags: [Timesheets]
 *     security:
 *       - bearerAuth: []
 */
router.get('/all-resources', authorize(UserRole.ADMIN), async (req: AuthRequest, res, next) => {
  try {
    const resources = await prisma.resource.findMany({
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
      orderBy: [
        { user: { firstName: 'asc' } },
        { user: { lastName: 'asc' } },
      ],
    });

    res.json({
      success: true,
      data: resources,
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/timesheets/all-projects:
 *   get:
 *     summary: Get all projects (Admin only)
 *     tags: [Timesheets]
 *     security:
 *       - bearerAuth: []
 */
router.get('/all-projects', authorize(UserRole.ADMIN), async (req: AuthRequest, res, next) => {
  try {
    const projects = await prisma.project.findMany({
      select: {
        id: true,
        name: true,
        description: true,
        startDate: true,
        endDate: true,
      },
      orderBy: { name: 'asc' },
    });

    res.json({
      success: true,
      data: projects,
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/timesheets/resource-tasks:
 *   get:
 *     summary: Get tasks for a specific resource (Admin only)
 *     tags: [Timesheets]
 *     security:
 *       - bearerAuth: []
 */
router.get('/resource-tasks', authorize(UserRole.ADMIN), async (req: AuthRequest, res, next) => {
  try {
    const { resourceId, projectId } = req.query;

    if (!resourceId) {
      throw createError('Resource ID is required', 400);
    }

    // Get the user ID for the resource
    const resource = await prisma.resource.findUnique({
      where: { id: resourceId as string },
      select: { userId: true },
    });

    if (!resource) {
      throw createError('Resource not found', 404);
    }

    const where: any = {
      assignedToId: resource.userId,
    };

    if (projectId) {
      where.projectId = projectId as string;
    }

    const tasks = await prisma.task.findMany({
      where,
      select: {
        id: true,
        title: true,
        description: true,
        projectId: true,
        priority: true,
        estimatedHours: true,
        project: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    });

    res.json({
      success: true,
      data: tasks,
    });
  } catch (error) {
    next(error);
  }
});

export default router;
