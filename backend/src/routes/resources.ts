import express from 'express';
import { body, query, validationResult } from 'express-validator';
import { prisma } from '../config/database';
import { createError } from '../middleware/errorHandler';
import { authenticate, authorize, AuthRequest } from '../middleware/auth';
import { UserRole, EmploymentType, ResourceStatus } from '@agentic-talent-pro/shared';

const router = express.Router();

// Apply authentication to all routes
router.use(authenticate);

/**
 * @swagger
 * /api/resources:
 *   get:
 *     summary: Get all resources
 *     tags: [Resources]
 *     security:
 *       - bearerAuth: []
 */
router.get('/', authorize(UserRole.ADMIN, UserRole.HR_MANAGER, UserRole.PROJECT_MANAGER), async (req: AuthRequest, res, next) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;

    const [resources, total] = await Promise.all([
      prisma.resource.findMany({
        skip,
        take: limit,
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              phone: true,
            },
          },
          skills: {
            include: {
              skill: true,
            },
          },
          vendor: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
      }),
      prisma.resource.count(),
    ]);

    res.json({
      success: true,
      data: resources,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/resources/search:
 *   post:
 *     summary: Search resources by skills and availability
 *     tags: [Resources]
 *     security:
 *       - bearerAuth: []
 */
router.post('/search', authorize(UserRole.ADMIN, UserRole.HR_MANAGER, UserRole.PROJECT_MANAGER), [
  body('skills').optional().isArray(),
  body('location').optional().isString(),
  body('employmentType').optional().isIn(Object.values(EmploymentType)),
  body('minExperience').optional().isInt({ min: 0 }),
], async (req: AuthRequest, res, next) => {
  try {
    const { skills, location, employmentType, minExperience } = req.body;

    const where: any = {
      status: ResourceStatus.AVAILABLE,
    };

    if (location) {
      where.location = { contains: location, mode: 'insensitive' };
    }

    if (employmentType) {
      where.employmentType = employmentType;
    }

    if (skills && skills.length > 0) {
      where.skills = {
        some: {
          skillId: { in: skills },
          ...(minExperience && { yearsOfExperience: { gte: minExperience } }),
        },
      };
    }

    const resources = await prisma.resource.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        skills: {
          include: {
            skill: true,
          },
        },
      },
    });

    res.json({
      success: true,
      data: resources,
    });
  } catch (error) {
    next(error);
  }
});

export default router;
