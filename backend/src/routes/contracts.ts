import express from 'express';
import { body, query, validationResult } from 'express-validator';
import { prisma } from '../config/database';
import { createError } from '../middleware/errorHandler';
import { authenticate, authorize, AuthRequest } from '../middleware/auth';
import { UserRole, ContractType, ContractStatus } from '@agentic-talent-pro/shared';

const router = express.Router();

// Apply authentication to all routes
router.use(authenticate);

/**
 * @swagger
 * /api/contracts:
 *   get:
 *     summary: Get all contracts
 *     tags: [Contracts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [DRAFT, ACTIVE, COMPLETED, TERMINATED]
 *       - in: query
 *         name: clientId
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Contracts retrieved successfully
 */
router.get('/', [
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('status').optional().isIn(Object.values(ContractStatus)),
], async (req: AuthRequest, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createError('Validation failed', 400);
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;
    const status = req.query.status as ContractStatus;
    const clientId = req.query.clientId as string;

    const where: any = {};
    
    if (status) {
      where.status = status;
    }
    
    if (clientId) {
      where.clientId = clientId;
    }

    // If user is a client, only show their contracts
    if (req.user!.role === UserRole.CLIENT) {
      where.clientId = req.user!.id;
    }

    const [contracts, total] = await Promise.all([
      prisma.contract.findMany({
        where,
        skip,
        take: limit,
        include: {
          client: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          projects: {
            select: {
              id: true,
              name: true,
              status: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
      }),
      prisma.contract.count({ where }),
    ]);

    res.json({
      success: true,
      data: contracts,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/contracts/{id}:
 *   get:
 *     summary: Get contract by ID
 *     tags: [Contracts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Contract retrieved successfully
 *       404:
 *         description: Contract not found
 */
router.get('/:id', async (req: AuthRequest, res, next) => {
  try {
    const { id } = req.params;

    const contract = await prisma.contract.findUnique({
      where: { id },
      include: {
        client: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            phone: true,
          },
        },
        projects: {
          include: {
            manager: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        },
      },
    });

    if (!contract) {
      throw createError('Contract not found', 404);
    }

    // Check access permissions
    if (req.user!.role === UserRole.CLIENT && contract.clientId !== req.user!.id) {
      throw createError('Access denied', 403);
    }

    res.json({
      success: true,
      data: contract,
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/contracts:
 *   post:
 *     summary: Create new contract
 *     tags: [Contracts]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - title
 *               - description
 *               - clientId
 *               - type
 *               - startDate
 *               - endDate
 *               - value
 *               - terms
 *             properties:
 *               title:
 *                 type: string
 *               description:
 *                 type: string
 *               clientId:
 *                 type: string
 *               type:
 *                 type: string
 *                 enum: [FIXED_PRICE, TIME_AND_MATERIAL, RETAINER]
 *               startDate:
 *                 type: string
 *                 format: date
 *               endDate:
 *                 type: string
 *                 format: date
 *               value:
 *                 type: number
 *               currency:
 *                 type: string
 *                 default: USD
 *               terms:
 *                 type: string
 *     responses:
 *       201:
 *         description: Contract created successfully
 */
router.post('/', authorize(UserRole.ADMIN, UserRole.PROJECT_MANAGER), [
  body('title').isLength({ min: 1 }),
  body('description').isLength({ min: 1 }),
  body('clientId').isUUID(),
  body('type').isIn(Object.values(ContractType)),
  body('startDate').isISO8601(),
  body('endDate').isISO8601(),
  body('value').isFloat({ min: 0 }),
  body('currency').optional().isLength({ min: 3, max: 3 }),
  body('terms').isLength({ min: 1 }),
], async (req: AuthRequest, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createError('Validation failed', 400);
    }

    const {
      title,
      description,
      clientId,
      type,
      startDate,
      endDate,
      value,
      currency = 'USD',
      terms,
    } = req.body;

    // Verify client exists
    const client = await prisma.user.findUnique({
      where: { id: clientId },
    });

    if (!client || client.role !== UserRole.CLIENT) {
      throw createError('Invalid client ID', 400);
    }

    // Validate dates
    if (new Date(startDate) >= new Date(endDate)) {
      throw createError('End date must be after start date', 400);
    }

    const contract = await prisma.contract.create({
      data: {
        title,
        description,
        clientId,
        type: type as ContractType,
        startDate: new Date(startDate),
        endDate: new Date(endDate),
        value,
        currency,
        terms,
      },
      include: {
        client: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });

    res.status(201).json({
      success: true,
      data: contract,
      message: 'Contract created successfully',
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/contracts/{id}:
 *   put:
 *     summary: Update contract
 *     tags: [Contracts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Contract updated successfully
 *       404:
 *         description: Contract not found
 */
router.put('/:id', authorize(UserRole.ADMIN, UserRole.PROJECT_MANAGER), [
  body('title').optional().isLength({ min: 1 }),
  body('description').optional().isLength({ min: 1 }),
  body('type').optional().isIn(Object.values(ContractType)),
  body('status').optional().isIn(Object.values(ContractStatus)),
  body('startDate').optional().isISO8601(),
  body('endDate').optional().isISO8601(),
  body('value').optional().isFloat({ min: 0 }),
  body('currency').optional().isLength({ min: 3, max: 3 }),
  body('terms').optional().isLength({ min: 1 }),
], async (req: AuthRequest, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createError('Validation failed', 400);
    }

    const { id } = req.params;
    const updateData = req.body;

    // Check if contract exists
    const existingContract = await prisma.contract.findUnique({
      where: { id },
    });

    if (!existingContract) {
      throw createError('Contract not found', 404);
    }

    // Validate dates if provided
    if (updateData.startDate && updateData.endDate) {
      if (new Date(updateData.startDate) >= new Date(updateData.endDate)) {
        throw createError('End date must be after start date', 400);
      }
    }

    // Convert date strings to Date objects
    if (updateData.startDate) {
      updateData.startDate = new Date(updateData.startDate);
    }
    if (updateData.endDate) {
      updateData.endDate = new Date(updateData.endDate);
    }

    const contract = await prisma.contract.update({
      where: { id },
      data: updateData,
      include: {
        client: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });

    res.json({
      success: true,
      data: contract,
      message: 'Contract updated successfully',
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/contracts/{id}:
 *   delete:
 *     summary: Delete contract
 *     tags: [Contracts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Contract deleted successfully
 *       404:
 *         description: Contract not found
 */
router.delete('/:id', authorize(UserRole.ADMIN), async (req: AuthRequest, res, next) => {
  try {
    const { id } = req.params;

    const contract = await prisma.contract.findUnique({
      where: { id },
      include: { projects: true },
    });

    if (!contract) {
      throw createError('Contract not found', 404);
    }

    // Check if contract has associated projects
    if (contract.projects.length > 0) {
      throw createError('Cannot delete contract with associated projects', 400);
    }

    await prisma.contract.delete({
      where: { id },
    });

    res.json({
      success: true,
      message: 'Contract deleted successfully',
    });
  } catch (error) {
    next(error);
  }
});

export default router;
