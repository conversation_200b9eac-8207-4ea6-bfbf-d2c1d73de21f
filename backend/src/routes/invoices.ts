import express from 'express';
import { body, query, validationResult } from 'express-validator';
import { prisma } from '../config/database';
import { createError } from '../middleware/errorHandler';
import { authenticate, authorize, AuthRequest } from '../middleware/auth';
import { UserRole, InvoiceStatus } from '@agentic-talent-pro/shared';

const router = express.Router();

// Apply authentication to all routes
router.use(authenticate);

/**
 * @swagger
 * /api/invoices:
 *   get:
 *     summary: Get all invoices
 *     tags: [Invoices]
 *     security:
 *       - bearerAuth: []
 */
router.get('/', authorize(UserRole.ADMIN, UserRole.BILLING_MANAGER, UserRole.PROJECT_MANAGER), async (req: AuthRequest, res, next) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;

    const [invoices, total] = await Promise.all([
      prisma.invoice.findMany({
        skip,
        take: limit,
        include: {
          project: {
            select: {
              id: true,
              name: true,
            },
          },
          resource: {
            include: {
              user: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true,
                },
              },
            },
          },
          timesheet: {
            select: {
              id: true,
              weekStarting: true,
              weekEnding: true,
              totalHours: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
      }),
      prisma.invoice.count(),
    ]);

    res.json({
      success: true,
      data: invoices,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/invoices/generate:
 *   post:
 *     summary: Generate invoice from timesheet
 *     tags: [Invoices]
 *     security:
 *       - bearerAuth: []
 */
router.post('/generate', authorize(UserRole.ADMIN, UserRole.BILLING_MANAGER), [
  body('timesheetId').isUUID(),
  body('taxRate').optional().isFloat({ min: 0, max: 100 }),
], async (req: AuthRequest, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createError('Validation failed', 400);
    }

    const { timesheetId, taxRate = 0 } = req.body;

    // Get timesheet with related data
    const timesheet = await prisma.timesheet.findUnique({
      where: { id: timesheetId },
      include: {
        resource: true,
        project: true,
      },
    });

    if (!timesheet) {
      throw createError('Timesheet not found', 404);
    }

    if (timesheet.status !== 'APPROVED') {
      throw createError('Only approved timesheets can be invoiced', 400);
    }

    // Check if invoice already exists for this timesheet
    const existingInvoice = await prisma.invoice.findFirst({
      where: { timesheetId },
    });

    if (existingInvoice) {
      throw createError('Invoice already exists for this timesheet', 409);
    }

    // Calculate invoice amounts
    const subtotal = timesheet.totalHours * timesheet.resource.hourlyRate;
    const tax = subtotal * (taxRate / 100);
    const total = subtotal + tax;

    // Generate invoice number
    const invoiceCount = await prisma.invoice.count();
    const invoiceNumber = `INV-${new Date().getFullYear()}-${String(invoiceCount + 1).padStart(4, '0')}`;

    const invoice = await prisma.invoice.create({
      data: {
        invoiceNumber,
        projectId: timesheet.projectId,
        resourceId: timesheet.resourceId,
        timesheetId,
        issueDate: new Date(),
        dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        subtotal,
        tax,
        total,
      },
      include: {
        project: {
          select: {
            id: true,
            name: true,
          },
        },
        resource: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        },
        timesheet: {
          select: {
            id: true,
            weekStarting: true,
            weekEnding: true,
            totalHours: true,
          },
        },
      },
    });

    res.status(201).json({
      success: true,
      data: invoice,
      message: 'Invoice generated successfully',
    });
  } catch (error) {
    next(error);
  }
});

export default router;
