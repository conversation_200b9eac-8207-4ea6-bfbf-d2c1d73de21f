import express from 'express';
import { body, param, query, validationResult } from 'express-validator';
import { prisma } from '../config/database';
import { createError } from '../middleware/errorHandler';
import { authorize, AuthRequest } from '../middleware/auth';
import { UserRole } from '@agentic-talent-pro/shared';
import { logger } from '../utils/logger';

const router = express.Router();

/**
 * @swagger
 * /api/bulk-hiring/jobs:
 *   get:
 *     summary: Get all bulk hiring jobs
 *     tags: [Bulk Hiring]
 *     security:
 *       - bearerAuth: []
 */
router.get('/jobs', authorize(UserRole.ADMIN, UserRole.HR_MANAGER), [
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('status').optional().isIn(['DRAFT', 'PUBLISHED', 'ACTIVE', 'ON_HOLD', 'CLOSED', 'CANCELLED']),
  query('jobType').optional().isIn(['BLUE_COLLAR', 'WHITE_COLLAR']),
  query('hiringType').optional().isIn(['BULK', 'ADHOC']),
  query('department').optional().isString(),
], async (req: AuthRequest, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createError('Validation failed', 400);
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;
    const { status, jobType, hiringType, department } = req.query;

    const where: any = {};
    if (status) where.status = status;
    if (jobType) where.jobType = jobType;
    if (hiringType) where.hiringType = hiringType;
    if (department) where.department = { contains: department, mode: 'insensitive' };

    const [jobs, total] = await Promise.all([
      prisma.bulkHiringJob.findMany({
        where,
        skip,
        take: limit,
        include: {
          creator: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
            },
          },
          approver: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
            },
          },
          _count: {
            select: {
              applications: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
      }),
      prisma.bulkHiringJob.count({ where }),
    ]);

    res.json({
      success: true,
      data: jobs,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/bulk-hiring/jobs:
 *   post:
 *     summary: Create new bulk hiring job
 *     tags: [Bulk Hiring]
 *     security:
 *       - bearerAuth: []
 */
router.post('/jobs', authorize(UserRole.ADMIN, UserRole.HR_MANAGER), [
  body('title').isLength({ min: 1 }),
  body('description').isLength({ min: 1 }),
  body('jobType').isIn(['BLUE_COLLAR', 'WHITE_COLLAR']),
  body('hiringType').isIn(['BULK', 'ADHOC']),
  body('department').isLength({ min: 1 }),
  body('location').isLength({ min: 1 }),
  body('positions').isInt({ min: 1 }),
  body('employmentType').isIn(['FTE', 'CONTRACTOR', 'VENDOR']),
  body('minExperience').optional().isInt({ min: 0 }),
  body('maxExperience').optional().isInt({ min: 0 }),
  body('requiredSkills').isArray(),
  body('minSalary').optional().isFloat({ min: 0 }),
  body('maxSalary').optional().isFloat({ min: 0 }),
], async (req: AuthRequest, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createError('Validation failed', 400);
    }

    const jobData = {
      ...req.body,
      requiredSkills: JSON.stringify(req.body.requiredSkills),
      preferredSkills: req.body.preferredSkills ? JSON.stringify(req.body.preferredSkills) : null,
      benefits: req.body.benefits ? JSON.stringify(req.body.benefits) : null,
      preferredVendors: req.body.preferredVendors ? JSON.stringify(req.body.preferredVendors) : null,
      createdBy: req.user!.id,
      applicationDeadline: req.body.applicationDeadline ? new Date(req.body.applicationDeadline) : null,
      expectedJoiningDate: req.body.expectedJoiningDate ? new Date(req.body.expectedJoiningDate) : null,
    };

    const job = await prisma.bulkHiringJob.create({
      data: jobData,
      include: {
        creator: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          },
        },
      },
    });

    logger.info('Bulk hiring job created', {
      jobId: job.id,
      title: job.title,
      jobType: job.jobType,
      positions: job.positions,
      createdBy: req.user!.id,
    });

    res.status(201).json({
      success: true,
      data: job,
      message: 'Bulk hiring job created successfully',
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/bulk-hiring/jobs/{id}:
 *   get:
 *     summary: Get bulk hiring job details
 *     tags: [Bulk Hiring]
 *     security:
 *       - bearerAuth: []
 */
router.get('/jobs/:id', authorize(UserRole.ADMIN, UserRole.HR_MANAGER), [
  param('id').isUUID(),
], async (req: AuthRequest, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createError('Validation failed', 400);
    }

    const { id } = req.params;

    const job = await prisma.bulkHiringJob.findUnique({
      where: { id },
      include: {
        creator: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          },
        },
        approver: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          },
        },
        applications: {
          include: {
            selector: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
              },
            },
          },
          orderBy: { createdAt: 'desc' },
        },
        interviews: {
          include: {
            interviewer: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
              },
            },
            application: {
              select: {
                id: true,
                candidateName: true,
                candidateEmail: true,
              },
            },
          },
          orderBy: { scheduledDate: 'desc' },
        },
      },
    });

    if (!job) {
      throw createError('Job not found', 404);
    }

    // Parse JSON fields
    const jobWithParsedData = {
      ...job,
      requiredSkills: JSON.parse(job.requiredSkills),
      preferredSkills: job.preferredSkills ? JSON.parse(job.preferredSkills) : null,
      benefits: job.benefits ? JSON.parse(job.benefits) : null,
      preferredVendors: job.preferredVendors ? JSON.parse(job.preferredVendors) : null,
    };

    res.json({
      success: true,
      data: jobWithParsedData,
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/bulk-hiring/jobs/{id}/publish:
 *   patch:
 *     summary: Publish bulk hiring job
 *     tags: [Bulk Hiring]
 *     security:
 *       - bearerAuth: []
 */
router.patch('/jobs/:id/publish', authorize(UserRole.ADMIN, UserRole.HR_MANAGER), [
  param('id').isUUID(),
], async (req: AuthRequest, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createError('Validation failed', 400);
    }

    const { id } = req.params;

    const job = await prisma.bulkHiringJob.findUnique({
      where: { id },
    });

    if (!job) {
      throw createError('Job not found', 404);
    }

    if (job.status !== 'DRAFT') {
      throw createError('Only draft jobs can be published', 400);
    }

    const updatedJob = await prisma.bulkHiringJob.update({
      where: { id },
      data: {
        status: 'PUBLISHED',
        publishedBy: req.user!.id,
        publishedAt: new Date(),
      },
      include: {
        creator: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          },
        },
        publisher: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          },
        },
      },
    });

    logger.info('Bulk hiring job published', {
      jobId: id,
      publishedBy: req.user!.id,
    });

    res.json({
      success: true,
      data: updatedJob,
      message: 'Job published successfully',
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/bulk-hiring/applications:
 *   post:
 *     summary: Submit job application
 *     tags: [Job Applications]
 *     security:
 *       - bearerAuth: []
 */
router.post('/applications', [
  body('jobId').isUUID(),
  body('candidateName').isLength({ min: 1 }),
  body('candidateEmail').isEmail(),
  body('candidatePhone').isLength({ min: 10 }),
  body('totalExperience').optional().isFloat({ min: 0 }),
  body('currentSalary').optional().isFloat({ min: 0 }),
  body('expectedSalary').optional().isFloat({ min: 0 }),
], async (req: AuthRequest, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createError('Validation failed', 400);
    }

    const application = await prisma.jobApplication.create({
      data: req.body,
      include: {
        job: {
          select: {
            id: true,
            title: true,
            jobType: true,
          },
        },
      },
    });

    logger.info('Job application submitted', {
      applicationId: application.id,
      jobId: req.body.jobId,
      candidateEmail: req.body.candidateEmail,
    });

    res.status(201).json({
      success: true,
      data: application,
      message: 'Application submitted successfully',
    });
  } catch (error) {
    next(error);
  }
});

export default router;
