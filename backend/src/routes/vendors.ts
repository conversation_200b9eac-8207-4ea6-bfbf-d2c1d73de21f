import express from 'express';
import { body, validationResult } from 'express-validator';
import { prisma } from '../config/database';
import { createError } from '../middleware/errorHandler';
import { authenticate, authorize, AuthRequest } from '../middleware/auth';
import { UserRole, VendorStatus } from '@agentic-talent-pro/shared';

const router = express.Router();

// Apply authentication to all routes
router.use(authenticate);

/**
 * @swagger
 * /api/vendors:
 *   get:
 *     summary: Get all vendors
 *     tags: [Vendors]
 *     security:
 *       - bearerAuth: []
 */
router.get('/', authorize(UserRole.ADMIN, UserRole.HR_MANAGER), async (req: AuthRequest, res, next) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;

    const [vendors, total] = await Promise.all([
      prisma.vendor.findMany({
        skip,
        take: limit,
        include: {
          _count: {
            select: {
              resources: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
      }),
      prisma.vendor.count(),
    ]);

    res.json({
      success: true,
      data: vendors,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/vendors:
 *   post:
 *     summary: Create new vendor
 *     tags: [Vendors]
 *     security:
 *       - bearerAuth: []
 */
router.post('/', authorize(UserRole.ADMIN, UserRole.HR_MANAGER), [
  body('name').isLength({ min: 1 }),
  body('contactPerson').isLength({ min: 1 }),
  body('email').isEmail(),
  body('phone').isLength({ min: 10 }),
  body('address').isLength({ min: 1 }),
  body('panNumber').isLength({ min: 10, max: 10 }),
  body('bankDetails').isLength({ min: 1 }),
], async (req: AuthRequest, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createError('Validation failed', 400);
    }

    const vendor = await prisma.vendor.create({
      data: req.body,
    });

    res.status(201).json({
      success: true,
      data: vendor,
      message: 'Vendor created successfully',
    });
  } catch (error) {
    next(error);
  }
});

export default router;
