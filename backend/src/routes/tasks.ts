import express from 'express';
import { body, query, validationResult } from 'express-validator';
import { prisma } from '../config/database';
import { createError } from '../middleware/errorHandler';
import { authenticate, authorize, AuthRequest } from '../middleware/auth';
import { UserRole, TaskStatus, TaskPriority } from '@agentic-talent-pro/shared';

const router = express.Router();

// Apply authentication to all routes
router.use(authenticate);

/**
 * @swagger
 * /api/tasks:
 *   get:
 *     summary: Get all tasks
 *     tags: [Tasks]
 *     security:
 *       - bearerAuth: []
 */
router.get('/', async (req: AuthRequest, res, next) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;
    const projectId = req.query.projectId as string;
    const assigneeId = req.query.assigneeId as string;
    const status = req.query.status as TaskStatus;

    const where: any = {};
    
    if (projectId) where.projectId = projectId;
    if (assigneeId) where.assignedToId = assigneeId;
    if (status) where.status = status;

    // Filter based on user role
    if (req.user!.role === UserRole.RESOURCE) {
      where.assignedToId = req.user!.id;
    }

    const [tasks, total] = await Promise.all([
      prisma.task.findMany({
        where,
        skip,
        take: limit,
        include: {
          project: {
            select: {
              id: true,
              name: true,
            },
          },
          assignedTo: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
      }),
      prisma.task.count({ where }),
    ]);

    res.json({
      success: true,
      data: tasks,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/tasks:
 *   post:
 *     summary: Create new task
 *     tags: [Tasks]
 *     security:
 *       - bearerAuth: []
 */
router.post('/', authorize(UserRole.ADMIN, UserRole.PROJECT_MANAGER), [
  body('title').isLength({ min: 1 }),
  body('description').isLength({ min: 1 }),
  body('projectId').isUUID(),
  body('assignedToId').isUUID(),
  body('priority').isIn(Object.values(TaskPriority)),
  body('estimatedHours').isFloat({ min: 0 }),
  body('startDate').isISO8601(),
  body('dueDate').isISO8601(),
], async (req: AuthRequest, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createError('Validation failed', 400);
    }

    const {
      title,
      description,
      projectId,
      assignedToId,
      priority,
      estimatedHours,
      startDate,
      dueDate,
    } = req.body;

    // Verify project exists and user has access
    const project = await prisma.project.findUnique({
      where: { id: projectId },
    });

    if (!project) {
      throw createError('Project not found', 404);
    }

    if (req.user!.role === UserRole.PROJECT_MANAGER && project.managerId !== req.user!.id) {
      throw createError('Access denied', 403);
    }

    // Verify assignee exists and is assigned to project
    const assignee = await prisma.user.findUnique({
      where: { id: assignedToId },
    });

    if (!assignee) {
      throw createError('Assignee not found', 404);
    }

    const task = await prisma.task.create({
      data: {
        title,
        description,
        projectId,
        assignedToId,
        priority: priority as TaskPriority,
        estimatedHours,
        startDate: new Date(startDate),
        dueDate: new Date(dueDate),
      },
      include: {
        project: {
          select: {
            id: true,
            name: true,
          },
        },
        assignedTo: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });

    res.status(201).json({
      success: true,
      data: task,
      message: 'Task created successfully',
    });
  } catch (error) {
    next(error);
  }
});

export default router;
