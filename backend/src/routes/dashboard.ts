import express from 'express';
import { prisma } from '../config/database';
import { authenticate, AuthRequest } from '../middleware/auth';
import { UserRole } from '@agentic-talent-pro/shared';

const router = express.Router();

// Apply authentication to all routes
router.use(authenticate);

/**
 * @swagger
 * /api/dashboard/stats:
 *   get:
 *     summary: Get dashboard statistics
 *     tags: [Dashboard]
 *     security:
 *       - bearerAuth: []
 */
router.get('/stats', async (req: AuthRequest, res, next) => {
  try {
    const stats: any = {};

    // Get basic counts
    const [
      totalContracts,
      activeProjects,
      totalResources,
      pendingTimesheets,
      totalInvoices,
      unpaidInvoices,
    ] = await Promise.all([
      prisma.contract.count(),
      prisma.project.count({ where: { status: 'ACTIVE' } }),
      prisma.resource.count({ where: { status: 'AVAILABLE' } }),
      prisma.timesheet.count({ where: { status: 'SUBMITTED' } }),
      prisma.invoice.count(),
      prisma.invoice.count({ where: { status: { in: ['SENT', 'OVERDUE'] } } }),
    ]);

    stats.overview = {
      totalContracts,
      activeProjects,
      totalResources,
      pendingTimesheets,
      totalInvoices,
      unpaidInvoices,
    };

    // Role-specific stats
    if (req.user!.role === UserRole.PROJECT_MANAGER) {
      const myProjects = await prisma.project.count({
        where: { managerId: req.user!.id },
      });
      stats.myProjects = myProjects;
    }

    if (req.user!.role === UserRole.RESOURCE) {
      const resource = await prisma.resource.findUnique({
        where: { userId: req.user!.id },
      });
      
      if (resource) {
        const myTasks = await prisma.task.count({
          where: { assignedToId: req.user!.id },
        });
        const myTimesheets = await prisma.timesheet.count({
          where: { resourceId: resource.id },
        });
        stats.myTasks = myTasks;
        stats.myTimesheets = myTimesheets;
      }
    }

    res.json({
      success: true,
      data: stats,
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/dashboard/charts:
 *   get:
 *     summary: Get dashboard chart data
 *     tags: [Dashboard]
 *     security:
 *       - bearerAuth: []
 */
router.get('/charts', async (req: AuthRequest, res, next) => {
  try {
    const charts: any = {};

    // Project status distribution
    const projectStatusData = await prisma.project.groupBy({
      by: ['status'],
      _count: {
        status: true,
      },
    });

    charts.projectStatus = projectStatusData.map(item => ({
      status: item.status,
      count: item._count.status,
    }));

    // Resource utilization
    const resourceUtilization = await prisma.resource.groupBy({
      by: ['status'],
      _count: {
        status: true,
      },
    });

    charts.resourceUtilization = resourceUtilization.map(item => ({
      status: item.status,
      count: item._count.status,
    }));

    // Monthly invoice trends (last 6 months)
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    const monthlyInvoices = await prisma.invoice.findMany({
      where: {
        createdAt: {
          gte: sixMonthsAgo,
        },
      },
      select: {
        createdAt: true,
        total: true,
      },
    });

    // Group by month
    const monthlyData = monthlyInvoices.reduce((acc: any, invoice) => {
      const month = invoice.createdAt.toISOString().slice(0, 7); // YYYY-MM
      if (!acc[month]) {
        acc[month] = { month, total: 0, count: 0 };
      }
      acc[month].total += invoice.total;
      acc[month].count += 1;
      return acc;
    }, {});

    charts.monthlyInvoices = Object.values(monthlyData);

    res.json({
      success: true,
      data: charts,
    });
  } catch (error) {
    next(error);
  }
});

export default router;
