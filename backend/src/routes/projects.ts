import express from 'express';
import { body, query, validationResult } from 'express-validator';
import { prisma } from '../config/database';
import { createError } from '../middleware/errorHandler';
import { authenticate, authorize, AuthRequest } from '../middleware/auth';
import { UserRole, ProjectStatus } from '@agentic-talent-pro/shared';

const router = express.Router();

// Apply authentication to all routes
router.use(authenticate);

/**
 * @swagger
 * /api/projects:
 *   get:
 *     summary: Get all projects
 *     tags: [Projects]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [PLANNING, ACTIVE, ON_HOLD, COMPLETED, CANCELLED]
 *       - in: query
 *         name: managerId
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Projects retrieved successfully
 */
router.get('/', [
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('status').optional().isIn(Object.values(ProjectStatus)),
], async (req: AuthRequest, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createError('Validation failed', 400);
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;
    const status = req.query.status as ProjectStatus;
    const managerId = req.query.managerId as string;

    const where: any = {};
    
    if (status) {
      where.status = status;
    }
    
    if (managerId) {
      where.managerId = managerId;
    }

    // Filter based on user role
    if (req.user!.role === UserRole.RESOURCE) {
      // Resources can only see projects they're assigned to
      where.projectResources = {
        some: {
          resource: {
            userId: req.user!.id,
          },
        },
      };
    } else if (req.user!.role === UserRole.PROJECT_MANAGER) {
      // Project managers can see projects they manage
      where.managerId = req.user!.id;
    }

    const [projects, total] = await Promise.all([
      prisma.project.findMany({
        where,
        skip,
        take: limit,
        include: {
          contract: {
            select: {
              id: true,
              title: true,
              client: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true,
                },
              },
            },
          },
          manager: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          projectResources: {
            include: {
              resource: {
                include: {
                  user: {
                    select: {
                      id: true,
                      firstName: true,
                      lastName: true,
                      email: true,
                    },
                  },
                },
              },
            },
          },
          _count: {
            select: {
              tasks: true,
              timesheets: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
      }),
      prisma.project.count({ where }),
    ]);

    res.json({
      success: true,
      data: projects,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/projects/{id}:
 *   get:
 *     summary: Get project by ID
 *     tags: [Projects]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Project retrieved successfully
 *       404:
 *         description: Project not found
 */
router.get('/:id', async (req: AuthRequest, res, next) => {
  try {
    const { id } = req.params;

    const project = await prisma.project.findUnique({
      where: { id },
      include: {
        contract: {
          include: {
            client: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
                phone: true,
              },
            },
          },
        },
        manager: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        projectResources: {
          include: {
            resource: {
              include: {
                user: {
                  select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    email: true,
                  },
                },
                skills: {
                  include: {
                    skill: true,
                  },
                },
              },
            },
          },
        },
        resourcePlans: {
          include: {
            skill: true,
          },
        },
        tasks: {
          include: {
            assignedTo: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        },
        _count: {
          select: {
            timesheets: true,
            invoices: true,
          },
        },
      },
    });

    if (!project) {
      throw createError('Project not found', 404);
    }

    // Check access permissions
    const hasAccess = 
      req.user!.role === UserRole.ADMIN ||
      req.user!.role === UserRole.HR_MANAGER ||
      project.managerId === req.user!.id ||
      project.projectResources.some(pr => pr.resource.userId === req.user!.id);

    if (!hasAccess) {
      throw createError('Access denied', 403);
    }

    res.json({
      success: true,
      data: project,
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/projects:
 *   post:
 *     summary: Create new project
 *     tags: [Projects]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - description
 *               - contractId
 *               - managerId
 *               - startDate
 *               - endDate
 *               - budget
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               contractId:
 *                 type: string
 *               managerId:
 *                 type: string
 *               startDate:
 *                 type: string
 *                 format: date
 *               endDate:
 *                 type: string
 *                 format: date
 *               budget:
 *                 type: number
 *     responses:
 *       201:
 *         description: Project created successfully
 */
router.post('/', authorize(UserRole.ADMIN, UserRole.PROJECT_MANAGER), [
  body('name').isLength({ min: 1 }),
  body('description').isLength({ min: 1 }),
  body('contractId').isUUID(),
  body('managerId').isUUID(),
  body('startDate').isISO8601(),
  body('endDate').isISO8601(),
  body('budget').isFloat({ min: 0 }),
], async (req: AuthRequest, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createError('Validation failed', 400);
    }

    const {
      name,
      description,
      contractId,
      managerId,
      startDate,
      endDate,
      budget,
    } = req.body;

    // Verify contract exists and is active
    const contract = await prisma.contract.findUnique({
      where: { id: contractId },
    });

    if (!contract) {
      throw createError('Contract not found', 404);
    }

    if (contract.status !== 'ACTIVE') {
      throw createError('Contract must be active to create projects', 400);
    }

    // Verify manager exists and has correct role
    const manager = await prisma.user.findUnique({
      where: { id: managerId },
    });

    if (!manager || manager.role !== UserRole.PROJECT_MANAGER) {
      throw createError('Invalid manager ID', 400);
    }

    // Validate dates
    if (new Date(startDate) >= new Date(endDate)) {
      throw createError('End date must be after start date', 400);
    }

    const project = await prisma.project.create({
      data: {
        name,
        description,
        contractId,
        managerId,
        startDate: new Date(startDate),
        endDate: new Date(endDate),
        budget,
      },
      include: {
        contract: {
          include: {
            client: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        },
        manager: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });

    res.status(201).json({
      success: true,
      data: project,
      message: 'Project created successfully',
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/projects/{id}/resources:
 *   post:
 *     summary: Assign resource to project
 *     tags: [Projects]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - resourceId
 *               - startDate
 *             properties:
 *               resourceId:
 *                 type: string
 *               allocationPercent:
 *                 type: integer
 *                 default: 100
 *               startDate:
 *                 type: string
 *                 format: date
 *               endDate:
 *                 type: string
 *                 format: date
 *     responses:
 *       201:
 *         description: Resource assigned successfully
 */
router.post('/:id/resources', authorize(UserRole.ADMIN, UserRole.PROJECT_MANAGER), [
  body('resourceId').isUUID(),
  body('allocationPercent').optional().isInt({ min: 1, max: 100 }),
  body('startDate').isISO8601(),
  body('endDate').optional().isISO8601(),
], async (req: AuthRequest, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createError('Validation failed', 400);
    }

    const { id: projectId } = req.params;
    const { resourceId, allocationPercent = 100, startDate, endDate } = req.body;

    // Verify project exists and user has access
    const project = await prisma.project.findUnique({
      where: { id: projectId },
    });

    if (!project) {
      throw createError('Project not found', 404);
    }

    if (req.user!.role === UserRole.PROJECT_MANAGER && project.managerId !== req.user!.id) {
      throw createError('Access denied', 403);
    }

    // Verify resource exists and is available
    const resource = await prisma.resource.findUnique({
      where: { id: resourceId },
    });

    if (!resource) {
      throw createError('Resource not found', 404);
    }

    if (resource.status !== 'AVAILABLE') {
      throw createError('Resource is not available', 400);
    }

    // Check if resource is already assigned to this project
    const existingAssignment = await prisma.projectResource.findUnique({
      where: {
        projectId_resourceId: {
          projectId,
          resourceId,
        },
      },
    });

    if (existingAssignment) {
      throw createError('Resource is already assigned to this project', 409);
    }

    const projectResource = await prisma.projectResource.create({
      data: {
        projectId,
        resourceId,
        allocationPercent,
        startDate: new Date(startDate),
        endDate: endDate ? new Date(endDate) : undefined,
      },
      include: {
        resource: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        },
      },
    });

    res.status(201).json({
      success: true,
      data: projectResource,
      message: 'Resource assigned to project successfully',
    });
  } catch (error) {
    next(error);
  }
});

export default router;
