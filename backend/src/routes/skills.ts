import express from 'express';
import { body, validationResult } from 'express-validator';
import { prisma } from '../config/database';
import { createError } from '../middleware/errorHandler';
import { authenticate, authorize, AuthRequest } from '../middleware/auth';
import { UserRole } from '@agentic-talent-pro/shared';

const router = express.Router();

// Apply authentication to all routes
router.use(authenticate);

/**
 * @swagger
 * /api/skills:
 *   get:
 *     summary: Get all skills
 *     tags: [Skills]
 *     security:
 *       - bearerAuth: []
 */
router.get('/', async (req: AuthRequest, res, next) => {
  try {
    const category = req.query.category as string;
    const where = category ? { category } : {};

    const skills = await prisma.skill.findMany({
      where,
      orderBy: { name: 'asc' },
    });

    res.json({
      success: true,
      data: skills,
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/skills:
 *   post:
 *     summary: Create new skill
 *     tags: [Skills]
 *     security:
 *       - bearerAuth: []
 */
router.post('/', authorize(UserRole.ADMIN, UserRole.HR_MANAGER), [
  body('name').isLength({ min: 1 }),
  body('category').isLength({ min: 1 }),
  body('description').optional().isLength({ min: 1 }),
], async (req: AuthRequest, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createError('Validation failed', 400);
    }

    const skill = await prisma.skill.create({
      data: req.body,
    });

    res.status(201).json({
      success: true,
      data: skill,
      message: 'Skill created successfully',
    });
  } catch (error) {
    next(error);
  }
});

export default router;
