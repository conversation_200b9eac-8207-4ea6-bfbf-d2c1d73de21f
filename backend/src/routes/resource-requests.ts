import express from 'express';
import { body, param, query, validationResult } from 'express-validator';
import { prisma } from '../config/database';
import { createError } from '../middleware/errorHandler';
import { authorize, AuthRequest } from '../middleware/auth';
import { UserRole } from '@agentic-talent-pro/shared';
import { logger } from '../utils/logger';

const router = express.Router();

/**
 * @swagger
 * /api/resource-requests:
 *   get:
 *     summary: Get all resource requests
 *     tags: [Resource Requests]
 *     security:
 *       - bearerAuth: []
 */
router.get('/', authorize(UserRole.ADMIN, UserRole.PROJECT_MANAGER, UserRole.HR_MANAGER), [
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('status').optional().isIn(['OPEN', 'IN_PROGRESS', 'FULFILLED', 'CANCELLED']),
  query('priority').optional().isIn(['LOW', 'MEDIUM', 'HIGH', 'URGENT']),
  query('assignedTo').optional().isUUID(),
], async (req: AuthRequest, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createError('Validation failed', 400);
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;
    const { status, priority, assignedTo } = req.query;

    const where: any = {};
    if (status) where.status = status;
    if (priority) where.priority = priority;
    if (assignedTo) where.assignedTo = assignedTo;

    // Project managers can only see requests for their projects
    if (req.user!.role === UserRole.PROJECT_MANAGER) {
      where.resourcePlan = {
        project: {
          managerId: req.user!.id,
        },
      };
    }

    // HR managers can see all requests or only assigned to them
    if (req.user!.role === UserRole.HR_MANAGER && !assignedTo) {
      where.OR = [
        { assignedTo: req.user!.id },
        { assignedTo: null },
      ];
    }

    const [resourceRequests, total] = await Promise.all([
      prisma.resourceRequest.findMany({
        where,
        skip,
        take: limit,
        include: {
          resourcePlan: {
            include: {
              project: {
                select: {
                  id: true,
                  name: true,
                  manager: {
                    select: {
                      id: true,
                      firstName: true,
                      lastName: true,
                    },
                  },
                },
              },
              skill: true,
            },
          },
          requester: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          assignee: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
        orderBy: [
          { priority: 'desc' },
          { createdAt: 'desc' },
        ],
      }),
      prisma.resourceRequest.count({ where }),
    ]);

    res.json({
      success: true,
      data: resourceRequests,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/resource-requests/{id}:
 *   get:
 *     summary: Get resource request by ID
 *     tags: [Resource Requests]
 *     security:
 *       - bearerAuth: []
 */
router.get('/:id', authorize(UserRole.ADMIN, UserRole.PROJECT_MANAGER, UserRole.HR_MANAGER), [
  param('id').isUUID(),
], async (req: AuthRequest, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createError('Validation failed', 400);
    }

    const { id } = req.params;

    const resourceRequest = await prisma.resourceRequest.findUnique({
      where: { id },
      include: {
        resourcePlan: {
          include: {
            project: {
              include: {
                manager: {
                  select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    email: true,
                  },
                },
              },
            },
            skill: true,
          },
        },
        requester: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        assignee: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });

    if (!resourceRequest) {
      throw createError('Resource request not found', 404);
    }

    // Check access permissions
    if (req.user!.role === UserRole.PROJECT_MANAGER && 
        resourceRequest.resourcePlan.project.managerId !== req.user!.id) {
      throw createError('Access denied', 403);
    }

    if (req.user!.role === UserRole.HR_MANAGER && 
        resourceRequest.assignedTo !== req.user!.id && 
        resourceRequest.assignedTo !== null) {
      throw createError('Access denied', 403);
    }

    res.json({
      success: true,
      data: resourceRequest,
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/resource-requests:
 *   post:
 *     summary: Create new resource request
 *     tags: [Resource Requests]
 *     security:
 *       - bearerAuth: []
 */
router.post('/', authorize(UserRole.ADMIN, UserRole.PROJECT_MANAGER), [
  body('resourcePlanId').isUUID(),
  body('title').isLength({ min: 1 }),
  body('description').isLength({ min: 1 }),
  body('jobDescription').isLength({ min: 1 }),
  body('requiredSkills').isArray(),
  body('minExperience').optional().isInt({ min: 0 }),
  body('maxBudget').optional().isFloat({ min: 0 }),
  body('priority').optional().isIn(['LOW', 'MEDIUM', 'HIGH', 'URGENT']),
  body('expectedDate').optional().isISO8601(),
], async (req: AuthRequest, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createError('Validation failed', 400);
    }

    const {
      resourcePlanId,
      title,
      description,
      jobDescription,
      requiredSkills,
      minExperience = 0,
      maxBudget,
      priority = 'MEDIUM',
      expectedDate,
    } = req.body;

    // Verify resource plan exists and user has access
    const resourcePlan = await prisma.resourcePlan.findUnique({
      where: { id: resourcePlanId },
      include: {
        project: true,
      },
    });

    if (!resourcePlan) {
      throw createError('Resource plan not found', 404);
    }

    if (req.user!.role === UserRole.PROJECT_MANAGER && 
        resourcePlan.project.managerId !== req.user!.id) {
      throw createError('Access denied', 403);
    }

    const resourceRequest = await prisma.resourceRequest.create({
      data: {
        resourcePlanId,
        title,
        description,
        jobDescription,
        requiredSkills: JSON.stringify(requiredSkills),
        minExperience,
        maxBudget,
        priority,
        requestedBy: req.user!.id,
        expectedDate: expectedDate ? new Date(expectedDate) : undefined,
      },
      include: {
        resourcePlan: {
          include: {
            project: {
              select: {
                id: true,
                name: true,
              },
            },
            skill: true,
          },
        },
        requester: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          },
        },
      },
    });

    logger.info('Resource request created', {
      resourceRequestId: resourceRequest.id,
      resourcePlanId,
      requestedBy: req.user!.id,
      priority,
    });

    res.status(201).json({
      success: true,
      data: resourceRequest,
      message: 'Resource request created successfully',
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/resource-requests/{id}:
 *   put:
 *     summary: Update resource request
 *     tags: [Resource Requests]
 *     security:
 *       - bearerAuth: []
 */
router.put('/:id', authorize(UserRole.ADMIN, UserRole.PROJECT_MANAGER, UserRole.HR_MANAGER), [
  param('id').isUUID(),
  body('title').optional().isLength({ min: 1 }),
  body('description').optional().isLength({ min: 1 }),
  body('jobDescription').optional().isLength({ min: 1 }),
  body('requiredSkills').optional().isArray(),
  body('minExperience').optional().isInt({ min: 0 }),
  body('maxBudget').optional().isFloat({ min: 0 }),
  body('priority').optional().isIn(['LOW', 'MEDIUM', 'HIGH', 'URGENT']),
  body('status').optional().isIn(['OPEN', 'IN_PROGRESS', 'FULFILLED', 'CANCELLED']),
  body('assignedTo').optional().isUUID(),
  body('expectedDate').optional().isISO8601(),
  body('rejectionReason').optional().isLength({ min: 1 }),
], async (req: AuthRequest, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createError('Validation failed', 400);
    }

    const { id } = req.params;
    const updateData = { ...req.body };

    // Get existing request
    const existingRequest = await prisma.resourceRequest.findUnique({
      where: { id },
      include: {
        resourcePlan: {
          include: {
            project: true,
          },
        },
      },
    });

    if (!existingRequest) {
      throw createError('Resource request not found', 404);
    }

    // Check access permissions
    if (req.user!.role === UserRole.PROJECT_MANAGER && 
        existingRequest.resourcePlan.project.managerId !== req.user!.id) {
      throw createError('Access denied', 403);
    }

    // Convert arrays and dates
    if (updateData.requiredSkills) {
      updateData.requiredSkills = JSON.stringify(updateData.requiredSkills);
    }
    if (updateData.expectedDate) {
      updateData.expectedDate = new Date(updateData.expectedDate);
    }

    // Set fulfilled date if status is being changed to FULFILLED
    if (updateData.status === 'FULFILLED' && existingRequest.status !== 'FULFILLED') {
      updateData.fulfilledDate = new Date();
    }

    const resourceRequest = await prisma.resourceRequest.update({
      where: { id },
      data: updateData,
      include: {
        resourcePlan: {
          include: {
            project: {
              select: {
                id: true,
                name: true,
              },
            },
            skill: true,
          },
        },
        requester: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          },
        },
        assignee: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          },
        },
      },
    });

    logger.info('Resource request updated', {
      resourceRequestId: id,
      updatedBy: req.user!.id,
      changes: Object.keys(req.body),
    });

    res.json({
      success: true,
      data: resourceRequest,
      message: 'Resource request updated successfully',
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/resource-requests/{id}/assign:
 *   post:
 *     summary: Assign resource request to HR manager
 *     tags: [Resource Requests]
 *     security:
 *       - bearerAuth: []
 */
router.post('/:id/assign', authorize(UserRole.ADMIN, UserRole.HR_MANAGER), [
  param('id').isUUID(),
  body('assignedTo').isUUID(),
], async (req: AuthRequest, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createError('Validation failed', 400);
    }

    const { id } = req.params;
    const { assignedTo } = req.body;

    // Verify assignee is HR manager
    const assignee = await prisma.user.findUnique({
      where: { id: assignedTo },
    });

    if (!assignee || assignee.role !== UserRole.HR_MANAGER) {
      throw createError('Assignee must be an HR manager', 400);
    }

    const resourceRequest = await prisma.resourceRequest.update({
      where: { id },
      data: {
        assignedTo,
        status: 'IN_PROGRESS',
      },
      include: {
        resourcePlan: {
          include: {
            project: {
              select: {
                id: true,
                name: true,
              },
            },
            skill: true,
          },
        },
        requester: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          },
        },
        assignee: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          },
        },
      },
    });

    logger.info('Resource request assigned', {
      resourceRequestId: id,
      assignedTo,
      assignedBy: req.user!.id,
    });

    res.json({
      success: true,
      data: resourceRequest,
      message: 'Resource request assigned successfully',
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/resource-requests/{id}/fulfill:
 *   post:
 *     summary: Mark resource request as fulfilled
 *     tags: [Resource Requests]
 *     security:
 *       - bearerAuth: []
 */
router.post('/:id/fulfill', authorize(UserRole.ADMIN, UserRole.HR_MANAGER), [
  param('id').isUUID(),
  body('resourceId').optional().isUUID(),
  body('notes').optional().isLength({ min: 1 }),
], async (req: AuthRequest, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createError('Validation failed', 400);
    }

    const { id } = req.params;
    const { resourceId, notes } = req.body;

    // Get the request
    const existingRequest = await prisma.resourceRequest.findUnique({
      where: { id },
      include: {
        resourcePlan: true,
      },
    });

    if (!existingRequest) {
      throw createError('Resource request not found', 404);
    }

    // Check if user can fulfill this request
    if (req.user!.role === UserRole.HR_MANAGER &&
        existingRequest.assignedTo !== req.user!.id) {
      throw createError('You can only fulfill requests assigned to you', 403);
    }

    const updateData: any = {
      status: 'FULFILLED',
      fulfilledDate: new Date(),
    };

    if (notes) {
      updateData.description = existingRequest.description + '\n\nFulfillment Notes: ' + notes;
    }

    const resourceRequest = await prisma.resourceRequest.update({
      where: { id },
      data: updateData,
      include: {
        resourcePlan: {
          include: {
            project: {
              select: {
                id: true,
                name: true,
              },
            },
            skill: true,
          },
        },
        requester: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          },
        },
        assignee: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          },
        },
      },
    });

    // If a resource was provided, create allocation
    if (resourceId) {
      await prisma.planAllocation.create({
        data: {
          resourcePlanId: existingRequest.resourcePlanId,
          resourceId,
          allocationPercent: existingRequest.resourcePlan.allocationPercent,
          startDate: existingRequest.resourcePlan.startDate,
          endDate: existingRequest.resourcePlan.endDate,
          isConfirmed: true,
        },
      });
    }

    logger.info('Resource request fulfilled', {
      resourceRequestId: id,
      fulfilledBy: req.user!.id,
      resourceId,
    });

    res.json({
      success: true,
      data: resourceRequest,
      message: 'Resource request fulfilled successfully',
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/resource-requests/{id}:
 *   delete:
 *     summary: Delete resource request
 *     tags: [Resource Requests]
 *     security:
 *       - bearerAuth: []
 */
router.delete('/:id', authorize(UserRole.ADMIN, UserRole.PROJECT_MANAGER), [
  param('id').isUUID(),
], async (req: AuthRequest, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createError('Validation failed', 400);
    }

    const { id } = req.params;

    // Get the request to check permissions
    const existingRequest = await prisma.resourceRequest.findUnique({
      where: { id },
      include: {
        resourcePlan: {
          include: {
            project: true,
          },
        },
      },
    });

    if (!existingRequest) {
      throw createError('Resource request not found', 404);
    }

    // Check access permissions
    if (req.user!.role === UserRole.PROJECT_MANAGER &&
        existingRequest.resourcePlan.project.managerId !== req.user!.id) {
      throw createError('Access denied', 403);
    }

    // Don't allow deletion of fulfilled requests
    if (existingRequest.status === 'FULFILLED') {
      throw createError('Cannot delete fulfilled resource requests', 400);
    }

    await prisma.resourceRequest.delete({
      where: { id },
    });

    logger.info('Resource request deleted', {
      resourceRequestId: id,
      deletedBy: req.user!.id,
    });

    res.json({
      success: true,
      message: 'Resource request deleted successfully',
    });
  } catch (error) {
    next(error);
  }
});

export default router;
