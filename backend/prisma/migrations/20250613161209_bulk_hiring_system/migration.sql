-- Create<PERSON><PERSON>
CREATE TYPE "JobType" AS ENUM ('<PERSON><PERSON><PERSON>_COLLAR', 'WHITE_COLLAR');

-- CreateEnum
CREATE TYPE "HiringType" AS ENUM ('BULK', 'ADHOC');

-- Create<PERSON>num
CREATE TYPE "JobPriority" AS ENUM ('LOW', 'MEDIUM', 'HIGH', 'URGENT');

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "JobStatus" AS ENUM ('DRAFT', 'PUBLISHED', 'ACTIVE', 'ON_HOLD', 'CLOSED', 'CANCELLED');

-- CreateEnum
CREATE TYPE "ApplicationStatus" AS ENUM ('APPLIED', 'SCREENING', 'SHORTLISTED', 'INTERVIEW_SCHEDULED', 'INTERVIEWED', 'SELECTED', 'REJECTED', 'OFFER_EXTENDED', 'OFFER_ACCEPTED', 'OFFER_DECLINED', 'JOINED', 'WITHDRAWN');

-- C<PERSON><PERSON><PERSON>
CREATE TYPE "InterviewType" AS ENUM ('PHONE_SCREENING', 'TECHNICAL', 'HR_ROUND', 'MANAGERIAL', 'FINAL', 'GROUP_DISCUSSION');

-- CreateEnum
CREATE TYPE "InterviewStatus" AS ENUM ('SCHEDULED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'NO_SHOW');

-- CreateTable
CREATE TABLE "bulk_hiring_jobs" (
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "jobType" "JobType" NOT NULL,
    "hiringType" "HiringType" NOT NULL,
    "priority" "JobPriority" NOT NULL DEFAULT 'MEDIUM',
    "status" "JobStatus" NOT NULL DEFAULT 'DRAFT',
    "department" TEXT NOT NULL,
    "location" TEXT NOT NULL,
    "positions" INTEGER NOT NULL,
    "filledPositions" INTEGER NOT NULL DEFAULT 0,
    "minExperience" INTEGER NOT NULL DEFAULT 0,
    "maxExperience" INTEGER,
    "minEducation" TEXT,
    "requiredSkills" TEXT NOT NULL,
    "preferredSkills" TEXT,
    "employmentType" "EmploymentType" NOT NULL,
    "workShift" TEXT,
    "workMode" TEXT,
    "minSalary" DOUBLE PRECISION,
    "maxSalary" DOUBLE PRECISION,
    "currency" TEXT NOT NULL DEFAULT 'USD',
    "benefits" TEXT,
    "applicationDeadline" TIMESTAMP(3),
    "expectedJoiningDate" TIMESTAMP(3),
    "sourceVendor" BOOLEAN NOT NULL DEFAULT false,
    "preferredVendors" TEXT,
    "directHiring" BOOLEAN NOT NULL DEFAULT true,
    "approvedBy" TEXT,
    "approvedAt" TIMESTAMP(3),
    "publishedBy" TEXT,
    "publishedAt" TIMESTAMP(3),
    "createdBy" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "bulk_hiring_jobs_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "job_applications" (
    "id" TEXT NOT NULL,
    "jobId" TEXT NOT NULL,
    "candidateName" TEXT NOT NULL,
    "candidateEmail" TEXT NOT NULL,
    "candidatePhone" TEXT NOT NULL,
    "resumeUrl" TEXT,
    "currentLocation" TEXT,
    "preferredLocation" TEXT,
    "currentSalary" DOUBLE PRECISION,
    "expectedSalary" DOUBLE PRECISION,
    "noticePeriod" INTEGER,
    "totalExperience" DOUBLE PRECISION,
    "relevantExperience" DOUBLE PRECISION,
    "coverLetter" TEXT,
    "portfolioUrl" TEXT,
    "linkedinUrl" TEXT,
    "status" "ApplicationStatus" NOT NULL DEFAULT 'APPLIED',
    "screeningScore" DOUBLE PRECISION,
    "screeningNotes" TEXT,
    "interviewScheduled" BOOLEAN NOT NULL DEFAULT false,
    "interviewDate" TIMESTAMP(3),
    "interviewFeedback" TEXT,
    "interviewScore" DOUBLE PRECISION,
    "selectedBy" TEXT,
    "selectedAt" TIMESTAMP(3),
    "rejectionReason" TEXT,
    "offerAmount" DOUBLE PRECISION,
    "offerCurrency" TEXT,
    "offerExtendedAt" TIMESTAMP(3),
    "offerAcceptedAt" TIMESTAMP(3),
    "joiningDate" TIMESTAMP(3),
    "actualJoiningDate" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "job_applications_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "interviews" (
    "id" TEXT NOT NULL,
    "jobId" TEXT NOT NULL,
    "applicationId" TEXT NOT NULL,
    "type" "InterviewType" NOT NULL,
    "status" "InterviewStatus" NOT NULL DEFAULT 'SCHEDULED',
    "scheduledDate" TIMESTAMP(3) NOT NULL,
    "duration" INTEGER NOT NULL DEFAULT 60,
    "location" TEXT,
    "meetingLink" TEXT,
    "interviewerId" TEXT NOT NULL,
    "panelMembers" TEXT,
    "feedback" TEXT,
    "score" DOUBLE PRECISION,
    "recommendation" TEXT,
    "technicalScore" DOUBLE PRECISION,
    "codingScore" DOUBLE PRECISION,
    "problemSolving" DOUBLE PRECISION,
    "communication" DOUBLE PRECISION,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "interviews_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "bulk_hiring_jobs" ADD CONSTRAINT "bulk_hiring_jobs_createdBy_fkey" FOREIGN KEY ("createdBy") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "bulk_hiring_jobs" ADD CONSTRAINT "bulk_hiring_jobs_approvedBy_fkey" FOREIGN KEY ("approvedBy") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "bulk_hiring_jobs" ADD CONSTRAINT "bulk_hiring_jobs_publishedBy_fkey" FOREIGN KEY ("publishedBy") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "job_applications" ADD CONSTRAINT "job_applications_jobId_fkey" FOREIGN KEY ("jobId") REFERENCES "bulk_hiring_jobs"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "job_applications" ADD CONSTRAINT "job_applications_selectedBy_fkey" FOREIGN KEY ("selectedBy") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "interviews" ADD CONSTRAINT "interviews_jobId_fkey" FOREIGN KEY ("jobId") REFERENCES "bulk_hiring_jobs"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "interviews" ADD CONSTRAINT "interviews_applicationId_fkey" FOREIGN KEY ("applicationId") REFERENCES "job_applications"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "interviews" ADD CONSTRAINT "interviews_interviewerId_fkey" FOREIGN KEY ("interviewerId") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
