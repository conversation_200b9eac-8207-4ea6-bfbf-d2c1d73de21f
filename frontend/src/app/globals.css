@tailwind base;
@tailwind components;
@tailwind utilities;

/* Oracle Redwood Design System */
@layer base {
  :root {
    /* Oracle Redwood Primary Colors - Red */
    --oj-palette-red-50: #fef2f2;
    --oj-palette-red-100: #fee2e2;
    --oj-palette-red-200: #fecaca;
    --oj-palette-red-300: #fca5a5;
    --oj-palette-red-400: #f87171;
    --oj-palette-red-500: #ef4444;
    --oj-palette-red-600: #dc2626;
    --oj-palette-red-700: #b91c1c;
    --oj-palette-red-800: #991b1b;
    --oj-palette-red-900: #7f1d1d;

    /* Oracle Redwood Neutral Colors */
    --oj-palette-neutral-0: #ffffff;
    --oj-palette-neutral-50: #fafafa;
    --oj-palette-neutral-100: #f5f5f5;
    --oj-palette-neutral-200: #e5e5e5;
    --oj-palette-neutral-300: #d4d4d4;
    --oj-palette-neutral-400: #a3a3a3;
    --oj-palette-neutral-500: #737373;
    --oj-palette-neutral-600: #525252;
    --oj-palette-neutral-700: #404040;
    --oj-palette-neutral-800: #262626;
    --oj-palette-neutral-900: #171717;

    /* Oracle Redwood Theme Variables */
    --background: 0 0% 100%;
    --foreground: 0 0% 9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 9%;
    --primary: 0 84% 43%; /* Red-600 */
    --primary-foreground: 0 0% 100%;
    --secondary: 0 0% 96%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96%;
    --muted-foreground: 0 0% 45%;
    --accent: 0 0% 96%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 90%;
    --input: 0 0% 90%;
    --ring: 0 84% 43%;
    --radius: 0.375rem;
  }

  .dark {
    --background: 0 0% 9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 15%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 15%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 84% 60%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 25%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 25%;
    --muted-foreground: 0 0% 64%;
    --accent: 0 0% 25%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 25%;
    --input: 0 0% 25%;
    --ring: 0 84% 60%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: 'Oracle Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }
  h1, h2, h3, h4, h5, h6 {
    font-family: 'Oracle Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-weight: 600;
  }
}

/* Oracle Redwood Components */
@layer components {
  .redwood-card {
    @apply bg-white border border-neutral-200 rounded-lg shadow-sm hover:shadow-md transition-shadow;
  }

  .redwood-button-primary {
    @apply bg-red-600 hover:bg-red-700 active:bg-red-800 text-white font-medium px-4 py-2 rounded-md transition-all duration-200 focus:ring-2 focus:ring-red-500 focus:ring-offset-2;
  }

  .redwood-button-secondary {
    @apply bg-neutral-100 hover:bg-neutral-200 active:bg-neutral-300 text-neutral-900 font-medium px-4 py-2 rounded-md transition-all duration-200 focus:ring-2 focus:ring-neutral-500 focus:ring-offset-2;
  }

  .redwood-input {
    @apply border border-neutral-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-all duration-200 placeholder:text-neutral-400;
  }

  .redwood-sidebar {
    @apply bg-neutral-50 border-r border-neutral-200;
  }

  .redwood-nav-item {
    @apply flex items-center px-3 py-2 text-sm font-medium rounded-md transition-all duration-200;
  }

  .redwood-nav-item-active {
    @apply bg-red-50 text-red-700 border-r-2 border-red-600 font-semibold;
  }

  .redwood-nav-item-inactive {
    @apply text-neutral-600 hover:bg-neutral-100 hover:text-neutral-900;
  }

  .redwood-header {
    @apply bg-white border-b border-neutral-200 shadow-sm;
  }

  .redwood-content {
    @apply bg-neutral-50 min-h-screen;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-300 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400;
}

/* Calendar styles */
.fc {
  font-family: inherit;
}

.fc-theme-standard td,
.fc-theme-standard th {
  border-color: hsl(var(--border));
}

.fc-button-primary {
  background-color: hsl(var(--primary));
  border-color: hsl(var(--primary));
}

.fc-button-primary:hover {
  background-color: hsl(var(--primary) / 0.9);
  border-color: hsl(var(--primary) / 0.9);
}

.fc-event {
  border: none;
  border-radius: 4px;
}

/* Loading spinner */
.spinner {
  border: 2px solid hsl(var(--muted));
  border-top: 2px solid hsl(var(--primary));
  border-radius: 50%;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Form styles */
.form-error {
  @apply text-sm text-destructive mt-1;
}

/* Table styles */
.table-container {
  @apply overflow-x-auto border rounded-lg;
}

.table-container table {
  @apply w-full border-collapse;
}

.table-container th {
  @apply bg-muted px-4 py-3 text-left text-sm font-medium text-muted-foreground border-b;
}

.table-container td {
  @apply px-4 py-3 text-sm border-b;
}

.table-container tr:hover {
  @apply bg-muted/50;
}

/* Oracle Redwood Status badges */
.status-badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.status-active {
  @apply bg-green-50 text-green-700 border border-green-200;
}

.status-inactive {
  @apply bg-neutral-100 text-neutral-700 border border-neutral-200;
}

.status-pending {
  @apply bg-yellow-50 text-yellow-700 border border-yellow-200;
}

.status-completed {
  @apply bg-blue-50 text-blue-700 border border-blue-200;
}

.status-cancelled {
  @apply bg-red-50 text-red-700 border border-red-200;
}

.status-draft {
  @apply bg-neutral-50 text-neutral-600 border border-neutral-200;
}

.status-terminated {
  @apply bg-red-50 text-red-700 border border-red-200;
}

.status-available {
  @apply bg-green-50 text-green-700 border border-green-200;
}

.status-allocated {
  @apply bg-blue-50 text-blue-700 border border-blue-200;
}

.status-on-hold {
  @apply bg-yellow-50 text-yellow-700 border border-yellow-200;
}

.status-planning {
  @apply bg-purple-50 text-purple-700 border border-purple-200;
}

/* Priority badges */
.priority-low {
  @apply bg-green-50 text-green-700 border border-green-200;
}

.priority-medium {
  @apply bg-yellow-50 text-yellow-700 border border-yellow-200;
}

.priority-high {
  @apply bg-orange-50 text-orange-700 border border-orange-200;
}

.priority-critical {
  @apply bg-red-50 text-red-700 border border-red-200;
}

/* Priority badges */
.priority-low {
  @apply bg-gray-100 text-gray-800;
}

.priority-medium {
  @apply bg-yellow-100 text-yellow-800;
}

.priority-high {
  @apply bg-orange-100 text-orange-800;
}

.priority-critical {
  @apply bg-red-100 text-red-800;
}
