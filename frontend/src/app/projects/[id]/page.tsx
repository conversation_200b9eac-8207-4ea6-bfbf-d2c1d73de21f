'use client';

import { useQuery } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { projectsApi } from '@/lib/api';
import { useAuth } from '@/hooks/useAuth';
import { formatDate, formatCurrency, getStatusColor } from '@/lib/utils';
import { ResourcePlanningTab } from '@/components/project/resource-planning-tab';
import { ArrowLeft, Edit, Users, Calendar, DollarSign, FileText, Clock, CheckCircle, Target, Plus, CheckSquare, Eye } from 'lucide-react';

interface ProjectDetailPageProps {
  params: {
    id: string;
  };
}

export default function ProjectDetailPage({ params }: ProjectDetailPageProps) {
  const router = useRouter();
  const { hasRole } = useAuth();
  const { id } = params;

  const { data: projectData, isLoading, error } = useQuery({
    queryKey: ['project', id],
    queryFn: () => projectsApi.getById(id),
  });

  const project = projectData?.data;

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="spinner"></div>
        </div>
      </DashboardLayout>
    );
  }

  if (error || !project) {
    return (
      <DashboardLayout>
        <div className="text-center py-12">
          <p className="text-gray-500 mb-4">Project not found</p>
          <Button onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Go Back
          </Button>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              onClick={() => router.back()}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">{project.name}</h1>
              <p className="text-gray-600">{project.contract?.title}</p>
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <Badge className={getStatusColor(project.status)}>
              {project.status}
            </Badge>
            {hasRole(['ADMIN', 'PROJECT_MANAGER']) && (
              <Button
                onClick={() => router.push(`/projects/${id}/edit`)}
                className="bg-red-600 hover:bg-red-700"
              >
                <Edit className="h-4 w-4 mr-2" />
                Edit Project
              </Button>
            )}
          </div>
        </div>

        {/* Project Overview */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <DollarSign className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Budget</p>
                  <p className="text-2xl font-bold">{formatCurrency(project.budget)}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Calendar className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Duration</p>
                  <p className="text-2xl font-bold">
                    {Math.ceil((new Date(project.endDate).getTime() - new Date(project.startDate).getTime()) / (1000 * 60 * 60 * 24))} days
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Users className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Team Size</p>
                  <p className="text-2xl font-bold">{project.projectResources?.length || 0}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Project Tabs */}
        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview" className="flex items-center space-x-2">
              <FileText className="h-4 w-4" />
              <span>Overview</span>
            </TabsTrigger>
            <TabsTrigger value="resource-planning" className="flex items-center space-x-2">
              <Target className="h-4 w-4" />
              <span>Resource Planning</span>
            </TabsTrigger>
            <TabsTrigger value="team" className="flex items-center space-x-2">
              <Users className="h-4 w-4" />
              <span>Team</span>
            </TabsTrigger>
            <TabsTrigger value="tasks" className="flex items-center space-x-2">
              <CheckCircle className="h-4 w-4" />
              <span>Tasks</span>
            </TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            {/* Project Details */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Basic Information */}
              <Card>
                <CardHeader>
                  <CardTitle>Project Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">Description</label>
                    <p className="mt-1 text-gray-900">{project.description}</p>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-600">Start Date</label>
                      <p className="mt-1 text-gray-900">{formatDate(project.startDate)}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">End Date</label>
                      <p className="mt-1 text-gray-900">{formatDate(project.endDate)}</p>
                    </div>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-gray-600">Project Manager</label>
                    <p className="mt-1 text-gray-900">
                      {project.manager?.firstName} {project.manager?.lastName}
                    </p>
                    <p className="text-sm text-gray-500">{project.manager?.email}</p>
                  </div>
                </CardContent>
              </Card>

              {/* Contract Information */}
              <Card>
                <CardHeader>
                  <CardTitle>Contract Details</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">Contract Title</label>
                    <p className="mt-1 text-gray-900">{project.contract?.title}</p>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-gray-600">Client</label>
                    <p className="mt-1 text-gray-900">
                      {project.contract?.client?.firstName} {project.contract?.client?.lastName}
                    </p>
                    <p className="text-sm text-gray-500">{project.contract?.client?.email}</p>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-gray-600">Contract Value</label>
                    <p className="mt-1 text-gray-900">{formatCurrency(project.contract?.value || 0)}</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Resource Planning Tab */}
          <TabsContent value="resource-planning">
            <ResourcePlanningTab project={project} />
          </TabsContent>

          {/* Team Tab */}
          <TabsContent value="team">
            <Card>
              <CardHeader>
                <CardTitle>Team Members</CardTitle>
                <p className="text-sm text-gray-600">
                  Team members are allocated through Resource Plans. Create resource plans first, then match and allocate resources.
                </p>
              </CardHeader>
              <CardContent>
                {project.projectResources && project.projectResources.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {project.projectResources.map((pr: any) => (
                      <div key={pr.id} className="border rounded-lg p-4">
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                            <span className="text-sm font-medium text-red-600">
                              {pr.resource?.user?.firstName?.[0]}{pr.resource?.user?.lastName?.[0]}
                            </span>
                          </div>
                          <div>
                            <p className="font-medium">
                              {pr.resource?.user?.firstName} {pr.resource?.user?.lastName}
                            </p>
                            <p className="text-sm text-gray-500">{pr.resource?.user?.email}</p>
                            <p className="text-xs text-gray-400">
                              {pr.allocationPercent}% allocation
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No Team Members Yet</h3>
                    <p className="text-gray-600 mb-4">
                      Team members are allocated through the Resource Planning process.
                    </p>
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 max-w-md mx-auto">
                      <h4 className="font-medium text-blue-900 mb-2">How to Add Team Members:</h4>
                      <ol className="text-sm text-blue-800 space-y-1 text-left">
                        <li>1. Go to Resource Planning tab</li>
                        <li>2. Create resource plans for needed roles</li>
                        <li>3. Match and allocate available resources</li>
                        <li>4. Or create job requests for hiring</li>
                      </ol>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Tasks Tab */}
          <TabsContent value="tasks">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle>Tasks Overview</CardTitle>
                  <p className="text-sm text-gray-600 mt-1">
                    Manage project tasks and track progress
                  </p>
                </div>
                {hasRole(['ADMIN', 'PROJECT_MANAGER']) && (
                  <Button
                    className="bg-red-600 hover:bg-red-700"
                    onClick={() => router.push(`/tasks/new?projectId=${project.id}`)}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Create Task
                  </Button>
                )}
              </CardHeader>
              <CardContent>
                {project.tasks && project.tasks.length > 0 ? (
                  <div className="space-y-6">
                    {/* Task Statistics */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="text-center p-4 bg-gray-50 rounded-lg">
                        <p className="text-2xl font-bold text-gray-900">{project.tasks.length}</p>
                        <p className="text-sm text-gray-600">Total Tasks</p>
                      </div>
                      <div className="text-center p-4 bg-blue-50 rounded-lg">
                        <p className="text-2xl font-bold text-blue-600">
                          {project.tasks.filter((t: any) => t.status === 'IN_PROGRESS').length}
                        </p>
                        <p className="text-sm text-gray-600">In Progress</p>
                      </div>
                      <div className="text-center p-4 bg-green-50 rounded-lg">
                        <p className="text-2xl font-bold text-green-600">
                          {project.tasks.filter((t: any) => t.status === 'COMPLETED').length}
                        </p>
                        <p className="text-sm text-gray-600">Completed</p>
                      </div>
                      <div className="text-center p-4 bg-yellow-50 rounded-lg">
                        <p className="text-2xl font-bold text-yellow-600">
                          {project.tasks.filter((t: any) => t.status === 'TODO').length}
                        </p>
                        <p className="text-sm text-gray-600">Pending</p>
                      </div>
                    </div>

                    {/* Task List */}
                    <div className="space-y-3">
                      <h4 className="font-medium text-gray-900">Recent Tasks</h4>
                      {project.tasks.slice(0, 10).map((task: any) => (
                        <div key={task.id} className="border rounded-lg p-4 hover:bg-gray-50">
                          <div className="flex items-center justify-between">
                            <div className="flex-1">
                              <div className="flex items-center space-x-3">
                                <h5 className="font-medium text-gray-900">{task.title}</h5>
                                <Badge
                                  className={`text-xs ${
                                    task.status === 'COMPLETED' ? 'bg-green-100 text-green-800' :
                                    task.status === 'IN_PROGRESS' ? 'bg-blue-100 text-blue-800' :
                                    'bg-yellow-100 text-yellow-800'
                                  }`}
                                >
                                  {task.status?.replace('_', ' ')}
                                </Badge>
                                <Badge variant="outline" className="text-xs">
                                  {task.priority}
                                </Badge>
                              </div>
                              <p className="text-sm text-gray-600 mt-1">{task.description}</p>
                              <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                                <span>Estimated: {task.estimatedHours || 0}h</span>
                                {task.assignedTo && (
                                  <span>Assigned to: {task.assignedTo.firstName} {task.assignedTo.lastName}</span>
                                )}
                                {task.dueDate && (
                                  <span>Due: {formatDate(task.dueDate)}</span>
                                )}
                              </div>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => router.push(`/tasks/${task.id}`)}
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                              {hasRole(['ADMIN', 'PROJECT_MANAGER']) && (
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => router.push(`/tasks/${task.id}/edit`)}
                                >
                                  <Edit className="h-4 w-4" />
                                </Button>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}

                      {project.tasks.length > 10 && (
                        <div className="text-center pt-4">
                          <Button
                            variant="outline"
                            onClick={() => router.push(`/tasks?projectId=${project.id}`)}
                          >
                            View All Tasks ({project.tasks.length})
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <CheckSquare className="h-8 w-8 text-gray-400" />
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No Tasks Yet</h3>
                    <p className="text-gray-600 mb-6">
                      Create tasks to break down project work and track progress.
                    </p>
                    {hasRole(['ADMIN', 'PROJECT_MANAGER']) && (
                      <Button
                        className="bg-red-600 hover:bg-red-700"
                        onClick={() => router.push(`/tasks/new?projectId=${project.id}`)}
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Create First Task
                      </Button>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
}
