'use client';

import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { timesheetsApi } from '@/lib/api';
import { useAuth } from '@/hooks/useAuth';
import { formatDate, getStatusColor } from '@/lib/utils';
import { Plus, Search, Filter, Eye, Edit, Clock, Calendar, CheckCircle } from 'lucide-react';
import Link from 'next/link';
import { toast } from 'sonner';

export default function TimesheetsPage() {
  const { hasRole } = useAuth();
  const [page, setPage] = useState(1);
  const [status, setStatus] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const queryClient = useQueryClient();

  const { data: timesheetsData, isLoading } = useQuery({
    queryKey: ['timesheets', page, status, searchTerm],
    queryFn: () => timesheetsApi.getAll({
      page,
      limit: 10,
      status: status || undefined,
      search: searchTerm || undefined
    }),
  });

  const timesheets = timesheetsData?.data || [];
  const pagination = timesheetsData?.pagination;

  // Mutations for timesheet actions
  const approveMutation = useMutation({
    mutationFn: (id: string) => timesheetsApi.approve(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['timesheets'] });
    },
  });

  const rejectMutation = useMutation({
    mutationFn: ({ id, reason }: { id: string; reason: string }) => timesheetsApi.reject(id, reason),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['timesheets'] });
    },
  });

  const submitMutation = useMutation({
    mutationFn: (id: string) => timesheetsApi.submit(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['timesheets'] });
    },
  });

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="spinner"></div>
        </div>
      </DashboardLayout>
    );
  }



  const getStatusColor = (status: string) => {
    switch (status) {
      case 'DRAFT': return 'status-draft';
      case 'SUBMITTED': return 'status-pending';
      case 'APPROVED': return 'status-completed';
      case 'REJECTED': return 'status-cancelled';
      default: return 'status-inactive';
    }
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Timesheets</h1>
            <p className="text-gray-600">
              {hasRole(['ADMIN'])
                ? 'Track and manage time entries for all resources'
                : 'Track and manage time entries'
              }
            </p>
          </div>
          {(hasRole(['RESOURCE']) || hasRole(['ADMIN'])) && (
            <Link href="/timesheets/entry">
              <Button className="redwood-button-primary">
                <Plus className="h-4 w-4 mr-2" />
                {hasRole(['ADMIN']) ? 'Enter Hours (Any Resource)' : 'Enter Hours'}
              </Button>
            </Link>
          )}
        </div>

        {/* Filters */}
        <Card className="redwood-card">
          <CardContent className="p-4">
            <div className="flex items-center space-x-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search timesheets..."
                    className="redwood-input pl-10 w-full"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>
              <select
                className="redwood-input"
                value={status}
                onChange={(e) => setStatus(e.target.value)}
              >
                <option value="">All Status</option>
                <option value="DRAFT">Draft</option>
                <option value="SUBMITTED">Submitted</option>
                <option value="APPROVED">Approved</option>
                <option value="REJECTED">Rejected</option>
              </select>
              <input
                type="week"
                className="redwood-input"
                placeholder="Week ending"
              />
              <Button variant="outline">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Current & Upcoming Weeks */}
        <WeeklyTimesheetStatus />

        {/* Timesheets List */}
        <div className="grid gap-4">
          {timesheets.map((timesheet) => (
            <Card key={timesheet.id} className="redwood-card">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="text-lg text-gray-900">
                      Week Ending {formatDate(timesheet.weekEnding)}
                    </CardTitle>
                    <CardDescription className="mt-1">
                      {timesheet.firstName} {timesheet.lastName} • {timesheet.project_name || 'No Project'}
                    </CardDescription>
                  </div>
                  <Badge className={getStatusColor(timesheet.status)}>
                    {timesheet.status}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                  <div className="flex items-center text-sm">
                    <Clock className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-gray-600">Total Hours:</span>
                    <span className="ml-1 font-bold text-lg">{timesheet.totalHours}h</span>
                  </div>
                  
                  <div className="flex items-center text-sm">
                    <span className="text-gray-600">Regular:</span>
                    <span className="ml-1 font-medium">{timesheet.regularHours}h</span>
                  </div>

                  <div className="flex items-center text-sm">
                    <span className="text-gray-600">Overtime:</span>
                    <span className="ml-1 font-medium">{timesheet.overtimeHours}h</span>
                  </div>

                  <div className="flex items-center text-sm">
                    <Calendar className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-gray-600">Submitted:</span>
                    <span className="ml-1 font-medium">
                      {timesheet.submittedAt ? formatDate(timesheet.submittedAt) : 'Not submitted'}
                    </span>
                  </div>
                </div>

                {/* Time Breakdown */}
                <div className="mb-4">
                  <h4 className="text-sm font-medium text-gray-700 mb-2">Daily Breakdown</h4>
                  <div className="grid grid-cols-7 gap-2">
                    {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map((day, index) => (
                      <div key={day} className="text-center">
                        <div className="text-xs text-gray-500 mb-1">{day}</div>
                        <div className="bg-gray-100 rounded p-2 text-sm font-medium">
                          {index < 5 ? '8h' : '0h'}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-2">
                  <Button variant="outline" size="sm">
                    <Eye className="h-4 w-4 mr-1" />
                    View Details
                  </Button>
                  
                  {timesheet.status === 'DRAFT' && (hasRole(['RESOURCE']) || hasRole(['ADMIN'])) && (
                    <>
                      <Link
                        href={`/timesheets/entry?timesheetId=${timesheet.id}&projectId=${timesheet.projectId}&resourceId=${timesheet.resourceId}&weekStarting=${timesheet.weekStarting.split('T')[0]}`}
                      >
                        <Button variant="outline" size="sm">
                          <Edit className="h-4 w-4 mr-1" />
                          {hasRole(['ADMIN']) ? 'Edit Hours (Admin)' : 'Edit Hours'}
                        </Button>
                      </Link>
                      <Button
                        className="redwood-button-primary"
                        size="sm"
                        onClick={() => submitMutation.mutate(timesheet.id)}
                        disabled={submitMutation.isPending}
                      >
                        {hasRole(['ADMIN']) ? 'Submit for Resource' : 'Submit for Approval'}
                      </Button>
                    </>
                  )}
                  
                  {timesheet.status === 'SUBMITTED' && hasRole(['ADMIN', 'PROJECT_MANAGER']) && (
                    <>
                      <Button
                        className="bg-green-600 hover:bg-green-700 text-white"
                        size="sm"
                        onClick={() => approveMutation.mutate(timesheet.id)}
                        disabled={approveMutation.isPending}
                      >
                        <CheckCircle className="h-4 w-4 mr-1" />
                        Approve
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-red-600 hover:text-red-700"
                        onClick={() => {
                          const reason = prompt('Please provide a reason for rejection:');
                          if (reason) {
                            rejectMutation.mutate({ id: timesheet.id, reason });
                          }
                        }}
                        disabled={rejectMutation.isPending}
                      >
                        Reject
                      </Button>
                    </>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {timesheets.length === 0 && (
          <Card className="redwood-card">
            <CardContent className="text-center py-12">
              <p className="text-gray-500 mb-4">No timesheets found</p>
              {(hasRole(['RESOURCE']) || hasRole(['ADMIN'])) && (
                <Link href="/timesheets/entry">
                  <Button className="redwood-button-primary">
                    <Plus className="h-4 w-4 mr-2" />
                    {hasRole(['ADMIN']) ? 'Enter First Timesheet (Any Resource)' : 'Enter Your First Timesheet'}
                  </Button>
                </Link>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardLayout>
  );
}

// Weekly Timesheet Status Component
function WeeklyTimesheetStatus() {
  const { hasRole } = useAuth();
  const queryClient = useQueryClient();

  // Helper function to get start of week (Monday)
  function getStartOfWeek(date: Date): string {
    const d = new Date(date);
    const day = d.getDay();
    const diff = d.getDate() - day + (day === 0 ? -6 : 1); // Adjust when day is Sunday
    d.setDate(diff);
    return d.toISOString().split('T')[0];
  }

  const currentWeek = getStartOfWeek(new Date());
  const nextWeek = getStartOfWeek(new Date(Date.now() + 7 * 24 * 60 * 60 * 1000));

  // Get user's projects or all projects for admin
  const { data: projectsData } = useQuery({
    queryKey: hasRole(['ADMIN']) ? ['all-projects'] : ['my-projects'],
    queryFn: () => hasRole(['ADMIN']) ? timesheetsApi.getAllProjects() : timesheetsApi.getMyProjects(),
  });

  // Get all resources for admin
  const { data: resourcesData } = useQuery({
    queryKey: ['all-resources'],
    queryFn: () => timesheetsApi.getAllResources(),
    enabled: hasRole(['ADMIN']),
  });

  const projects = projectsData?.data || [];
  const resources = resourcesData?.data || [];

  // Create timesheet mutation
  const createTimesheetMutation = useMutation({
    mutationFn: (data: any) => timesheetsApi.create(data),
    onSuccess: () => {
      toast.success('Timesheet created successfully');
      queryClient.invalidateQueries({ queryKey: ['timesheets'] });
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to create timesheet');
    },
  });

  const handleCreateTimesheet = (weekStarting: string, projectId: string, resourceId?: string) => {
    const data: any = {
      projectId,
      weekStarting,
    };

    if (resourceId) {
      data.resourceId = resourceId;
    }

    createTimesheetMutation.mutate(data);
  };

  return (
    <Card className="redwood-card">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Calendar className="h-5 w-5" />
          <span>Current & Upcoming Weeks</span>
        </CardTitle>
        <CardDescription>
          {hasRole(['ADMIN'])
            ? 'Create and manage timesheets for current and upcoming weeks for any resource'
            : 'Create and manage your timesheets for current and upcoming weeks'
          }
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid gap-4 md:grid-cols-2">
          {/* Current Week */}
          <WeekCard
            title="Current Week"
            weekStarting={currentWeek}
            projects={projects}
            resources={resources}
            isAdmin={hasRole(['ADMIN'])}
            onCreateTimesheet={handleCreateTimesheet}
            isCreating={createTimesheetMutation.isPending}
          />

          {/* Next Week */}
          <WeekCard
            title="Next Week"
            weekStarting={nextWeek}
            projects={projects}
            resources={resources}
            isAdmin={hasRole(['ADMIN'])}
            onCreateTimesheet={handleCreateTimesheet}
            isCreating={createTimesheetMutation.isPending}
          />
        </div>
      </CardContent>
    </Card>
  );
}

// Week Card Component
interface WeekCardProps {
  title: string;
  weekStarting: string;
  projects: any[];
  resources: any[];
  isAdmin: boolean;
  onCreateTimesheet: (weekStarting: string, projectId: string, resourceId?: string) => void;
  isCreating: boolean;
}

function WeekCard({ title, weekStarting, projects, resources, isAdmin, onCreateTimesheet, isCreating }: WeekCardProps) {
  const [selectedProject, setSelectedProject] = useState('');
  const [selectedResource, setSelectedResource] = useState('');

  const weekEndDate = new Date(weekStarting);
  weekEndDate.setDate(weekEndDate.getDate() + 6);

  // Get existing timesheets for this week
  const { data: weekTimesheets } = useQuery({
    queryKey: ['week-timesheets', weekStarting],
    queryFn: () => timesheetsApi.getAll({ weekStarting, limit: 50 }),
  });

  const existingTimesheets = weekTimesheets?.data || [];

  const handleCreate = () => {
    if (!selectedProject) {
      toast.error('Please select a project');
      return;
    }

    if (isAdmin && !selectedResource) {
      toast.error('Please select a resource');
      return;
    }

    onCreateTimesheet(weekStarting, selectedProject, isAdmin ? selectedResource : undefined);
    setSelectedProject('');
    setSelectedResource('');
  };

  return (
    <div className="border rounded-lg p-4 space-y-4">
      <div>
        <h3 className="font-medium text-gray-900">{title}</h3>
        <p className="text-sm text-gray-500">
          {formatDate(weekStarting)} - {formatDate(weekEndDate.toISOString().split('T')[0])}
        </p>
      </div>

      {/* Existing Timesheets */}
      {existingTimesheets.length > 0 && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-700">Existing Timesheets:</h4>
          {existingTimesheets.map((timesheet: any) => (
            <div key={timesheet.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
              <div className="text-sm">
                <span className="font-medium">{timesheet.resource?.user?.firstName} {timesheet.resource?.user?.lastName}</span>
                <span className="text-gray-500 ml-2">• {timesheet.project?.name}</span>
              </div>
              <div className="flex items-center space-x-2">
                <Badge className={
                  timesheet.status === 'DRAFT' ? 'bg-yellow-100 text-yellow-800' :
                  timesheet.status === 'SUBMITTED' ? 'bg-blue-100 text-blue-800' :
                  timesheet.status === 'APPROVED' ? 'bg-green-100 text-green-800' :
                  'bg-red-100 text-red-800'
                }>
                  {timesheet.status}
                </Badge>
                {timesheet.status === 'DRAFT' && (
                  <Link
                    href={`/timesheets/entry?timesheetId=${timesheet.id}&projectId=${timesheet.projectId}&resourceId=${timesheet.resourceId}&weekStarting=${weekStarting}`}
                  >
                    <Button size="sm" variant="outline">
                      <Edit className="h-3 w-3 mr-1" />
                      Edit
                    </Button>
                  </Link>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Create New Timesheet */}
      <div className="space-y-3">
        <h4 className="text-sm font-medium text-gray-700">Create New Timesheet:</h4>

        {isAdmin && (
          <div>
            <label className="block text-xs font-medium text-gray-600 mb-1">Resource</label>
            <select
              className="w-full text-sm border rounded px-2 py-1"
              value={selectedResource}
              onChange={(e) => setSelectedResource(e.target.value)}
            >
              <option value="">Select resource...</option>
              {resources.map((resource: any) => (
                <option key={resource.id} value={resource.id}>
                  {resource.user.firstName} {resource.user.lastName}
                </option>
              ))}
            </select>
          </div>
        )}

        <div>
          <label className="block text-xs font-medium text-gray-600 mb-1">Project</label>
          <select
            className="w-full text-sm border rounded px-2 py-1"
            value={selectedProject}
            onChange={(e) => setSelectedProject(e.target.value)}
          >
            <option value="">Select project...</option>
            {projects.map((project: any) => (
              <option key={project.id} value={project.id}>
                {project.name}
              </option>
            ))}
          </select>
        </div>

        <Button
          size="sm"
          className="w-full"
          onClick={handleCreate}
          disabled={isCreating || !selectedProject || (isAdmin && !selectedResource)}
        >
          <Plus className="h-3 w-3 mr-1" />
          Create Timesheet
        </Button>
      </div>
    </div>
  );
}
