'use client';

import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { timesheetsApi } from '@/lib/api';
import { useAuth } from '@/hooks/useAuth';
import { formatDate } from '@/lib/utils';
import {
  Calendar,
  Clock,
  Save,
  Send,
  Plus,
  ChevronLeft,
  ChevronRight,
  ArrowLeft,
  AlertCircle
} from 'lucide-react';
import Link from 'next/link';
import { toast } from 'sonner';
import { QuickTaskModal } from '@/components/modals/quick-task-modal';
import { SmartTimesheetInfo } from '@/components/timesheets/smart-timesheet-info';
import { useSearchParams } from 'next/navigation';

interface TimesheetEntry {
  id?: string;
  taskId: string;
  date: string;
  hours: number;
  description: string;
  task?: {
    id: string;
    title: string;
  };
}

export default function TimesheetEntryPage() {
  const { user, hasRole } = useAuth();
  const queryClient = useQueryClient();
  const searchParams = useSearchParams();

  // Get URL parameters for editing existing timesheet
  const timesheetId = searchParams.get('timesheetId');
  const urlProjectId = searchParams.get('projectId');
  const urlResourceId = searchParams.get('resourceId');
  const urlWeekStarting = searchParams.get('weekStarting');

  // State management
  const [selectedProject, setSelectedProject] = useState(urlProjectId || '');
  const [selectedResource, setSelectedResource] = useState(urlResourceId || '');
  const [selectedWeek, setSelectedWeek] = useState(urlWeekStarting || getStartOfWeek(new Date()));
  const [currentTimesheet, setCurrentTimesheet] = useState<any>(null);
  const [entries, setEntries] = useState<TimesheetEntry[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showTaskModal, setShowTaskModal] = useState(false);

  const isAdmin = hasRole(['ADMIN']);

  // Get user's projects or all projects for admin
  const { data: projectsData } = useQuery({
    queryKey: isAdmin ? ['all-projects'] : ['my-projects'],
    queryFn: () => isAdmin ? timesheetsApi.getAllProjects() : timesheetsApi.getMyProjects(),
  });

  // Get all resources for admin
  const { data: resourcesData } = useQuery({
    queryKey: ['all-resources'],
    queryFn: () => timesheetsApi.getAllResources(),
    enabled: isAdmin,
  });

  // Get user's tasks for selected project or resource tasks for admin
  const { data: tasksData } = useQuery({
    queryKey: isAdmin ? ['resource-tasks', selectedResource, selectedProject] : ['my-tasks', selectedProject],
    queryFn: () => {
      if (isAdmin && selectedResource) {
        return timesheetsApi.getResourceTasks(selectedResource, selectedProject);
      } else if (!isAdmin) {
        return timesheetsApi.getMyTasks(selectedProject);
      }
      return Promise.resolve({ data: [] });
    },
    enabled: isAdmin ? !!selectedResource : !!selectedProject,
  });

  // Get specific timesheet if timesheetId is provided
  const { data: specificTimesheetData } = useQuery({
    queryKey: ['specific-timesheet', timesheetId],
    queryFn: () => timesheetsApi.getById(timesheetId!),
    enabled: !!timesheetId,
  });

  // Get existing timesheet for selected week and project
  const { data: timesheetData, refetch: refetchTimesheet } = useQuery({
    queryKey: ['timesheet', selectedProject, selectedWeek, selectedResource],
    queryFn: () => {
      const params: any = {
        projectId: selectedProject,
        weekStarting: selectedWeek,
        limit: 1,
      };

      if (isAdmin && selectedResource) {
        params.resourceId = selectedResource;
      }

      return timesheetsApi.getAll(params);
    },
    enabled: !timesheetId && !!selectedProject && !!selectedWeek && (isAdmin ? !!selectedResource : true),
  });

  const projects = projectsData?.data || [];
  const resources = resourcesData?.data || [];
  const tasks = tasksData?.data || [];

  // Create timesheet mutation
  const createTimesheetMutation = useMutation({
    mutationFn: (data: any) => timesheetsApi.create(data),
    onSuccess: (response) => {
      setCurrentTimesheet(response.data);
      toast.success('Timesheet created successfully');

      // Refetch the appropriate timesheet data
      if (timesheetId) {
        queryClient.invalidateQueries({ queryKey: ['specific-timesheet', timesheetId] });
      } else {
        refetchTimesheet();
      }
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to create timesheet');
    },
  });

  // Add entry mutation
  const addEntryMutation = useMutation({
    mutationFn: ({ timesheetId, entryData }: { timesheetId: string; entryData: any }) =>
      timesheetsApi.addEntry(timesheetId, entryData),
    onSuccess: (response) => {
      toast.success('Hours saved successfully');

      // Refetch the appropriate timesheet data
      if (timesheetId) {
        queryClient.invalidateQueries({ queryKey: ['specific-timesheet', timesheetId] });
      } else {
        refetchTimesheet();
      }

      // Update local entries state immediately for better UX
      setEntries(prevEntries => {
        const existingIndex = prevEntries.findIndex(
          entry => entry.taskId === response.data.taskId && entry.date === response.data.date.split('T')[0]
        );

        if (existingIndex >= 0) {
          // Update existing entry
          const newEntries = [...prevEntries];
          newEntries[existingIndex] = {
            ...newEntries[existingIndex],
            hours: response.data.hours,
            description: response.data.description,
          };
          return newEntries;
        } else {
          // Add new entry
          return [...prevEntries, {
            id: response.data.id,
            taskId: response.data.taskId,
            date: response.data.date.split('T')[0],
            hours: response.data.hours,
            description: response.data.description,
            task: response.data.task,
          }];
        }
      });
    },
    onError: (error: any) => {
      console.error('Failed to save timesheet entry:', error);
      toast.error(error.response?.data?.message || 'Failed to save hours');
    },
  });

  // Submit timesheet mutation
  const submitTimesheetMutation = useMutation({
    mutationFn: (id: string) => timesheetsApi.submit(id),
    onSuccess: () => {
      toast.success('Timesheet submitted for approval');
      queryClient.invalidateQueries({ queryKey: ['timesheets'] });

      // Refetch the appropriate timesheet data
      if (timesheetId) {
        queryClient.invalidateQueries({ queryKey: ['specific-timesheet', timesheetId] });
      } else {
        refetchTimesheet();
      }
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to submit timesheet');
    },
  });

  // Helper function to get start of week (Monday)
  function getStartOfWeek(date: Date): string {
    const d = new Date(date);
    const day = d.getDay();
    const diff = d.getDate() - day + (day === 0 ? -6 : 1); // Adjust when day is Sunday
    d.setDate(diff);
    return d.toISOString().split('T')[0];
  }

  // Generate week dates
  function getWeekDates(startDate: string): Date[] {
    const dates = [];
    const start = new Date(startDate);
    for (let i = 0; i < 7; i++) {
      const date = new Date(start);
      date.setDate(start.getDate() + i);
      dates.push(date);
    }
    return dates;
  }

  // Handle week navigation
  const navigateWeek = (direction: 'prev' | 'next') => {
    const currentDate = new Date(selectedWeek);
    const newDate = new Date(currentDate);
    newDate.setDate(currentDate.getDate() + (direction === 'next' ? 7 : -7));
    setSelectedWeek(getStartOfWeek(newDate));
  };

  // Handle project change
  const handleProjectChange = (projectId: string) => {
    setSelectedProject(projectId);
    setCurrentTimesheet(null);
    setEntries([]);
  };

  // Handle resource change (admin only)
  const handleResourceChange = (resourceId: string) => {
    setSelectedResource(resourceId);
    setCurrentTimesheet(null);
    setEntries([]);
  };

  // Create timesheet if it doesn't exist
  const handleCreateTimesheet = () => {
    if (!selectedProject || !selectedWeek) return;
    if (isAdmin && !selectedResource) return;

    const data: any = {
      projectId: selectedProject,
      weekStarting: selectedWeek,
    };

    if (isAdmin && selectedResource) {
      data.resourceId = selectedResource;
    }

    createTimesheetMutation.mutate(data);
  };

  // Save hours for a specific day and task
  const saveHours = (taskId: string, date: string, hours: number, description: string) => {
    if (!currentTimesheet) return;

    addEntryMutation.mutate({
      timesheetId: currentTimesheet.id,
      entryData: {
        taskId,
        date,
        hours,
        description,
      },
    });
  };

  // Submit timesheet for approval
  const handleSubmitTimesheet = () => {
    if (!currentTimesheet) return;
    
    setIsSubmitting(true);
    submitTimesheetMutation.mutate(currentTimesheet.id);
    setIsSubmitting(false);
  };

  // Update current timesheet when data changes
  useEffect(() => {
    let timesheet = null;

    // Use specific timesheet if available, otherwise use general timesheet data
    if (specificTimesheetData?.data) {
      timesheet = specificTimesheetData.data;
    } else if (timesheetData?.data && timesheetData.data.length > 0) {
      timesheet = timesheetData.data[0];
    }

    if (timesheet) {
      setCurrentTimesheet(timesheet);

      // Convert entries to the format we need
      const formattedEntries = timesheet.entries?.map((entry: any) => ({
        id: entry.id,
        taskId: entry.taskId,
        date: entry.date.split('T')[0],
        hours: entry.hours,
        description: entry.description,
        task: entry.task,
      })) || [];

      setEntries(formattedEntries);
    } else {
      setCurrentTimesheet(null);
      setEntries([]);
    }
  }, [timesheetData, specificTimesheetData]);

  const weekDates = getWeekDates(selectedWeek);
  const weekEndDate = new Date(selectedWeek);
  weekEndDate.setDate(weekEndDate.getDate() + 6);

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/timesheets">
              <Button variant="outline" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Timesheets
              </Button>
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                {timesheetId ? 'Edit Timesheet' : 'Timesheet Entry'}
              </h1>
              <p className="text-gray-600">
                {timesheetId
                  ? `Editing timesheet for week ${formatDate(selectedWeek)} - ${formatDate(weekEndDate.toISOString().split('T')[0])}`
                  : 'Enter your hours for the selected week'
                }
              </p>
            </div>
          </div>
        </div>

        {/* Project, Resource and Week Selection */}
        <Card className="redwood-card">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Calendar className="h-5 w-5" />
              <span>{isAdmin ? 'Resource, Project & Week Selection' : 'Project & Week Selection'}</span>
            </CardTitle>
            {isAdmin && (
              <CardDescription>
                As an admin, you can enter hours on behalf of any resource.
              </CardDescription>
            )}
          </CardHeader>
          <CardContent>
            <div className={`grid gap-4 ${isAdmin ? 'grid-cols-1 lg:grid-cols-3' : 'grid-cols-1 md:grid-cols-2'}`}>
              {/* Resource Selection (Admin Only) */}
              {isAdmin && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Select Resource
                  </label>
                  <select
                    className="redwood-input w-full"
                    value={selectedResource}
                    onChange={(e) => handleResourceChange(e.target.value)}
                  >
                    <option value="">Choose a resource...</option>
                    {resources.map((resource: any) => (
                      <option key={resource.id} value={resource.id}>
                        {resource.user.firstName} {resource.user.lastName} ({resource.user.email})
                      </option>
                    ))}
                  </select>
                </div>
              )}

              {/* Project Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Select Project
                </label>
                <select
                  className="redwood-input w-full"
                  value={selectedProject}
                  onChange={(e) => handleProjectChange(e.target.value)}
                >
                  <option value="">Choose a project...</option>
                  {projects.map((project: any) => (
                    <option key={project.id} value={project.id}>
                      {project.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Week Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Week Starting
                </label>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => navigateWeek('prev')}
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <input
                    type="date"
                    className="redwood-input flex-1"
                    value={selectedWeek}
                    onChange={(e) => setSelectedWeek(e.target.value)}
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => navigateWeek('next')}
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
                <p className="text-sm text-gray-500 mt-1">
                  Week: {formatDate(selectedWeek)} - {formatDate(weekEndDate.toISOString().split('T')[0])}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Timesheet Status and Actions */}
        {selectedProject && (isAdmin ? selectedResource : true) && (
          <Card className="redwood-card">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  {isAdmin && selectedResource && (
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-medium text-gray-700">Resource:</span>
                      <span className="text-sm text-gray-900">
                        {resources.find((r: any) => r.id === selectedResource)?.user?.firstName}{' '}
                        {resources.find((r: any) => r.id === selectedResource)?.user?.lastName}
                      </span>
                    </div>
                  )}
                  {currentTimesheet ? (
                    <>
                      <Badge className={
                        currentTimesheet.status === 'DRAFT' ? 'bg-yellow-100 text-yellow-800' :
                        currentTimesheet.status === 'SUBMITTED' ? 'bg-blue-100 text-blue-800' :
                        currentTimesheet.status === 'APPROVED' ? 'bg-green-100 text-green-800' :
                        'bg-red-100 text-red-800'
                      }>
                        {currentTimesheet.status}
                      </Badge>
                      <span className="text-sm text-gray-600">
                        Total Hours: {currentTimesheet.totalHours || 0}
                      </span>
                      {currentTimesheet.submittedAt && (
                        <span className="text-sm text-gray-600">
                          Submitted: {formatDate(currentTimesheet.submittedAt)}
                        </span>
                      )}
                    </>
                  ) : (
                    <span className="text-sm text-gray-600">No timesheet created for this week</span>
                  )}
                </div>
                <div className="flex space-x-2">
                  {!currentTimesheet ? (
                    <Button
                      className="redwood-button-primary"
                      onClick={handleCreateTimesheet}
                      disabled={createTimesheetMutation.isPending}
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Create Timesheet
                    </Button>
                  ) : currentTimesheet.status === 'DRAFT' ? (
                    <>
                      {(isAdmin || hasRole(['PROJECT_MANAGER'])) && (
                        <Button
                          variant="outline"
                          onClick={() => setShowTaskModal(true)}
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          Add Task
                        </Button>
                      )}
                      <Button
                        className="redwood-button-primary"
                        onClick={handleSubmitTimesheet}
                        disabled={isSubmitting || submitTimesheetMutation.isPending}
                      >
                        <Send className="h-4 w-4 mr-2" />
                        {isAdmin ? 'Submit for Resource' : 'Submit for Approval'}
                      </Button>
                    </>
                  ) : null}
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Debug Information */}
        {process.env.NODE_ENV === 'development' && (
          <Card className="redwood-card bg-gray-50">
            <CardContent className="p-4">
              <div className="text-xs space-y-1">
                <div>Current Timesheet: {currentTimesheet ? 'Yes' : 'No'}</div>
                <div>Timesheet Status: {currentTimesheet?.status || 'N/A'}</div>
                <div>Tasks Count: {tasks.length}</div>
                <div>Entries Count: {entries.length}</div>
                <div>Selected Project: {selectedProject || 'None'}</div>
                <div>Selected Resource: {selectedResource || 'None'}</div>
                <div>Is Admin: {isAdmin ? 'Yes' : 'No'}</div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Time Entry Grid */}
        {currentTimesheet && currentTimesheet.status === 'DRAFT' && tasks.length > 0 && (
          <Card className="redwood-card">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Clock className="h-5 w-5" />
                <span>Time Entry</span>
              </CardTitle>
              <CardDescription>
                Enter your hours for each task and day. Click on a cell to add or edit hours.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left p-3 font-medium text-gray-700 min-w-[200px]">
                        Task
                      </th>
                      {weekDates.map((date, index) => (
                        <th key={index} className="text-center p-3 font-medium text-gray-700 min-w-[100px]">
                          <div className="flex flex-col">
                            <span className="text-xs text-gray-500">
                              {date.toLocaleDateString('en-US', { weekday: 'short' })}
                            </span>
                            <span className="text-sm">
                              {date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                            </span>
                          </div>
                        </th>
                      ))}
                      <th className="text-center p-3 font-medium text-gray-700 min-w-[80px]">
                        Total
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {tasks.map((task: any) => {
                      const taskEntries = entries.filter(entry => entry.taskId === task.id);
                      const taskTotal = taskEntries.reduce((sum, entry) => sum + entry.hours, 0);

                      return (
                        <tr key={task.id} className="border-b hover:bg-gray-50">
                          <td className="p-3">
                            <div>
                              <div className="font-medium text-gray-900">{task.title}</div>
                              <div className="text-sm text-gray-500">{task.project?.name}</div>
                            </div>
                          </td>
                          {weekDates.map((date, dateIndex) => {
                            const dateStr = date.toISOString().split('T')[0];
                            const existingEntry = taskEntries.find(entry => entry.date === dateStr);

                            return (
                              <td key={dateIndex} className="p-1 text-center">
                                <TimeEntryCell
                                  taskId={task.id}
                                  date={dateStr}
                                  initialHours={existingEntry?.hours || 0}
                                  initialDescription={existingEntry?.description || ''}
                                  onSave={(hours, description) => saveHours(task.id, dateStr, hours, description)}
                                  disabled={addEntryMutation.isPending}
                                  task={task}
                                  dailyAllocatedHours={task.allocation?.allocationPercent ? (40 * task.allocation.allocationPercent / 100) / 7 : 8}
                                  dailyLoggedHours={entries
                                    .filter(entry => entry.date === dateStr)
                                    .reduce((sum, entry) => sum + entry.hours, 0)
                                  }
                                />
                              </td>
                            );
                          })}
                          <td className="p-3 text-center font-medium">
                            {taskTotal.toFixed(1)}h
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                  <tfoot>
                    <tr className="border-t-2 bg-gray-50">
                      <td className="p-3 font-medium text-gray-900">Daily Totals</td>
                      {weekDates.map((date, dateIndex) => {
                        const dateStr = date.toISOString().split('T')[0];
                        const dayTotal = entries
                          .filter(entry => entry.date === dateStr)
                          .reduce((sum, entry) => sum + entry.hours, 0);

                        return (
                          <td key={dateIndex} className="p-3 text-center font-medium">
                            {dayTotal.toFixed(1)}h
                          </td>
                        );
                      })}
                      <td className="p-3 text-center font-bold text-lg">
                        {entries.reduce((sum, entry) => sum + entry.hours, 0).toFixed(1)}h
                      </td>
                    </tr>
                  </tfoot>
                </table>
              </div>
            </CardContent>
          </Card>
        )}

        {/* No Tasks Message */}
        {currentTimesheet && tasks.length === 0 && (
          <Card className="redwood-card">
            <CardContent className="text-center py-12">
              <AlertCircle className="h-12 w-12 text-amber-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Tasks Available</h3>
              <p className="text-gray-500 mb-4">
                {isAdmin
                  ? 'No tasks have been created for this resource in the selected project.'
                  : 'You don\'t have any tasks assigned for the selected project.'
                }
              </p>
              {(isAdmin || hasRole(['PROJECT_MANAGER'])) && selectedProject && (isAdmin ? selectedResource : true) && (
                <Button
                  className="redwood-button-primary"
                  onClick={() => setShowTaskModal(true)}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Create Task
                </Button>
              )}
              {!isAdmin && !hasRole(['PROJECT_MANAGER']) && (
                <p className="text-sm text-gray-400">
                  Contact your project manager to get tasks assigned.
                </p>
              )}
            </CardContent>
          </Card>
        )}

        {/* Instructions */}
        {(!selectedProject || (isAdmin && !selectedResource)) && (
          <Card className="redwood-card">
            <CardContent className="text-center py-12">
              <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {isAdmin ? 'Select Resource and Project' : 'Select a Project'}
              </h3>
              <p className="text-gray-500">
                {isAdmin
                  ? 'Choose a resource, project and week to start entering timesheet on their behalf.'
                  : 'Choose a project and week to start entering your timesheet.'
                }
              </p>
            </CardContent>
          </Card>
        )}

        {/* Smart Timesheet Information */}
        {currentTimesheet && (
          <SmartTimesheetInfo timesheetId={currentTimesheet.id} />
        )}

        {/* Quick Task Creation Modal */}
        <QuickTaskModal
          isOpen={showTaskModal}
          onClose={() => setShowTaskModal(false)}
          projectId={selectedProject}
          resourceId={isAdmin ? selectedResource : (resources.find((r: any) => r.userId === user?.id)?.id || '')}
          onTaskCreated={() => {
            // Refetch tasks after creation
            queryClient.invalidateQueries({
              queryKey: isAdmin ? ['resource-tasks', selectedResource, selectedProject] : ['my-tasks', selectedProject]
            });
          }}
        />
      </div>
    </DashboardLayout>
  );
}

// Time Entry Cell Component
interface TimeEntryCellProps {
  taskId: string;
  date: string;
  initialHours: number;
  initialDescription: string;
  onSave: (hours: number, description: string) => void;
  disabled: boolean;
  task?: any;
  dailyAllocatedHours?: number;
  dailyLoggedHours?: number;
}

function TimeEntryCell({
  taskId,
  date,
  initialHours,
  initialDescription,
  onSave,
  disabled,
  task,
  dailyAllocatedHours = 8,
  dailyLoggedHours = 0
}: TimeEntryCellProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [hours, setHours] = useState(initialHours.toString());
  const [description, setDescription] = useState(initialDescription);

  // Update local state when props change
  useEffect(() => {
    setHours(initialHours.toString());
    setDescription(initialDescription);
  }, [initialHours, initialDescription]);

  // Close editing when not disabled (save completed)
  useEffect(() => {
    if (!disabled && isEditing) {
      setIsEditing(false);
    }
  }, [disabled, isEditing]);

  const handleSave = () => {
    const hoursNum = parseFloat(hours) || 0;
    if (hoursNum >= 0) {
      onSave(hoursNum, description);
      // Don't close editing state immediately - let the mutation handle it
    }
  };

  const handleCancel = () => {
    setHours(initialHours.toString());
    setDescription(initialDescription);
    setIsEditing(false);
  };

  const remainingTaskHours = task ? Math.max(0, (task.estimatedHours || 0) - (task.actualHours || 0)) : 0;
  const suggestedHours = Math.min(remainingTaskHours, dailyAllocatedHours / 7); // Rough daily suggestion
  const wouldExceedDaily = (dailyLoggedHours - initialHours + parseFloat(hours || '0')) > dailyAllocatedHours;
  const wouldExceedTask = task && (parseFloat(hours || '0') > remainingTaskHours);

  if (isEditing) {
    return (
      <div className="space-y-2 p-3 bg-white border rounded shadow-lg min-w-[200px] max-w-[250px]">
        {/* Smart guidance */}
        {task && (
          <div className="text-xs space-y-1 mb-2 p-2 bg-blue-50 rounded">
            <div className="font-medium text-blue-800">Task: {task.title}</div>
            <div className="text-blue-600">
              Estimated: {task.estimatedHours || 0}h | Remaining: {remainingTaskHours.toFixed(1)}h
            </div>
            {suggestedHours > 0 && (
              <div className="text-green-600">
                Suggested: {suggestedHours.toFixed(1)}h
              </div>
            )}
          </div>
        )}

        <input
          type="number"
          step="0.5"
          min="0"
          max="24"
          value={hours}
          onChange={(e) => setHours(e.target.value)}
          className={`w-full px-2 py-1 text-sm border rounded ${
            wouldExceedDaily || wouldExceedTask ? 'border-amber-400 bg-amber-50' : 'border-gray-300'
          }`}
          placeholder={suggestedHours > 0 ? suggestedHours.toFixed(1) : "Hours"}
          autoFocus
        />

        {/* Warnings */}
        {wouldExceedDaily && (
          <div className="text-xs text-amber-600 flex items-center space-x-1">
            <AlertCircle className="h-3 w-3" />
            <span>Exceeds daily allocation</span>
          </div>
        )}
        {wouldExceedTask && (
          <div className="text-xs text-amber-600 flex items-center space-x-1">
            <AlertCircle className="h-3 w-3" />
            <span>Exceeds task estimate</span>
          </div>
        )}

        <textarea
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          className="w-full px-2 py-1 text-sm border rounded resize-none"
          placeholder="Description (optional)"
          rows={2}
        />

        <div className="flex space-x-1">
          <Button
            size="sm"
            onClick={handleSave}
            disabled={disabled}
            className="flex-1 text-xs"
          >
            <Save className="h-3 w-3" />
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={handleCancel}
            className="flex-1 text-xs"
          >
            Cancel
          </Button>
        </div>
      </div>
    );
  }

  return (
    <button
      onClick={() => setIsEditing(true)}
      className={`w-full h-12 rounded border-2 border-dashed transition-colors ${
        initialHours > 0
          ? 'border-blue-300 bg-blue-50 text-blue-900 hover:bg-blue-100'
          : 'border-gray-300 hover:border-gray-400 hover:bg-gray-50'
      }`}
      title={initialDescription || 'Click to add hours'}
    >
      {initialHours > 0 ? `${initialHours}h` : '+'}
    </button>
  );
}
