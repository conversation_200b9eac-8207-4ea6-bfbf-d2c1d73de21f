'use client';

import { useState } from 'react';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { apiRequest } from '@/lib/api';
import { useAuth } from '@/hooks/useAuth';
import { ArrowLeft, Save, Users, Briefcase, DollarSign, Calendar, MapPin } from 'lucide-react';
import Link from 'next/link';
import { toast } from 'sonner';

export default function CreateBulkHiringPage() {
  const { hasRole } = useAuth();
  const router = useRouter();
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    jobType: 'WHITE_COLLAR',
    hiringType: 'BULK',
    department: '',
    location: '',
    positions: 1,
    employmentType: 'FTE',
    minExperience: 0,
    maxExperience: '',
    requiredSkills: [''],
    preferredSkills: [''],
    minSalary: '',
    maxSalary: '',
    benefits: [''],
    workShift: '',
    workMode: '',
    applicationDeadline: '',
    expectedJoiningDate: '',
    sourceVendor: false,
    directHiring: true,
    priority: 'MEDIUM',
  });

  const createJobMutation = useMutation({
    mutationFn: (data: any) => apiRequest.post('/bulk-hiring/jobs', data),
    onSuccess: () => {
      toast.success('Bulk hiring job created successfully');
      router.push('/bulk-hiring');
    },
    onError: () => {
      toast.error('Failed to create bulk hiring job');
    },
  });

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleArrayChange = (field: string, index: number, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field as keyof typeof prev].map((item: string, i: number) => 
        i === index ? value : item
      ),
    }));
  };

  const addArrayItem = (field: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: [...prev[field as keyof typeof prev], ''],
    }));
  };

  const removeArrayItem = (field: string, index: number) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field as keyof typeof prev].filter((_: any, i: number) => i !== index),
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const submitData = {
      ...formData,
      positions: parseInt(formData.positions.toString()),
      minExperience: parseInt(formData.minExperience.toString()),
      maxExperience: formData.maxExperience ? parseInt(formData.maxExperience.toString()) : null,
      minSalary: formData.minSalary ? parseFloat(formData.minSalary.toString()) : null,
      maxSalary: formData.maxSalary ? parseFloat(formData.maxSalary.toString()) : null,
      requiredSkills: formData.requiredSkills.filter(skill => skill.trim() !== ''),
      preferredSkills: formData.preferredSkills.filter(skill => skill.trim() !== ''),
      benefits: formData.benefits.filter(benefit => benefit.trim() !== ''),
      applicationDeadline: formData.applicationDeadline || null,
      expectedJoiningDate: formData.expectedJoiningDate || null,
    };

    createJobMutation.mutate(submitData);
  };

  if (!hasRole(['ADMIN', 'HR_MANAGER'])) {
    return (
      <DashboardLayout>
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold text-gray-900">Access Denied</h2>
          <p className="text-gray-600 mt-2">You don't have permission to create bulk hiring jobs.</p>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center space-x-4">
          <Link href="/bulk-hiring">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Bulk Hiring
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Create Bulk Hiring Job</h1>
            <p className="text-gray-600">Create a new bulk or adhoc hiring job for blue collar or white collar positions</p>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Briefcase className="h-5 w-5" />
                <span>Basic Information</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="title">Job Title *</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    placeholder="e.g., Software Engineer, Factory Worker, Sales Executive"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="department">Department *</Label>
                  <select
                    id="department"
                    value={formData.department}
                    onChange={(e) => handleInputChange('department', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                    required
                  >
                    <option value="">Select Department</option>
                    <option value="Engineering">Engineering</option>
                    <option value="Operations">Operations</option>
                    <option value="Manufacturing">Manufacturing</option>
                    <option value="Sales">Sales</option>
                    <option value="Marketing">Marketing</option>
                    <option value="HR">HR</option>
                    <option value="Finance">Finance</option>
                    <option value="Customer Service">Customer Service</option>
                    <option value="Quality Assurance">Quality Assurance</option>
                    <option value="Logistics">Logistics</option>
                  </select>
                </div>
              </div>

              <div>
                <Label htmlFor="description">Job Description *</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="Detailed job description including responsibilities, requirements, and expectations..."
                  rows={4}
                  required
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="jobType">Job Type *</Label>
                  <select
                    id="jobType"
                    value={formData.jobType}
                    onChange={(e) => handleInputChange('jobType', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                    required
                  >
                    <option value="WHITE_COLLAR">White Collar (Professional/Office)</option>
                    <option value="BLUE_COLLAR">Blue Collar (Manual/Skilled Labor)</option>
                  </select>
                </div>
                <div>
                  <Label htmlFor="hiringType">Hiring Type *</Label>
                  <select
                    id="hiringType"
                    value={formData.hiringType}
                    onChange={(e) => handleInputChange('hiringType', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                    required
                  >
                    <option value="BULK">Bulk Hiring (10+ positions)</option>
                    <option value="ADHOC">Adhoc Hiring (1-9 positions)</option>
                  </select>
                </div>
                <div>
                  <Label htmlFor="employmentType">Employment Type *</Label>
                  <select
                    id="employmentType"
                    value={formData.employmentType}
                    onChange={(e) => handleInputChange('employmentType', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                    required
                  >
                    <option value="FTE">Full-Time Employee</option>
                    <option value="CONTRACTOR">Contractor</option>
                    <option value="VENDOR">Vendor Resource</option>
                  </select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Position Details */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Users className="h-5 w-5" />
                <span>Position Details</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="positions">Number of Positions *</Label>
                  <Input
                    id="positions"
                    type="number"
                    min="1"
                    value={formData.positions}
                    onChange={(e) => handleInputChange('positions', e.target.value)}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="location">Location *</Label>
                  <Input
                    id="location"
                    value={formData.location}
                    onChange={(e) => handleInputChange('location', e.target.value)}
                    placeholder="e.g., Bangalore, Mumbai, Remote"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="priority">Priority</Label>
                  <select
                    id="priority"
                    value={formData.priority}
                    onChange={(e) => handleInputChange('priority', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                  >
                    <option value="LOW">Low</option>
                    <option value="MEDIUM">Medium</option>
                    <option value="HIGH">High</option>
                    <option value="URGENT">Urgent</option>
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="workShift">Work Shift</Label>
                  <select
                    id="workShift"
                    value={formData.workShift}
                    onChange={(e) => handleInputChange('workShift', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                  >
                    <option value="">Select Shift</option>
                    <option value="Day">Day Shift</option>
                    <option value="Night">Night Shift</option>
                    <option value="Rotational">Rotational</option>
                    <option value="Flexible">Flexible</option>
                  </select>
                </div>
                <div>
                  <Label htmlFor="workMode">Work Mode</Label>
                  <select
                    id="workMode"
                    value={formData.workMode}
                    onChange={(e) => handleInputChange('workMode', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                  >
                    <option value="">Select Work Mode</option>
                    <option value="On-site">On-site</option>
                    <option value="Remote">Remote</option>
                    <option value="Hybrid">Hybrid</option>
                  </select>
                </div>
                <div>
                  <Label htmlFor="minExperience">Min Experience (Years)</Label>
                  <Input
                    id="minExperience"
                    type="number"
                    min="0"
                    value={formData.minExperience}
                    onChange={(e) => handleInputChange('minExperience', e.target.value)}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Skills & Requirements */}
          <Card>
            <CardHeader>
              <CardTitle>Skills & Requirements</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label>Required Skills *</Label>
                {formData.requiredSkills.map((skill, index) => (
                  <div key={index} className="flex items-center space-x-2 mt-2">
                    <Input
                      value={skill}
                      onChange={(e) => handleArrayChange('requiredSkills', index, e.target.value)}
                      placeholder="e.g., JavaScript, Welding, Sales Experience"
                    />
                    {formData.requiredSkills.length > 1 && (
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => removeArrayItem('requiredSkills', index)}
                      >
                        Remove
                      </Button>
                    )}
                  </div>
                ))}
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => addArrayItem('requiredSkills')}
                  className="mt-2"
                >
                  Add Skill
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Compensation */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <DollarSign className="h-5 w-5" />
                <span>Compensation & Timeline</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="minSalary">Minimum Salary</Label>
                  <Input
                    id="minSalary"
                    type="number"
                    min="0"
                    value={formData.minSalary}
                    onChange={(e) => handleInputChange('minSalary', e.target.value)}
                    placeholder="e.g., 50000"
                  />
                </div>
                <div>
                  <Label htmlFor="maxSalary">Maximum Salary</Label>
                  <Input
                    id="maxSalary"
                    type="number"
                    min="0"
                    value={formData.maxSalary}
                    onChange={(e) => handleInputChange('maxSalary', e.target.value)}
                    placeholder="e.g., 80000"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="applicationDeadline">Application Deadline</Label>
                  <Input
                    id="applicationDeadline"
                    type="date"
                    value={formData.applicationDeadline}
                    onChange={(e) => handleInputChange('applicationDeadline', e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="expectedJoiningDate">Expected Joining Date</Label>
                  <Input
                    id="expectedJoiningDate"
                    type="date"
                    value={formData.expectedJoiningDate}
                    onChange={(e) => handleInputChange('expectedJoiningDate', e.target.value)}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Submit Buttons */}
          <div className="flex justify-end space-x-4">
            <Link href="/bulk-hiring">
              <Button type="button" variant="outline">
                Cancel
              </Button>
            </Link>
            <Button 
              type="submit" 
              className="bg-red-600 hover:bg-red-700"
              disabled={createJobMutation.isPending}
            >
              <Save className="h-4 w-4 mr-2" />
              {createJobMutation.isPending ? 'Creating...' : 'Create Job'}
            </Button>
          </div>
        </form>
      </div>
    </DashboardLayout>
  );
}
