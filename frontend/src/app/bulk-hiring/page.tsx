'use client';

import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { apiRequest } from '@/lib/api';
import { useAuth } from '@/hooks/useAuth';
import { 
  Users, 
  Search, 
  Filter, 
  Plus, 
  Eye, 
  Edit,
  Play,
  Pause,
  X,
  Briefcase,
  MapPin,
  Calendar,
  DollarSign,
  UserCheck,
  Building,
  Clock,
  Target,
  TrendingUp
} from 'lucide-react';
import Link from 'next/link';
import { toast } from 'sonner';

export default function BulkHiringPage() {
  const { hasRole } = useAuth();
  const queryClient = useQueryClient();
  const [searchTerm, setSearchTerm] = useState('');
  const [status, setStatus] = useState('');
  const [jobType, setJobType] = useState('');
  const [hiringType, setHiringType] = useState('');
  const [department, setDepartment] = useState('');
  const [showCreateModal, setShowCreateModal] = useState(false);

  const { data: jobsData, isLoading } = useQuery({
    queryKey: ['bulk-hiring-jobs', status, jobType, hiringType, department],
    queryFn: () => apiRequest.get('/bulk-hiring/jobs', { 
      status: status || undefined,
      jobType: jobType || undefined,
      hiringType: hiringType || undefined,
      department: department || undefined,
    }),
  });

  const publishJobMutation = useMutation({
    mutationFn: (jobId: string) => apiRequest.patch(`/bulk-hiring/jobs/${jobId}/publish`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['bulk-hiring-jobs'] });
      toast.success('Job published successfully');
    },
    onError: () => {
      toast.error('Failed to publish job');
    },
  });

  const jobs = jobsData?.data || [];

  const filteredJobs = jobs.filter((job: any) =>
    searchTerm === '' ||
    job.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    job.department.toLowerCase().includes(searchTerm.toLowerCase()) ||
    job.location.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'DRAFT': return 'bg-gray-100 text-gray-800';
      case 'PUBLISHED': return 'bg-blue-100 text-blue-800';
      case 'ACTIVE': return 'bg-green-100 text-green-800';
      case 'ON_HOLD': return 'bg-yellow-100 text-yellow-800';
      case 'CLOSED': return 'bg-purple-100 text-purple-800';
      case 'CANCELLED': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getJobTypeColor = (type: string) => {
    switch (type) {
      case 'BLUE_COLLAR': return 'bg-blue-100 text-blue-800';
      case 'WHITE_COLLAR': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getHiringTypeColor = (type: string) => {
    switch (type) {
      case 'BULK': return 'bg-orange-100 text-orange-800';
      case 'ADHOC': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="spinner"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Bulk Hiring Management</h1>
            <p className="text-gray-600">Manage bulk and adhoc hiring for blue collar and white collar positions</p>
          </div>
          {hasRole(['ADMIN', 'HR_MANAGER']) && (
            <Link href="/bulk-hiring/create">
              <Button className="bg-red-600 hover:bg-red-700">
                <Plus className="h-4 w-4 mr-2" />
                Create Hiring Job
              </Button>
            </Link>
          )}
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Briefcase className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Jobs</p>
                  <p className="text-2xl font-bold text-gray-900">{jobs.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <TrendingUp className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Active Jobs</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {jobs.filter((j: any) => j.status === 'ACTIVE' || j.status === 'PUBLISHED').length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Building className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Blue Collar</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {jobs.filter((j: any) => j.jobType === 'BLUE_COLLAR').length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <UserCheck className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">White Collar</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {jobs.filter((j: any) => j.jobType === 'WHITE_COLLAR').length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search jobs..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <select
                value={status}
                onChange={(e) => setStatus(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
              >
                <option value="">All Status</option>
                <option value="DRAFT">Draft</option>
                <option value="PUBLISHED">Published</option>
                <option value="ACTIVE">Active</option>
                <option value="ON_HOLD">On Hold</option>
                <option value="CLOSED">Closed</option>
                <option value="CANCELLED">Cancelled</option>
              </select>
              <select
                value={jobType}
                onChange={(e) => setJobType(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
              >
                <option value="">All Job Types</option>
                <option value="BLUE_COLLAR">Blue Collar</option>
                <option value="WHITE_COLLAR">White Collar</option>
              </select>
              <select
                value={hiringType}
                onChange={(e) => setHiringType(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
              >
                <option value="">All Hiring Types</option>
                <option value="BULK">Bulk Hiring</option>
                <option value="ADHOC">Adhoc Hiring</option>
              </select>
              <select
                value={department}
                onChange={(e) => setDepartment(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
              >
                <option value="">All Departments</option>
                <option value="Engineering">Engineering</option>
                <option value="Operations">Operations</option>
                <option value="Manufacturing">Manufacturing</option>
                <option value="Sales">Sales</option>
                <option value="Marketing">Marketing</option>
                <option value="HR">HR</option>
                <option value="Finance">Finance</option>
              </select>
              <Button variant="outline" onClick={() => {
                setSearchTerm('');
                setStatus('');
                setJobType('');
                setHiringType('');
                setDepartment('');
              }}>
                <Filter className="h-4 w-4 mr-2" />
                Clear
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Jobs List */}
        <div className="grid gap-6">
          {filteredJobs.length === 0 ? (
            <Card>
              <CardContent className="text-center py-12">
                <Briefcase className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Hiring Jobs Found</h3>
                <p className="text-gray-600">
                  {searchTerm || status || jobType || hiringType || department
                    ? 'No hiring jobs match your search criteria.'
                    : 'No hiring jobs have been created yet.'
                  }
                </p>
                {hasRole(['ADMIN', 'HR_MANAGER']) && (
                  <Link href="/bulk-hiring/create">
                    <Button className="mt-4 bg-red-600 hover:bg-red-700">
                      <Plus className="h-4 w-4 mr-2" />
                      Create First Job
                    </Button>
                  </Link>
                )}
              </CardContent>
            </Card>
          ) : (
            filteredJobs.map((job: any) => (
              <Card key={job.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-3">
                        <h3 className="text-xl font-semibold text-gray-900">{job.title}</h3>
                        <Badge className={getStatusColor(job.status)}>
                          {job.status}
                        </Badge>
                        <Badge className={getJobTypeColor(job.jobType)}>
                          {job.jobType.replace('_', ' ')}
                        </Badge>
                        <Badge className={getHiringTypeColor(job.hiringType)}>
                          {job.hiringType}
                        </Badge>
                      </div>
                      
                      <p className="text-gray-600 mb-4 line-clamp-2">{job.description}</p>
                      
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-4">
                        <div className="flex items-center space-x-2">
                          <Building className="h-4 w-4 text-gray-400" />
                          <span>{job.department}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <MapPin className="h-4 w-4 text-gray-400" />
                          <span>{job.location}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Target className="h-4 w-4 text-gray-400" />
                          <span>{job.positions} positions</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Users className="h-4 w-4 text-gray-400" />
                          <span>{job.total_applications || 0} applications</span>
                        </div>
                      </div>

                      <div className="flex items-center space-x-4 text-sm text-gray-600">
                        <span>Created by {job.creator_firstName} {job.creator_lastName}</span>
                        <span>•</span>
                        <span>{new Date(job.createdAt).toLocaleDateString()}</span>
                        {job.minSalary && (
                          <>
                            <span>•</span>
                            <span>${job.minSalary}{job.maxSalary ? ` - $${job.maxSalary}` : '+'}</span>
                          </>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex space-x-2 ml-4">
                      <Link href={`/bulk-hiring/${job.id}`}>
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4 mr-1" />
                          View
                        </Button>
                      </Link>
                      {hasRole(['ADMIN', 'HR_MANAGER']) && job.status === 'DRAFT' && (
                        <Button 
                          size="sm" 
                          className="bg-green-600 hover:bg-green-700"
                          onClick={() => publishJobMutation.mutate(job.id)}
                          disabled={publishJobMutation.isPending}
                        >
                          <Play className="h-4 w-4 mr-1" />
                          {publishJobMutation.isPending ? 'Publishing...' : 'Publish'}
                        </Button>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}
