'use client';

import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/hooks/useAuth';
import { formatCurrency, formatDate } from '@/lib/utils';
import { invoicesApi } from '@/lib/api';
import { InvoiceForm } from '@/components/forms/invoice-form';
import { Plus, Search, Filter, Eye, Edit, Download, Send, DollarSign, Calendar } from 'lucide-react';

export default function InvoicesPage() {
  const { hasRole } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [showInvoiceForm, setShowInvoiceForm] = useState(false);
  const [selectedInvoice, setSelectedInvoice] = useState<any>(null);
  const [formMode, setFormMode] = useState<'create' | 'edit'>('create');

  // Fetch invoices from API
  const { data: invoicesData, isLoading, error } = useQuery({
    queryKey: ['invoices', { search: searchTerm, status: statusFilter }],
    queryFn: () => invoicesApi.getAll({
      limit: 100,
      ...(searchTerm && { search: searchTerm }),
      ...(statusFilter && { status: statusFilter })
    }),
  });

  const invoices = invoicesData?.data || [];

  const handleCreateInvoice = () => {
    setSelectedInvoice(null);
    setFormMode('create');
    setShowInvoiceForm(true);
  };

  const handleEditInvoice = (invoice: any) => {
    setSelectedInvoice(invoice);
    setFormMode('edit');
    setShowInvoiceForm(true);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'DRAFT': return 'status-draft';
      case 'SENT': return 'status-pending';
      case 'PAID': return 'status-completed';
      case 'OVERDUE': return 'status-cancelled';
      case 'CANCELLED': return 'status-cancelled';
      default: return 'status-inactive';
    }
  };

  const getTotalAmount = () => {
    return invoices.reduce((sum, invoice) => sum + (invoice.total || invoice.amount || 0), 0);
  };

  const getPaidAmount = () => {
    return invoices.filter(inv => inv.status === 'PAID').reduce((sum, invoice) => sum + (invoice.total || invoice.amount || 0), 0);
  };

  const getOutstandingAmount = () => {
    return invoices.filter(inv => inv.status !== 'PAID').reduce((sum, invoice) => sum + (invoice.total || invoice.amount || 0), 0);
  };

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="space-y-6">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
              {[1, 2, 3].map(i => (
                <div key={i} className="h-24 bg-gray-200 rounded"></div>
              ))}
            </div>
            <div className="space-y-4">
              {[1, 2, 3].map(i => (
                <div key={i} className="h-32 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Invoices</h1>
            <p className="text-gray-600">Manage billing and payments</p>
          </div>
          {hasRole(['ADMIN', 'BILLING_MANAGER']) && (
            <Button
              className="redwood-button-primary"
              onClick={handleCreateInvoice}
            >
              <Plus className="h-4 w-4 mr-2" />
              New Invoice
            </Button>
          )}
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="redwood-card">
            <CardContent className="p-6">
              <div className="flex items-center">
                <DollarSign className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Invoiced</p>
                  <p className="text-2xl font-bold text-gray-900">{formatCurrency(getTotalAmount())}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="redwood-card">
            <CardContent className="p-6">
              <div className="flex items-center">
                <DollarSign className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Paid</p>
                  <p className="text-2xl font-bold text-gray-900">{formatCurrency(getPaidAmount())}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="redwood-card">
            <CardContent className="p-6">
              <div className="flex items-center">
                <DollarSign className="h-8 w-8 text-red-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Outstanding</p>
                  <p className="text-2xl font-bold text-gray-900">{formatCurrency(getOutstandingAmount())}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card className="redwood-card">
          <CardContent className="p-4">
            <div className="flex items-center space-x-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search invoices..."
                    className="redwood-input pl-10 w-full"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>
              <select
                className="redwood-input"
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
              >
                <option value="">All Status</option>
                <option value="DRAFT">Draft</option>
                <option value="SENT">Sent</option>
                <option value="PAID">Paid</option>
                <option value="OVERDUE">Overdue</option>
                <option value="CANCELLED">Cancelled</option>
              </select>
              <input
                type="month"
                className="redwood-input"
                placeholder="Month"
              />
              <Button variant="outline">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Invoices List */}
        <div className="grid gap-4">
          {invoices.map((invoice) => (
            <Card key={invoice.id} className="redwood-card">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="text-lg text-gray-900">
                      {invoice.invoiceNumber}
                    </CardTitle>
                    <CardDescription className="mt-1">
                      {invoice.client} • {invoice.project}
                    </CardDescription>
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-gray-900">
                      {formatCurrency(invoice.total || invoice.amount || 0, invoice.currency)}
                    </div>
                    <Badge className={getStatusColor(invoice.status)}>
                      {invoice.status}
                    </Badge>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 mb-4">{invoice.description}</p>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                  <div className="flex items-center text-sm">
                    <Calendar className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-gray-600">Issue Date:</span>
                    <span className="ml-1 font-medium">{formatDate(invoice.issueDate)}</span>
                  </div>
                  
                  <div className="flex items-center text-sm">
                    <Calendar className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-gray-600">Due Date:</span>
                    <span className="ml-1 font-medium">{formatDate(invoice.dueDate)}</span>
                  </div>

                  {invoice.paidDate && (
                    <div className="flex items-center text-sm">
                      <Calendar className="h-4 w-4 text-gray-400 mr-2" />
                      <span className="text-gray-600">Paid Date:</span>
                      <span className="ml-1 font-medium">{formatDate(invoice.paidDate)}</span>
                    </div>
                  )}
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-2">
                  <Button variant="outline" size="sm">
                    <Eye className="h-4 w-4 mr-1" />
                    View
                  </Button>
                  
                  <Button variant="outline" size="sm">
                    <Download className="h-4 w-4 mr-1" />
                    Download PDF
                  </Button>
                  
                  {hasRole(['ADMIN', 'BILLING_MANAGER']) && (
                    <>
                      {invoice.status === 'DRAFT' && (
                        <>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEditInvoice(invoice)}
                          >
                            <Edit className="h-4 w-4 mr-1" />
                            Edit
                          </Button>
                          <Button className="redwood-button-primary" size="sm">
                            <Send className="h-4 w-4 mr-1" />
                            Send
                          </Button>
                        </>
                      )}
                      
                      {(invoice.status === 'SENT' || invoice.status === 'OVERDUE') && (
                        <Button className="bg-green-600 hover:bg-green-700 text-white" size="sm">
                          Mark as Paid
                        </Button>
                      )}
                    </>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {invoices.length === 0 && (
          <Card className="redwood-card">
            <CardContent className="text-center py-12">
              <p className="text-gray-500 mb-4">No invoices found</p>
              {hasRole(['ADMIN', 'BILLING_MANAGER']) && (
                <Button
                  className="redwood-button-primary"
                  onClick={handleCreateInvoice}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Create First Invoice
                </Button>
              )}
            </CardContent>
          </Card>
        )}
      </div>

      {/* Invoice Form Modal */}
      <InvoiceForm
        isOpen={showInvoiceForm}
        onClose={() => setShowInvoiceForm(false)}
        invoice={selectedInvoice}
        mode={formMode}
      />
    </DashboardLayout>
  );
}
