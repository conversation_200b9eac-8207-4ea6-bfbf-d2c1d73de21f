'use client';

import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/hooks/useAuth';
import { getInitials } from '@/lib/utils';
import { vendorsApi } from '@/lib/api';
import { VendorForm } from '@/components/forms/vendor-form';
import { VendorView } from '@/components/vendor-view';
import { Plus, Search, Filter, Eye, Edit, Mail, Phone, MapPin, Building, Star } from 'lucide-react';

export default function VendorsPage() {
  const { hasRole } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('');
  const [showVendorForm, setShowVendorForm] = useState(false);
  const [showVendorView, setShowVendorView] = useState(false);
  const [selectedVendor, setSelectedVendor] = useState<any>(null);
  const [formMode, setFormMode] = useState<'create' | 'edit'>('create');

  // Fetch vendors from API
  const { data: vendorsData, isLoading, error } = useQuery({
    queryKey: ['vendors', { search: searchTerm, status: statusFilter, category: categoryFilter }],
    queryFn: () => vendorsApi.getAll({
      limit: 100,
      ...(searchTerm && { search: searchTerm }),
      ...(statusFilter && { status: statusFilter }),
      ...(categoryFilter && { category: categoryFilter })
    }),
  });

  const vendors = vendorsData?.data || [];

  const handleCreateVendor = () => {
    setSelectedVendor(null);
    setFormMode('create');
    setShowVendorForm(true);
  };

  const handleEditVendor = (vendor: any) => {
    setSelectedVendor(vendor);
    setFormMode('edit');
    setShowVendorForm(true);
  };

  const handleViewVendor = (vendor: any) => {
    setSelectedVendor(vendor);
    setShowVendorView(true);
  };

  const handleEditFromView = () => {
    setShowVendorView(false);
    setFormMode('edit');
    setShowVendorForm(true);
  };

  const getStatusColor = (status: string | undefined | null) => {
    if (!status) return 'status-inactive';

    switch (status) {
      case 'ACTIVE': return 'status-active';
      case 'PENDING': return 'status-pending';
      case 'INACTIVE': return 'status-inactive';
      case 'SUSPENDED': return 'status-cancelled';
      default: return 'status-inactive';
    }
  };

  const getCategoryColor = (category: string | undefined | null) => {
    if (!category) return 'bg-gray-100 text-gray-800';

    switch (category) {
      case 'SOFTWARE_DEVELOPMENT': return 'bg-blue-100 text-blue-800';
      case 'DESIGN': return 'bg-purple-100 text-purple-800';
      case 'CONSULTING': return 'bg-green-100 text-green-800';
      case 'MARKETING': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatCategory = (category: string | undefined | null) => {
    if (!category) return 'Uncategorized';
    return category.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
  };

  const renderStars = (rating: number | undefined | null) => {
    const safeRating = rating || 0;
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${i < Math.floor(safeRating) ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
      />
    ));
  };

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="space-y-6">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
            <div className="h-16 bg-gray-200 rounded mb-6"></div>
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {[1, 2, 3, 4, 5, 6].map(i => (
                <div key={i} className="h-80 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Vendors</h1>
            <p className="text-gray-600">Manage vendor relationships and contracts</p>
          </div>
          {hasRole(['ADMIN', 'HR_MANAGER']) && (
            <Button
              className="redwood-button-primary"
              onClick={handleCreateVendor}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Vendor
            </Button>
          )}
        </div>

        {/* Filters */}
        <Card className="redwood-card">
          <CardContent className="p-4">
            <div className="flex items-center space-x-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search vendors..."
                    className="redwood-input pl-10 w-full"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>
              <select
                className="redwood-input"
                value={categoryFilter}
                onChange={(e) => setCategoryFilter(e.target.value)}
              >
                <option value="">All Categories</option>
                <option value="SOFTWARE_DEVELOPMENT">Software Development</option>
                <option value="DESIGN">Design</option>
                <option value="CONSULTING">Consulting</option>
                <option value="MARKETING">Marketing</option>
              </select>
              <select
                className="redwood-input"
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
              >
                <option value="">All Status</option>
                <option value="ACTIVE">Active</option>
                <option value="PENDING">Pending</option>
                <option value="INACTIVE">Inactive</option>
                <option value="SUSPENDED">Suspended</option>
              </select>
              <Button variant="outline">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Vendors Grid */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {vendors.map((vendor) => (
            <Card key={vendor.id} className="redwood-card">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                      <Building className="h-6 w-6 text-red-600" />
                    </div>
                    <div>
                      <CardTitle className="text-lg text-gray-900">{vendor.name || 'Unnamed Vendor'}</CardTitle>
                      <CardDescription>{vendor.contactPerson || 'No contact person'}</CardDescription>
                    </div>
                  </div>
                  <Badge className={getStatusColor(vendor.status)}>
                    {vendor.status || 'Unknown'}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                  {vendor.description || 'No description available'}
                </p>

                {/* Category */}
                <div className="mb-4">
                  <Badge className={getCategoryColor(vendor.category)}>
                    {formatCategory(vendor.category)}
                  </Badge>
                </div>

                {/* Contact Info */}
                <div className="space-y-2 mb-4">
                  <div className="flex items-center text-sm text-gray-600">
                    <Mail className="h-4 w-4 mr-2" />
                    <span className="truncate">{vendor.email || 'No email provided'}</span>
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <Phone className="h-4 w-4 mr-2" />
                    <span>{vendor.phone || 'No phone provided'}</span>
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <MapPin className="h-4 w-4 mr-2" />
                    <span className="truncate">{vendor.address || 'No address provided'}</span>
                  </div>
                </div>

                {/* Rating */}
                <div className="flex items-center mb-4">
                  <div className="flex items-center mr-2">
                    {renderStars(vendor.rating || 0)}
                  </div>
                  <span className="text-sm font-medium text-gray-700">{vendor.rating || 'No rating'}</span>
                </div>

                {/* Stats */}
                <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                  <div>
                    <span className="text-gray-600">Contracts:</span>
                    <span className="ml-1 font-medium">{vendor.contractsCount || 0}</span>
                  </div>
                  <div>
                    <span className="text-gray-600">Total Value:</span>
                    <span className="ml-1 font-medium">${(vendor.totalValue || 0).toLocaleString()}</span>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex-1"
                    onClick={() => handleViewVendor(vendor)}
                  >
                    <Eye className="h-4 w-4 mr-1" />
                    View
                  </Button>
                  {hasRole(['ADMIN', 'HR_MANAGER']) && (
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1"
                      onClick={() => handleEditVendor(vendor)}
                    >
                      <Edit className="h-4 w-4 mr-1" />
                      Edit
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {vendors.length === 0 && (
          <Card className="redwood-card">
            <CardContent className="text-center py-12">
              <p className="text-gray-500 mb-4">No vendors found</p>
              {hasRole(['ADMIN', 'HR_MANAGER']) && (
                <Button
                  className="redwood-button-primary"
                  onClick={handleCreateVendor}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add First Vendor
                </Button>
              )}
            </CardContent>
          </Card>
        )}
      </div>

      {/* Vendor Form Modal */}
      <VendorForm
        isOpen={showVendorForm}
        onClose={() => setShowVendorForm(false)}
        vendor={selectedVendor}
        mode={formMode}
      />

      {/* Vendor View Modal */}
      <VendorView
        isOpen={showVendorView}
        onClose={() => setShowVendorView(false)}
        vendor={selectedVendor}
        onEdit={handleEditFromView}
      />
    </DashboardLayout>
  );
}
