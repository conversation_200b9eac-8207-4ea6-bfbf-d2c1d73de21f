'use client';

import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { TaskForm } from '@/components/forms/task-form';
import { tasksApi } from '@/lib/api';
import { useAuth } from '@/hooks/useAuth';
import { formatDate, getStatusColor } from '@/lib/utils';
import { Plus, Search, Filter, Eye, Edit, Clock, User, Calendar, Trash2 } from 'lucide-react';

export default function TasksPage() {
  const { hasRole } = useAuth();
  const queryClient = useQueryClient();
  const [page, setPage] = useState(1);
  const [status, setStatus] = useState('');
  const [priority, setPriority] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [modalState, setModalState] = useState<{
    isOpen: boolean;
    mode: 'create' | 'edit' | 'view';
    task?: any;
  }>({
    isOpen: false,
    mode: 'create',
    task: null,
  });

  const { data: tasksData, isLoading } = useQuery({
    queryKey: ['tasks', page, status, priority, searchTerm],
    queryFn: () => tasksApi.getAll({
      page,
      limit: 10,
      status: status || undefined,
      priority: priority || undefined,
      search: searchTerm || undefined
    }),
  });

  const tasks = tasksData?.data || [];
  const pagination = tasksData?.pagination;

  // Delete mutation
  const deleteMutation = useMutation({
    mutationFn: (id: string) => tasksApi.delete(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
    },
  });

  const handleOpenModal = (mode: 'create' | 'edit' | 'view', task?: any) => {
    setModalState({ isOpen: true, mode, task });
  };

  const handleCloseModal = () => {
    setModalState({ isOpen: false, mode: 'create', task: null });
  };

  const handleDelete = (task: any) => {
    if (window.confirm(`Are you sure you want to delete "${task.title}"?`)) {
      deleteMutation.mutate(task.id);
    }
  };

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="spinner"></div>
        </div>
      </DashboardLayout>
    );
  }



  const getStatusColor = (status: string) => {
    switch (status) {
      case 'TODO': return 'status-pending';
      case 'IN_PROGRESS': return 'status-active';
      case 'COMPLETED': return 'status-completed';
      case 'CANCELLED': return 'status-cancelled';
      default: return 'status-inactive';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'LOW': return 'priority-low';
      case 'MEDIUM': return 'priority-medium';
      case 'HIGH': return 'priority-high';
      case 'CRITICAL': return 'priority-critical';
      default: return 'priority-low';
    }
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Tasks</h1>
            <p className="text-gray-600">Manage and track task progress</p>
          </div>
          {hasRole(['ADMIN', 'PROJECT_MANAGER']) && (
            <Button
              className="redwood-button-primary"
              onClick={() => handleOpenModal('create')}
            >
              <Plus className="h-4 w-4 mr-2" />
              New Task
            </Button>
          )}
        </div>

        {/* Filters */}
        <Card className="redwood-card">
          <CardContent className="p-4">
            <div className="flex items-center space-x-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search tasks..."
                    className="redwood-input pl-10 w-full"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>
              <select
                className="redwood-input"
                value={status}
                onChange={(e) => setStatus(e.target.value)}
              >
                <option value="">All Status</option>
                <option value="TODO">To Do</option>
                <option value="IN_PROGRESS">In Progress</option>
                <option value="REVIEW">Review</option>
                <option value="COMPLETED">Completed</option>
                <option value="CANCELLED">Cancelled</option>
              </select>
              <select
                className="redwood-input"
                value={priority}
                onChange={(e) => setPriority(e.target.value)}
              >
                <option value="">All Priority</option>
                <option value="LOW">Low</option>
                <option value="MEDIUM">Medium</option>
                <option value="HIGH">High</option>
                <option value="CRITICAL">Critical</option>
              </select>
              <Button variant="outline">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Tasks List */}
        <div className="grid gap-4">
          {tasks.map((task) => (
            <Card key={task.id} className="redwood-card">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-lg text-gray-900">{task.title}</CardTitle>
                    <CardDescription className="mt-1">
                      {task.description}
                    </CardDescription>
                  </div>
                  <div className="flex space-x-2">
                    <Badge className={getStatusColor(task.status)}>
                      {task.status.replace('_', ' ')}
                    </Badge>
                    <Badge className={getPriorityColor(task.priority)}>
                      {task.priority}
                    </Badge>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                  <div className="flex items-center text-sm">
                    <User className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-gray-600">Assignee:</span>
                    <span className="ml-1 font-medium">
                      {task.firstName && task.lastName ? `${task.firstName} ${task.lastName}` : 'Unassigned'}
                    </span>
                  </div>

                  <div className="flex items-center text-sm">
                    <Calendar className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-gray-600">Due:</span>
                    <span className="ml-1 font-medium">{formatDate(task.dueDate)}</span>
                  </div>

                  <div className="flex items-center text-sm">
                    <Clock className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-gray-600">Hours:</span>
                    <span className="ml-1 font-medium">{task.actualHours || 0}/{task.estimatedHours}h</span>
                  </div>

                  <div className="text-sm">
                    <span className="text-gray-600">Project:</span>
                    <span className="ml-1 font-medium">{task.project_name}</span>
                  </div>
                </div>

                {/* Progress Bar */}
                <div className="mb-4">
                  <div className="flex items-center justify-between text-sm mb-1">
                    <span className="text-gray-600">Progress</span>
                    <span className="font-medium">
                      {Math.round(((task.actualHours || 0) / task.estimatedHours) * 100)}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-red-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${Math.min(((task.actualHours || 0) / task.estimatedHours) * 100, 100)}%` }}
                    ></div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleOpenModal('view', task)}
                  >
                    <Eye className="h-4 w-4 mr-1" />
                    View
                  </Button>
                  {hasRole(['ADMIN', 'PROJECT_MANAGER', 'RESOURCE']) && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleOpenModal('edit', task)}
                    >
                      <Edit className="h-4 w-4 mr-1" />
                      Edit
                    </Button>
                  )}
                  {hasRole(['ADMIN', 'PROJECT_MANAGER']) && (
                    <Button
                      variant="outline"
                      size="sm"
                      className="text-red-600 hover:text-red-700"
                      onClick={() => handleDelete(task)}
                      disabled={deleteMutation.isPending}
                    >
                      <Trash2 className="h-4 w-4 mr-1" />
                      Delete
                    </Button>
                  )}
                  {task.status !== 'COMPLETED' && hasRole(['RESOURCE']) && (
                    <Button className="redwood-button-primary" size="sm">
                      <Clock className="h-4 w-4 mr-1" />
                      Log Time
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Pagination */}
        {pagination && pagination.totalPages > 1 && (
          <div className="flex items-center justify-between">
            <p className="text-sm text-gray-700">
              Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
              {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
              {pagination.total} results
            </p>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                onClick={() => setPage(page - 1)}
                disabled={page === 1}
              >
                Previous
              </Button>
              <Button
                variant="outline"
                onClick={() => setPage(page + 1)}
                disabled={page === pagination.totalPages}
              >
                Next
              </Button>
            </div>
          </div>
        )}

        {tasks.length === 0 && (
          <Card className="redwood-card">
            <CardContent className="text-center py-12">
              <p className="text-gray-500 mb-4">No tasks found</p>
              {hasRole(['ADMIN', 'PROJECT_MANAGER']) && (
                <Button
                  className="redwood-button-primary"
                  onClick={() => handleOpenModal('create')}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Create First Task
                </Button>
              )}
            </CardContent>
          </Card>
        )}

        {/* Task Form Modal */}
        <TaskForm
          isOpen={modalState.isOpen}
          onClose={handleCloseModal}
          task={modalState.task}
          mode={modalState.mode}
        />
      </div>
    </DashboardLayout>
  );
}
