'use client';

import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useAuth } from '@/hooks/useAuth';
import { formatCurrency, formatDate } from '@/lib/utils';
import { invoicesApi, paymentsApi } from '@/lib/api';
import { PayrollProcessForm } from '@/components/forms/payroll-process-form';
import {
  DollarSign,
  CreditCard,
  Building,
  User,
  Calendar,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Clock,
  XCircle,
  Plus,
  Search,
  Filter,
  Download,
  Send,
  Receipt
} from 'lucide-react';

export default function BillingPage() {
  const { hasRole } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [showPayrollForm, setShowPayrollForm] = useState(false);
  const [showInvoiceSelector, setShowInvoiceSelector] = useState(false);
  const [selectedInvoice, setSelectedInvoice] = useState<any>(null);

  // Fetch invoices for payroll processing
  const { data: invoicesData, isLoading: invoicesLoading } = useQuery({
    queryKey: ['invoices', { status: 'SENT,APPROVED' }],
    queryFn: () => invoicesApi.getAll({ 
      status: 'SENT,APPROVED',
      limit: 100 
    }),
  });

  // Fetch payments data
  const { data: paymentsData, isLoading: paymentsLoading } = useQuery({
    queryKey: ['payments', { search: searchTerm, status: statusFilter }],
    queryFn: () => paymentsApi.getAll({
      ...(searchTerm && { search: searchTerm }),
      ...(statusFilter && { status: statusFilter }),
      limit: 100
    }),
  });

  // Fetch billing statistics
  const { data: billingStats } = useQuery({
    queryKey: ['billing-stats'],
    queryFn: () => paymentsApi.getStats(),
  });

  const invoices = invoicesData?.data || [];
  const payments = paymentsData?.data || [];
  const stats = billingStats?.data || {};

  const handleProcessPayroll = (invoice: any) => {
    setSelectedInvoice(invoice);
    setShowPayrollForm(true);
  };

  const handleProcessPaymentClick = () => {
    setShowInvoiceSelector(true);
  };

  const handleInvoiceSelected = (invoice: any) => {
    setSelectedInvoice(invoice);
    setShowInvoiceSelector(false);
    setShowPayrollForm(true);
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED': return 'bg-green-100 text-green-800';
      case 'PROCESSING': return 'bg-blue-100 text-blue-800';
      case 'PENDING': return 'bg-yellow-100 text-yellow-800';
      case 'FAILED': return 'bg-red-100 text-red-800';
      case 'CANCELLED': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPaymentTypeIcon = (type: string) => {
    switch (type) {
      case 'PAYROLL': return <User className="h-4 w-4" />;
      case 'CONTRACTOR_DIRECT': return <CreditCard className="h-4 w-4" />;
      case 'VENDOR_PAYMENT': return <Building className="h-4 w-4" />;
      default: return <DollarSign className="h-4 w-4" />;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'COMPLETED': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'PROCESSING': return <Clock className="h-4 w-4 text-blue-600" />;
      case 'PENDING': return <AlertCircle className="h-4 w-4 text-yellow-600" />;
      case 'FAILED': return <XCircle className="h-4 w-4 text-red-600" />;
      default: return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  if (invoicesLoading || paymentsLoading) {
    return (
      <DashboardLayout>
        <div className="space-y-6">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4 mb-6">
              {[1, 2, 3, 4].map(i => (
                <div key={i} className="h-32 bg-gray-200 rounded"></div>
              ))}
            </div>
            <div className="h-96 bg-gray-200 rounded"></div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Billing Management</h1>
            <p className="text-gray-600">Process payroll, manage payments, and track billing</p>
            <p className="text-sm text-gray-500 mt-1">
              Click "Process Payment" to select an invoice and process payment (direct contractor, vendor, or payroll)
            </p>
          </div>
          {hasRole(['ADMIN', 'BILLING_MANAGER']) && (
            <Button
              className="redwood-button-primary"
              onClick={handleProcessPaymentClick}
            >
              <Plus className="h-4 w-4 mr-2" />
              Process Payment
            </Button>
          )}
        </div>

        {/* Statistics Cards */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          <Card className="redwood-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Payments</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatCurrency(stats.totalPayments || 0)}
                  </p>
                </div>
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <DollarSign className="h-6 w-6 text-green-600" />
                </div>
              </div>
              <div className="mt-4 flex items-center text-sm">
                <TrendingUp className="h-4 w-4 text-green-600 mr-1" />
                <span className="text-green-600">+12.5%</span>
                <span className="text-gray-600 ml-1">from last month</span>
              </div>
            </CardContent>
          </Card>

          <Card className="redwood-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Pending Payments</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {stats.pendingCount || 0}
                  </p>
                </div>
                <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                  <Clock className="h-6 w-6 text-yellow-600" />
                </div>
              </div>
              <div className="mt-4 flex items-center text-sm">
                <span className="text-yellow-600">
                  {formatCurrency(stats.pendingAmount || 0)}
                </span>
                <span className="text-gray-600 ml-1">pending</span>
              </div>
            </CardContent>
          </Card>

          <Card className="redwood-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Processed This Month</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {stats.monthlyCount || 0}
                  </p>
                </div>
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <CheckCircle className="h-6 w-6 text-blue-600" />
                </div>
              </div>
              <div className="mt-4 flex items-center text-sm">
                <span className="text-blue-600">
                  {formatCurrency(stats.monthlyAmount || 0)}
                </span>
                <span className="text-gray-600 ml-1">processed</span>
              </div>
            </CardContent>
          </Card>

          <Card className="redwood-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Failed Payments</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {stats.failedCount || 0}
                  </p>
                </div>
                <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                  <XCircle className="h-6 w-6 text-red-600" />
                </div>
              </div>
              <div className="mt-4 flex items-center text-sm">
                <span className="text-red-600">
                  {formatCurrency(stats.failedAmount || 0)}
                </span>
                <span className="text-gray-600 ml-1">failed</span>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">Payment Overview</TabsTrigger>
            <TabsTrigger value="payroll">Payroll Processing</TabsTrigger>
            <TabsTrigger value="history">Payment History</TabsTrigger>
          </TabsList>

          {/* Payment Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            <Card className="redwood-card">
              <CardHeader>
                <CardTitle>Recent Payments</CardTitle>
                <CardDescription>Latest payment transactions and their status</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {payments.slice(0, 10).map((payment: any) => (
                    <div key={payment.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-4">
                        <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                          {getPaymentTypeIcon(payment.paymentType)}
                        </div>
                        <div>
                          <p className="font-medium text-gray-900">
                            {payment.resource?.user?.firstName} {payment.resource?.user?.lastName}
                            {payment.vendor && ` (via ${payment.vendor.name})`}
                          </p>
                          <p className="text-sm text-gray-600">
                            {payment.paymentType.replace('_', ' ')} • {payment.referenceNumber}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-4">
                        <div className="text-right">
                          <p className="font-medium text-gray-900">
                            {formatCurrency(payment.amount, payment.currency)}
                          </p>
                          <p className="text-sm text-gray-600">
                            {payment.paymentDate ? formatDate(payment.paymentDate) : 'Pending'}
                          </p>
                        </div>
                        <div className="flex items-center space-x-2">
                          {getStatusIcon(payment.status)}
                          <Badge className={getPaymentStatusColor(payment.status)}>
                            {payment.status}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Payroll Processing Tab */}
          <TabsContent value="payroll" className="space-y-6">
            <Card className="redwood-card">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Invoices Ready for Payment</CardTitle>
                    <CardDescription>
                      Process payroll for approved invoices. Click "Process" on individual invoices or use "Process Payment" to select from all unpaid invoices.
                    </CardDescription>
                  </div>
                  <Button
                    className="redwood-button-primary"
                    onClick={handleProcessPaymentClick}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Process Payment
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {invoices.filter((invoice: any) => !invoice.paidAt).map((invoice: any) => (
                    <div key={invoice.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-4">
                        <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                          <DollarSign className="h-6 w-6 text-blue-600" />
                        </div>
                        <div>
                          <p className="font-medium text-gray-900">
                            {invoice.invoiceNumber}
                          </p>
                          <p className="text-sm text-gray-600">
                            {invoice.resource?.user?.firstName} {invoice.resource?.user?.lastName}
                            {invoice.resource?.employmentType === 'CONTRACTOR' && invoice.resource?.vendor &&
                              ` • Vendor: ${invoice.resource.vendor.name}`
                            }
                          </p>
                          <p className="text-sm text-gray-600">
                            {invoice.project?.name} • Due: {formatDate(invoice.dueDate)}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-4">
                        <div className="text-right">
                          <p className="font-medium text-gray-900">
                            {formatCurrency(invoice.total, invoice.currency)}
                          </p>
                          <p className="text-sm text-gray-600">
                            {invoice.totalHours}h @ {formatCurrency(invoice.hourlyRate, invoice.currency)}/hr
                          </p>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge className="bg-blue-100 text-blue-800">
                            {invoice.resource?.employmentType === 'CONTRACTOR' && invoice.resource?.vendor
                              ? 'Vendor Payment'
                              : invoice.resource?.employmentType === 'CONTRACTOR'
                              ? 'Direct Payment'
                              : 'Payroll'
                            }
                          </Badge>
                          <Button
                            size="sm"
                            onClick={() => handleProcessPayroll(invoice)}
                            className="bg-green-600 hover:bg-green-700"
                          >
                            <Send className="h-4 w-4 mr-1" />
                            Process
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}

                  {invoices.filter((invoice: any) => !invoice.paidAt).length === 0 && (
                    <div className="text-center py-12">
                      <CheckCircle className="h-12 w-12 text-green-600 mx-auto mb-4" />
                      <p className="text-gray-500">No invoices pending payment</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Payment History Tab */}
          <TabsContent value="history" className="space-y-6">
            <Card className="redwood-card">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Payment History</CardTitle>
                    <CardDescription>Complete payment transaction history</CardDescription>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <input
                        type="text"
                        placeholder="Search payments..."
                        className="redwood-input pl-10"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                      />
                    </div>
                    <select
                      className="redwood-input"
                      value={statusFilter}
                      onChange={(e) => setStatusFilter(e.target.value)}
                    >
                      <option value="">All Status</option>
                      <option value="PENDING">Pending</option>
                      <option value="PROCESSING">Processing</option>
                      <option value="COMPLETED">Completed</option>
                      <option value="FAILED">Failed</option>
                      <option value="CANCELLED">Cancelled</option>
                    </select>
                    <Button variant="outline">
                      <Download className="h-4 w-4 mr-2" />
                      Export
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Payment</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Recipient</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Type</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Amount</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Date</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Status</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {payments.map((payment: any) => (
                        <tr key={payment.id} className="border-b hover:bg-gray-50">
                          <td className="py-3 px-4">
                            <div>
                              <p className="font-medium text-gray-900">{payment.referenceNumber}</p>
                              <p className="text-sm text-gray-600">{payment.invoice?.invoiceNumber}</p>
                            </div>
                          </td>
                          <td className="py-3 px-4">
                            <div>
                              <p className="font-medium text-gray-900">
                                {payment.resource?.user?.firstName} {payment.resource?.user?.lastName}
                              </p>
                              {payment.vendor && (
                                <p className="text-sm text-gray-600">via {payment.vendor.name}</p>
                              )}
                            </div>
                          </td>
                          <td className="py-3 px-4">
                            <div className="flex items-center space-x-2">
                              {getPaymentTypeIcon(payment.paymentType)}
                              <span className="text-sm">
                                {payment.paymentType.replace('_', ' ')}
                              </span>
                            </div>
                          </td>
                          <td className="py-3 px-4">
                            <div>
                              <p className="font-medium text-gray-900">
                                {formatCurrency(payment.amount, payment.currency)}
                              </p>
                              {payment.taxDeducted > 0 && (
                                <p className="text-sm text-gray-600">
                                  Tax: {formatCurrency(payment.taxDeducted, payment.currency)}
                                </p>
                              )}
                            </div>
                          </td>
                          <td className="py-3 px-4">
                            <p className="text-sm text-gray-900">
                              {payment.paymentDate ? formatDate(payment.paymentDate) : 'Pending'}
                            </p>
                          </td>
                          <td className="py-3 px-4">
                            <div className="flex items-center space-x-2">
                              {getStatusIcon(payment.status)}
                              <Badge className={getPaymentStatusColor(payment.status)}>
                                {payment.status}
                              </Badge>
                            </div>
                          </td>
                          <td className="py-3 px-4">
                            <div className="flex items-center space-x-2">
                              <Button variant="outline" size="sm">
                                View
                              </Button>
                              {payment.status === 'FAILED' && (
                                <Button size="sm" className="bg-orange-600 hover:bg-orange-700">
                                  Retry
                                </Button>
                              )}
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Invoice Selection Modal */}
        {showInvoiceSelector && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[80vh] overflow-y-auto">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-gray-900">Select Invoice to Process Payment</h2>
                <Button
                  variant="ghost"
                  onClick={() => setShowInvoiceSelector(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </Button>
              </div>

              <div className="space-y-4">
                <p className="text-gray-600 mb-4">
                  Choose an invoice to process payment. Only unpaid invoices are shown.
                </p>

                {invoices.filter((invoice: any) => !invoice.paidAt).length === 0 ? (
                  <div className="text-center py-12">
                    <Receipt className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">No unpaid invoices available for payment processing</p>
                    <Button
                      className="mt-4"
                      onClick={() => setShowInvoiceSelector(false)}
                    >
                      Close
                    </Button>
                  </div>
                ) : (
                  <div className="grid gap-4">
                    {invoices.filter((invoice: any) => !invoice.paidAt).map((invoice: any) => (
                      <div
                        key={invoice.id}
                        className="border rounded-lg p-4 hover:bg-gray-50 cursor-pointer transition-colors"
                        onClick={() => handleInvoiceSelected(invoice)}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-4">
                            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                              <Receipt className="h-6 w-6 text-blue-600" />
                            </div>
                            <div>
                              <h3 className="font-medium text-gray-900">{invoice.invoiceNumber}</h3>
                              <p className="text-sm text-gray-600">
                                {invoice.resource?.user?.firstName} {invoice.resource?.user?.lastName}
                                {invoice.resource?.employmentType === 'CONTRACTOR' && invoice.resource?.vendor &&
                                  ` • Vendor: ${invoice.resource.vendor.name}`
                                }
                              </p>
                              <p className="text-sm text-gray-600">
                                {invoice.project?.name} • Due: {formatDate(invoice.dueDate)}
                              </p>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="font-medium text-gray-900 text-lg">
                              {formatCurrency(invoice.total, invoice.currency)}
                            </p>
                            <p className="text-sm text-gray-600">
                              {invoice.totalHours}h @ {formatCurrency(invoice.hourlyRate, invoice.currency)}/hr
                            </p>
                            <div className="mt-2">
                              <Badge className="bg-blue-100 text-blue-800">
                                {invoice.resource?.employmentType === 'CONTRACTOR' && invoice.resource?.vendor
                                  ? 'Vendor Payment'
                                  : invoice.resource?.employmentType === 'CONTRACTOR'
                                  ? 'Direct Payment'
                                  : 'Payroll'
                                }
                              </Badge>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Payroll Processing Form Modal */}
        <PayrollProcessForm
          isOpen={showPayrollForm}
          onClose={() => setShowPayrollForm(false)}
          invoice={selectedInvoice}
        />
      </div>
    </DashboardLayout>
  );
}
