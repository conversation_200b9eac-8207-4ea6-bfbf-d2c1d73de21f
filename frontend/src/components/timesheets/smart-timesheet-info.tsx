'use client';

import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { timesheetsApi } from '@/lib/api';
import { 
  Clock, 
  TrendingUp, 
  AlertTriangle, 
  CheckCircle, 
  Target,
  Calendar,
  BarChart3
} from 'lucide-react';

interface SmartTimesheetInfoProps {
  timesheetId: string;
}

export function SmartTimesheetInfo({ timesheetId }: SmartTimesheetInfoProps) {
  const { data: allocationData, isLoading } = useQuery({
    queryKey: ['allocation-data', timesheetId],
    queryFn: () => timesheetsApi.getAllocationData(timesheetId),
    enabled: !!timesheetId,
  });

  if (isLoading) {
    return (
      <Card className="redwood-card">
        <CardContent className="p-6">
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            <div className="h-4 bg-gray-200 rounded w-2/3"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!allocationData?.data) {
    return null;
  }

  const data = allocationData.data;
  const { allocation, summary, tasks, dailyBreakdown } = data;

  return (
    <div className="space-y-4">
      {/* Allocation Summary */}
      <Card className="redwood-card">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Target className="h-5 w-5" />
            <span>Project Allocation & Hours Summary</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* Allocated Hours */}
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {allocation.allocatedHoursPerWeek.toFixed(1)}h
              </div>
              <div className="text-sm text-gray-500">Allocated/Week</div>
              <div className="text-xs text-gray-400">
                {allocation.allocationPercent}% of {allocation.standardHoursPerWeek}h
              </div>
            </div>

            {/* Logged Hours */}
            <div className="text-center">
              <div className={`text-2xl font-bold ${
                summary.isOverAllocated ? 'text-red-600' : 
                summary.isOvertime ? 'text-amber-600' : 'text-green-600'
              }`}>
                {summary.totalLoggedHours.toFixed(1)}h
              </div>
              <div className="text-sm text-gray-500">Logged Hours</div>
              <div className="flex justify-center mt-1">
                {summary.isOverAllocated && (
                  <Badge className="bg-red-100 text-red-800 text-xs">
                    Over Allocated
                  </Badge>
                )}
                {summary.isOvertime && !summary.isOverAllocated && (
                  <Badge className="bg-amber-100 text-amber-800 text-xs">
                    Overtime
                  </Badge>
                )}
                {!summary.isOvertime && !summary.isOverAllocated && (
                  <Badge className="bg-green-100 text-green-800 text-xs">
                    Within Limits
                  </Badge>
                )}
              </div>
            </div>

            {/* Utilization */}
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {summary.utilizationPercent.toFixed(0)}%
              </div>
              <div className="text-sm text-gray-500">Utilization</div>
              <Progress 
                value={Math.min(summary.utilizationPercent, 100)} 
                className="mt-2 h-2"
              />
            </div>

            {/* Remaining Allocation */}
            <div className="text-center">
              <div className={`text-2xl font-bold ${
                allocation.allocatedHoursPerWeek - summary.totalLoggedHours > 0 ? 'text-green-600' : 'text-gray-400'
              }`}>
                {Math.max(0, allocation.allocatedHoursPerWeek - summary.totalLoggedHours).toFixed(1)}h
              </div>
              <div className="text-sm text-gray-500">Remaining</div>
              <div className="text-xs text-gray-400">Available to log</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Task Progress */}
      <Card className="redwood-card">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <BarChart3 className="h-5 w-5" />
            <span>Task Progress & Estimates</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {tasks.map((task: any) => (
              <div key={task.id} className="border rounded-lg p-3">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-gray-900">{task.title}</h4>
                  <div className="flex items-center space-x-2">
                    {task.isOverEstimate && (
                      <AlertTriangle className="h-4 w-4 text-amber-500" />
                    )}
                    <Badge variant={task.isOverEstimate ? 'destructive' : 'secondary'}>
                      {task.loggedHours.toFixed(1)}h / {task.estimatedHours.toFixed(1)}h
                    </Badge>
                  </div>
                </div>
                
                <div className="grid grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="text-gray-500">Estimated:</span>
                    <span className="ml-1 font-medium">{task.estimatedHours.toFixed(1)}h</span>
                  </div>
                  <div>
                    <span className="text-gray-500">Logged:</span>
                    <span className="ml-1 font-medium">{task.loggedHours.toFixed(1)}h</span>
                  </div>
                  <div>
                    <span className="text-gray-500">Remaining:</span>
                    <span className={`ml-1 font-medium ${
                      task.remainingHours <= 0 ? 'text-red-600' : 'text-green-600'
                    }`}>
                      {task.remainingHours.toFixed(1)}h
                    </span>
                  </div>
                </div>
                
                <Progress 
                  value={Math.min((task.loggedHours / task.estimatedHours) * 100, 100)} 
                  className="mt-2 h-2"
                />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Daily Breakdown */}
      <Card className="redwood-card">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Calendar className="h-5 w-5" />
            <span>Daily Hour Breakdown</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-7 gap-2">
            {dailyBreakdown.map((day: any, index: number) => (
              <div key={index} className="text-center">
                <div className="text-sm font-medium text-gray-700 mb-1">
                  {day.dayName}
                </div>
                <div className="text-xs text-gray-500 mb-2">
                  {new Date(day.date).getDate()}
                </div>
                
                <div className={`text-lg font-bold mb-1 ${
                  day.isOverAllocated ? 'text-red-600' : 
                  day.isOvertime ? 'text-amber-600' : 'text-green-600'
                }`}>
                  {day.loggedHours.toFixed(1)}h
                </div>
                
                <div className="text-xs space-y-1">
                  <div className="text-gray-500">
                    Target: {day.allocatedHours.toFixed(1)}h
                  </div>
                  {day.isOvertime && (
                    <Badge className="bg-amber-100 text-amber-800 text-xs">
                      Overtime
                    </Badge>
                  )}
                  {day.isOverAllocated && (
                    <Badge className="bg-red-100 text-red-800 text-xs">
                      Over
                    </Badge>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Recommendations */}
      {(summary.isOverAllocated || summary.isOvertime || tasks.some((t: any) => t.isOverEstimate)) && (
        <Card className="redwood-card border-amber-200 bg-amber-50">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 text-amber-800">
              <AlertTriangle className="h-5 w-5" />
              <span>Recommendations</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              {summary.isOverAllocated && (
                <div className="flex items-start space-x-2">
                  <AlertTriangle className="h-4 w-4 text-amber-500 mt-0.5" />
                  <span>
                    <strong>Over-allocated:</strong> Consider redistributing {(summary.totalLoggedHours - allocation.allocatedHoursPerWeek).toFixed(1)} hours to other projects or discuss allocation adjustment with project manager.
                  </span>
                </div>
              )}
              
              {summary.isOvertime && !summary.isOverAllocated && (
                <div className="flex items-start space-x-2">
                  <Clock className="h-4 w-4 text-amber-500 mt-0.5" />
                  <span>
                    <strong>Overtime detected:</strong> {(summary.totalLoggedHours - allocation.standardHoursPerWeek).toFixed(1)} hours over standard work week. Ensure proper overtime approval.
                  </span>
                </div>
              )}
              
              {tasks.filter((t: any) => t.isOverEstimate).map((task: any) => (
                <div key={task.id} className="flex items-start space-x-2">
                  <TrendingUp className="h-4 w-4 text-amber-500 mt-0.5" />
                  <span>
                    <strong>{task.title}:</strong> {(task.loggedHours - task.estimatedHours).toFixed(1)} hours over estimate. Consider updating task estimates or reviewing scope.
                  </span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
