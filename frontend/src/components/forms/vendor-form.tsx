'use client';

import { useState, useEffect } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { vendorsApi } from '@/lib/api';
import { useAuth } from '@/hooks/useAuth';
import { Save, Building, CreditCard, FileText, Award, MapPin, Phone } from 'lucide-react';
import toast from 'react-hot-toast';

interface VendorFormProps {
  isOpen: boolean;
  onClose: () => void;
  vendor?: any;
  mode: 'create' | 'edit';
}

export function VendorForm({ 
  isOpen, 
  onClose, 
  vendor, 
  mode 
}: VendorFormProps) {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  
  const [formData, setFormData] = useState({
    // Basic Information
    name: '',
    contactPerson: '',
    email: '',
    phone: '',
    alternatePhone: '',
    
    // Address Information
    address: '',
    city: '',
    state: '',
    country: 'India',
    pincode: '',
    
    // Company Information
    companyType: '',
    incorporationDate: '',
    registrationNumber: '',
    gstNumber: '',
    panNumber: '',
    tanNumber: '',
    
    // Banking Information
    bankName: '',
    accountNumber: '',
    ifscCode: '',
    accountType: 'CURRENT',
    branch: '',
    
    // Compliance & Certifications
    msmeCertificate: false,
    iso27001: false,
    iso9001: false,
    cmmiLevel: '',
    
    // Contract & Commercial
    contractType: '',
    paymentTerms: 'NET30',
    currency: 'INR',
    
    // Performance
    rating: 0,
    
    status: 'ACTIVE',
  });

  // Reset form when modal opens/closes or mode changes
  useEffect(() => {
    if (isOpen && mode === 'edit' && vendor) {
      const bankDetails = vendor.bankDetails ? JSON.parse(vendor.bankDetails) : {};
      
      setFormData({
        name: vendor.name || '',
        contactPerson: vendor.contactPerson || '',
        email: vendor.email || '',
        phone: vendor.phone || '',
        alternatePhone: vendor.alternatePhone || '',
        address: vendor.address || '',
        city: vendor.city || '',
        state: vendor.state || '',
        country: vendor.country || 'India',
        pincode: vendor.pincode || '',
        companyType: vendor.companyType || '',
        incorporationDate: vendor.incorporationDate ? vendor.incorporationDate.split('T')[0] : '',
        registrationNumber: vendor.registrationNumber || '',
        gstNumber: vendor.gstNumber || '',
        panNumber: vendor.panNumber || '',
        tanNumber: vendor.tanNumber || '',
        bankName: bankDetails.bankName || '',
        accountNumber: bankDetails.accountNumber || '',
        ifscCode: bankDetails.ifscCode || '',
        accountType: bankDetails.accountType || 'CURRENT',
        branch: bankDetails.branch || '',
        msmeCertificate: vendor.msmeCertificate || false,
        iso27001: vendor.iso27001 || false,
        iso9001: vendor.iso9001 || false,
        cmmiLevel: vendor.cmmiLevel || '',
        contractType: vendor.contractType || '',
        paymentTerms: vendor.paymentTerms || 'NET30',
        currency: vendor.currency || 'INR',
        rating: vendor.rating || 0,
        status: vendor.status || 'ACTIVE',
      });
    } else if (mode === 'create') {
      setFormData({
        name: '', contactPerson: '', email: '', phone: '', alternatePhone: '',
        address: '', city: '', state: '', country: 'India', pincode: '',
        companyType: '', incorporationDate: '', registrationNumber: '',
        gstNumber: '', panNumber: '', tanNumber: '',
        bankName: '', accountNumber: '', ifscCode: '', accountType: 'CURRENT', branch: '',
        msmeCertificate: false, iso27001: false, iso9001: false, cmmiLevel: '',
        contractType: '', paymentTerms: 'NET30', currency: 'INR',
        rating: 0, status: 'ACTIVE',
      });
    }
  }, [mode, vendor, isOpen]);

  const createMutation = useMutation({
    mutationFn: (data: any) => vendorsApi.create(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['vendors'] });
      toast.success('Vendor created successfully');
      onClose();
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to create vendor');
    },
  });

  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) => vendorsApi.update(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['vendors'] });
      toast.success('Vendor updated successfully');
      onClose();
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to update vendor');
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const bankDetails = {
      bankName: formData.bankName,
      accountNumber: formData.accountNumber,
      ifscCode: formData.ifscCode,
      accountType: formData.accountType,
      branch: formData.branch,
    };

    const submitData = {
      ...formData,
      bankDetails: JSON.stringify(bankDetails),
      incorporationDate: formData.incorporationDate ? new Date(formData.incorporationDate).toISOString() : undefined,
      rating: parseFloat(formData.rating.toString()) || 0,
    };

    // Remove bank detail fields from main object
    delete submitData.bankName;
    delete submitData.accountNumber;
    delete submitData.ifscCode;
    delete submitData.accountType;
    delete submitData.branch;

    if (mode === 'create') {
      createMutation.mutate(submitData);
    } else if (mode === 'edit') {
      updateMutation.mutate({ id: vendor.id, data: submitData });
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const isLoading = createMutation.isPending || updateMutation.isPending;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[95vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Building className="h-5 w-5" />
            <span>{mode === 'create' ? 'Add New Vendor' : 'Edit Vendor'}</span>
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <Tabs defaultValue="basic" className="w-full">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="basic">Basic Info</TabsTrigger>
              <TabsTrigger value="company">Company</TabsTrigger>
              <TabsTrigger value="banking">Banking</TabsTrigger>
              <TabsTrigger value="compliance">Compliance</TabsTrigger>
              <TabsTrigger value="commercial">Commercial</TabsTrigger>
            </TabsList>

            {/* Basic Information Tab */}
            <TabsContent value="basic" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-gray-900 flex items-center">
                    <Building className="h-5 w-5 mr-2" />
                    Vendor Details
                  </h3>
                  
                  <div className="space-y-2">
                    <Label htmlFor="name">Company Name *</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="contactPerson">Contact Person *</Label>
                    <Input
                      id="contactPerson"
                      value={formData.contactPerson}
                      onChange={(e) => handleInputChange('contactPerson', e.target.value)}
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="email">Email *</Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone *</Label>
                    <Input
                      id="phone"
                      value={formData.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="alternatePhone">Alternate Phone</Label>
                    <Input
                      id="alternatePhone"
                      value={formData.alternatePhone}
                      onChange={(e) => handleInputChange('alternatePhone', e.target.value)}
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-gray-900 flex items-center">
                    <MapPin className="h-5 w-5 mr-2" />
                    Address Information
                  </h3>
                  
                  <div className="space-y-2">
                    <Label htmlFor="address">Address *</Label>
                    <Textarea
                      id="address"
                      value={formData.address}
                      onChange={(e) => handleInputChange('address', e.target.value)}
                      rows={3}
                      required
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="city">City</Label>
                      <Input
                        id="city"
                        value={formData.city}
                        onChange={(e) => handleInputChange('city', e.target.value)}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="state">State</Label>
                      <Input
                        id="state"
                        value={formData.state}
                        onChange={(e) => handleInputChange('state', e.target.value)}
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="country">Country</Label>
                      <select
                        id="country"
                        value={formData.country}
                        onChange={(e) => handleInputChange('country', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                      >
                        <option value="India">India</option>
                        <option value="USA">USA</option>
                        <option value="UK">UK</option>
                        <option value="Canada">Canada</option>
                        <option value="Australia">Australia</option>
                      </select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="pincode">Pincode</Label>
                      <Input
                        id="pincode"
                        value={formData.pincode}
                        onChange={(e) => handleInputChange('pincode', e.target.value)}
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="status">Status</Label>
                    <select
                      id="status"
                      value={formData.status}
                      onChange={(e) => handleInputChange('status', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                    >
                      <option value="ACTIVE">Active</option>
                      <option value="INACTIVE">Inactive</option>
                      <option value="BLACKLISTED">Blacklisted</option>
                    </select>
                  </div>
                </div>
              </div>
            </TabsContent>

            {/* Company Information Tab */}
            <TabsContent value="company" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-gray-900 flex items-center">
                    <FileText className="h-5 w-5 mr-2" />
                    Company Registration
                  </h3>

                  <div className="space-y-2">
                    <Label htmlFor="companyType">Company Type</Label>
                    <select
                      id="companyType"
                      value={formData.companyType}
                      onChange={(e) => handleInputChange('companyType', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                    >
                      <option value="">Select Company Type</option>
                      <option value="PRIVATE_LIMITED">Private Limited</option>
                      <option value="PUBLIC_LIMITED">Public Limited</option>
                      <option value="PARTNERSHIP">Partnership</option>
                      <option value="PROPRIETORSHIP">Proprietorship</option>
                      <option value="LLP">Limited Liability Partnership</option>
                      <option value="OPC">One Person Company</option>
                    </select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="incorporationDate">Incorporation Date</Label>
                    <Input
                      id="incorporationDate"
                      type="date"
                      value={formData.incorporationDate}
                      onChange={(e) => handleInputChange('incorporationDate', e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="registrationNumber">Registration Number</Label>
                    <Input
                      id="registrationNumber"
                      value={formData.registrationNumber}
                      onChange={(e) => handleInputChange('registrationNumber', e.target.value)}
                      placeholder="CIN/Registration Number"
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-gray-900">Tax Information</h3>

                  <div className="space-y-2">
                    <Label htmlFor="panNumber">PAN Number *</Label>
                    <Input
                      id="panNumber"
                      value={formData.panNumber}
                      onChange={(e) => handleInputChange('panNumber', e.target.value.toUpperCase())}
                      placeholder="**********"
                      maxLength={10}
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="gstNumber">GST Number</Label>
                    <Input
                      id="gstNumber"
                      value={formData.gstNumber}
                      onChange={(e) => handleInputChange('gstNumber', e.target.value.toUpperCase())}
                      placeholder="22AAAAA0000A1Z5"
                      maxLength={15}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="tanNumber">TAN Number</Label>
                    <Input
                      id="tanNumber"
                      value={formData.tanNumber}
                      onChange={(e) => handleInputChange('tanNumber', e.target.value.toUpperCase())}
                      placeholder="ABCD12345E"
                      maxLength={10}
                    />
                  </div>
                </div>
              </div>
            </TabsContent>

            {/* Banking Information Tab */}
            <TabsContent value="banking" className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900 flex items-center">
                  <CreditCard className="h-5 w-5 mr-2" />
                  Banking Details
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="bankName">Bank Name *</Label>
                      <Input
                        id="bankName"
                        value={formData.bankName}
                        onChange={(e) => handleInputChange('bankName', e.target.value)}
                        required
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="accountNumber">Account Number *</Label>
                      <Input
                        id="accountNumber"
                        value={formData.accountNumber}
                        onChange={(e) => handleInputChange('accountNumber', e.target.value)}
                        required
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="ifscCode">IFSC Code *</Label>
                      <Input
                        id="ifscCode"
                        value={formData.ifscCode}
                        onChange={(e) => handleInputChange('ifscCode', e.target.value.toUpperCase())}
                        placeholder="SBIN0001234"
                        maxLength={11}
                        required
                      />
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="accountType">Account Type</Label>
                      <select
                        id="accountType"
                        value={formData.accountType}
                        onChange={(e) => handleInputChange('accountType', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                      >
                        <option value="CURRENT">Current Account</option>
                        <option value="SAVINGS">Savings Account</option>
                        <option value="OVERDRAFT">Overdraft Account</option>
                      </select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="branch">Branch</Label>
                      <Input
                        id="branch"
                        value={formData.branch}
                        onChange={(e) => handleInputChange('branch', e.target.value)}
                        placeholder="Branch Name/Location"
                      />
                    </div>
                  </div>
                </div>

                <Card className="bg-blue-50 border-blue-200">
                  <CardContent className="p-4">
                    <p className="text-sm text-blue-800">
                      <strong>Note:</strong> Banking information is required for payment processing.
                      Please ensure all details are accurate to avoid payment delays.
                    </p>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* Compliance & Certifications Tab */}
            <TabsContent value="compliance" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-gray-900 flex items-center">
                    <Award className="h-5 w-5 mr-2" />
                    Certifications
                  </h3>

                  <div className="space-y-4">
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="msmeCertificate"
                        checked={formData.msmeCertificate}
                        onChange={(e) => handleInputChange('msmeCertificate', e.target.checked)}
                        className="rounded border-gray-300 text-red-600 focus:ring-red-500"
                      />
                      <Label htmlFor="msmeCertificate">MSME Certificate</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="iso27001"
                        checked={formData.iso27001}
                        onChange={(e) => handleInputChange('iso27001', e.target.checked)}
                        className="rounded border-gray-300 text-red-600 focus:ring-red-500"
                      />
                      <Label htmlFor="iso27001">ISO 27001 (Information Security)</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="iso9001"
                        checked={formData.iso9001}
                        onChange={(e) => handleInputChange('iso9001', e.target.checked)}
                        className="rounded border-gray-300 text-red-600 focus:ring-red-500"
                      />
                      <Label htmlFor="iso9001">ISO 9001 (Quality Management)</Label>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="cmmiLevel">CMMI Level</Label>
                      <select
                        id="cmmiLevel"
                        value={formData.cmmiLevel}
                        onChange={(e) => handleInputChange('cmmiLevel', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                      >
                        <option value="">Select CMMI Level</option>
                        <option value="LEVEL_1">Level 1 - Initial</option>
                        <option value="LEVEL_2">Level 2 - Managed</option>
                        <option value="LEVEL_3">Level 3 - Defined</option>
                        <option value="LEVEL_4">Level 4 - Quantitatively Managed</option>
                        <option value="LEVEL_5">Level 5 - Optimizing</option>
                      </select>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-gray-900">Performance Rating</h3>

                  <div className="space-y-2">
                    <Label htmlFor="rating">Vendor Rating (1-5)</Label>
                    <Input
                      id="rating"
                      type="number"
                      min="0"
                      max="5"
                      step="0.1"
                      value={formData.rating}
                      onChange={(e) => handleInputChange('rating', parseFloat(e.target.value) || 0)}
                    />
                  </div>

                  <Card className="bg-green-50 border-green-200">
                    <CardContent className="p-4">
                      <h4 className="font-medium text-green-800 mb-2">Certification Benefits:</h4>
                      <ul className="text-sm text-green-700 space-y-1">
                        <li>• MSME: Tax benefits and priority in tenders</li>
                        <li>• ISO 27001: Information security compliance</li>
                        <li>• ISO 9001: Quality management assurance</li>
                        <li>• CMMI: Process maturity certification</li>
                      </ul>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </TabsContent>

            {/* Commercial Information Tab */}
            <TabsContent value="commercial" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-gray-900">Contract Information</h3>

                  <div className="space-y-2">
                    <Label htmlFor="contractType">Contract Type</Label>
                    <select
                      id="contractType"
                      value={formData.contractType}
                      onChange={(e) => handleInputChange('contractType', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                    >
                      <option value="">Select Contract Type</option>
                      <option value="MSA">Master Service Agreement</option>
                      <option value="SOW">Statement of Work</option>
                      <option value="FIXED_PRICE">Fixed Price Contract</option>
                      <option value="TIME_MATERIAL">Time & Material</option>
                      <option value="RETAINER">Retainer Agreement</option>
                    </select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="paymentTerms">Payment Terms</Label>
                    <select
                      id="paymentTerms"
                      value={formData.paymentTerms}
                      onChange={(e) => handleInputChange('paymentTerms', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                    >
                      <option value="NET15">NET 15 Days</option>
                      <option value="NET30">NET 30 Days</option>
                      <option value="NET45">NET 45 Days</option>
                      <option value="NET60">NET 60 Days</option>
                      <option value="ADVANCE">Advance Payment</option>
                      <option value="COD">Cash on Delivery</option>
                    </select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="currency">Preferred Currency</Label>
                    <select
                      id="currency"
                      value={formData.currency}
                      onChange={(e) => handleInputChange('currency', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                    >
                      <option value="INR">INR (Indian Rupee)</option>
                      <option value="USD">USD (US Dollar)</option>
                      <option value="EUR">EUR (Euro)</option>
                      <option value="GBP">GBP (British Pound)</option>
                    </select>
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-gray-900">Commercial Summary</h3>

                  <Card className="bg-gray-50">
                    <CardContent className="p-4">
                      <div className="space-y-3">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Company:</span>
                          <span className="font-medium">{formData.name || 'Not specified'}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Contact Person:</span>
                          <span className="font-medium">{formData.contactPerson || 'Not specified'}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Email:</span>
                          <span className="font-medium">{formData.email || 'Not specified'}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Phone:</span>
                          <span className="font-medium">{formData.phone || 'Not specified'}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">PAN Number:</span>
                          <span className="font-medium">{formData.panNumber || 'Not specified'}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">GST Number:</span>
                          <span className="font-medium">{formData.gstNumber || 'Not specified'}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Payment Terms:</span>
                          <span className="font-medium">{formData.paymentTerms}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Currency:</span>
                          <span className="font-medium">{formData.currency}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Status:</span>
                          <span className={`font-medium ${
                            formData.status === 'ACTIVE' ? 'text-green-600' :
                            formData.status === 'INACTIVE' ? 'text-yellow-600' : 'text-red-600'
                          }`}>
                            {formData.status}
                          </span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="bg-orange-50 border-orange-200">
                    <CardContent className="p-4">
                      <p className="text-sm text-orange-800">
                        <strong>Important:</strong> Please review all information carefully before submitting.
                        Incorrect banking or tax details may cause payment processing delays.
                      </p>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </TabsContent>
          </Tabs>

          {/* Form Actions */}
          <div className="flex justify-end space-x-3 pt-4 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="bg-red-600 hover:bg-red-700"
              disabled={isLoading}
            >
              <Save className="h-4 w-4 mr-2" />
              {isLoading
                ? (mode === 'create' ? 'Creating...' : 'Updating...')
                : (mode === 'create' ? 'Create Vendor' : 'Update Vendor')
              }
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
