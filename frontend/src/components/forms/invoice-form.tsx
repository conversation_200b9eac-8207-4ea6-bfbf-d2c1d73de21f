'use client';

import { useState, useEffect } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { invoicesApi, projectsApi, resourcesApi, timesheetsApi } from '@/lib/api';
import { useAuth } from '@/hooks/useAuth';
import { formatCurrency, formatDate } from '@/lib/utils';
import { Save, Calculator, AlertTriangle, Clock, DollarSign } from 'lucide-react';
import toast from 'react-hot-toast';

interface InvoiceFormProps {
  isOpen: boolean;
  onClose: () => void;
  invoice?: any;
  mode: 'create' | 'edit';
}

export function InvoiceForm({ 
  isOpen, 
  onClose, 
  invoice, 
  mode 
}: InvoiceFormProps) {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  
  const [formData, setFormData] = useState({
    projectId: '',
    resourceId: '',
    timesheetId: '',
    invoiceType: 'MONTHLY', // MONTHLY, WEEKLY, CUSTOM
    billingPeriodStart: '',
    billingPeriodEnd: '',
    hourlyRate: 0,
    totalHours: 0,
    subtotal: 0,
    taxRate: 0,
    tax: 0,
    penalties: 0,
    penaltyReason: '',
    arrears: 0,
    arrearsReason: '',
    total: 0,
    dueDate: '',
    notes: '',
    currency: 'USD',
  });

  const [calculationDetails, setCalculationDetails] = useState({
    regularHours: 0,
    overtimeHours: 0,
    contractedRate: 0,
    actualRate: 0,
    slaBreaches: [],
    penaltyCalculations: [],
  });

  // Fetch projects for dropdown
  const { data: projectsData } = useQuery({
    queryKey: ['projects'],
    queryFn: () => projectsApi.getAll({ limit: 100 }),
  });

  // Fetch resources for dropdown
  const { data: resourcesData } = useQuery({
    queryKey: ['resources'],
    queryFn: () => resourcesApi.getAll({ limit: 100 }),
  });

  // Fetch timesheets when project and resource are selected
  const { data: timesheetsData } = useQuery({
    queryKey: ['timesheets', formData.projectId, formData.resourceId],
    queryFn: () => timesheetsApi.getAll({ 
      projectId: formData.projectId,
      resourceId: formData.resourceId,
      status: 'APPROVED',
      limit: 100 
    }),
    enabled: !!(formData.projectId && formData.resourceId),
  });

  const projects = projectsData?.data || [];
  const resources = resourcesData?.data || [];
  const timesheets = timesheetsData?.data || [];

  // Reset form when modal opens/closes or mode changes
  useEffect(() => {
    if (isOpen && mode === 'edit' && invoice) {
      setFormData({
        projectId: invoice.projectId || '',
        resourceId: invoice.resourceId || '',
        timesheetId: invoice.timesheetId || '',
        invoiceType: invoice.invoiceType || 'MONTHLY',
        billingPeriodStart: invoice.billingPeriodStart ? invoice.billingPeriodStart.split('T')[0] : '',
        billingPeriodEnd: invoice.billingPeriodEnd ? invoice.billingPeriodEnd.split('T')[0] : '',
        hourlyRate: invoice.hourlyRate || 0,
        totalHours: invoice.totalHours || 0,
        subtotal: invoice.subtotal || 0,
        taxRate: invoice.taxRate || 0,
        tax: invoice.tax || 0,
        penalties: invoice.penalties || 0,
        penaltyReason: invoice.penaltyReason || '',
        arrears: invoice.arrears || 0,
        arrearsReason: invoice.arrearsReason || '',
        total: invoice.total || 0,
        dueDate: invoice.dueDate ? invoice.dueDate.split('T')[0] : '',
        notes: invoice.notes || '',
        currency: invoice.currency || 'USD',
      });
    } else if (mode === 'create') {
      const nextMonth = new Date();
      nextMonth.setMonth(nextMonth.getMonth() + 1);
      
      setFormData({
        projectId: '', resourceId: '', timesheetId: '', invoiceType: 'MONTHLY',
        billingPeriodStart: '', billingPeriodEnd: '', hourlyRate: 0, totalHours: 0,
        subtotal: 0, taxRate: 10, tax: 0, penalties: 0, penaltyReason: '',
        arrears: 0, arrearsReason: '', total: 0,
        dueDate: nextMonth.toISOString().split('T')[0],
        notes: '', currency: 'USD',
      });
    }
  }, [mode, invoice, isOpen]);

  // Auto-calculate monthly billing period when invoice type changes
  useEffect(() => {
    if (formData.invoiceType === 'MONTHLY' && !formData.billingPeriodStart) {
      const now = new Date();
      const firstDay = new Date(now.getFullYear(), now.getMonth() - 1, 1);
      const lastDay = new Date(now.getFullYear(), now.getMonth(), 0);
      
      setFormData(prev => ({
        ...prev,
        billingPeriodStart: firstDay.toISOString().split('T')[0],
        billingPeriodEnd: lastDay.toISOString().split('T')[0],
      }));
    }
  }, [formData.invoiceType]);

  // Calculate invoice amounts when relevant fields change
  useEffect(() => {
    const subtotal = formData.totalHours * formData.hourlyRate;
    const tax = subtotal * (formData.taxRate / 100);
    const total = subtotal + tax + formData.penalties + formData.arrears;

    setFormData(prev => ({
      ...prev,
      subtotal,
      tax,
      total,
    }));
  }, [formData.totalHours, formData.hourlyRate, formData.taxRate, formData.penalties, formData.arrears]);

  // Auto-populate data when timesheet is selected
  useEffect(() => {
    if (formData.timesheetId) {
      const selectedTimesheet = timesheets.find(ts => ts.id === formData.timesheetId);
      if (selectedTimesheet) {
        setFormData(prev => ({
          ...prev,
          totalHours: selectedTimesheet.totalHours || 0,
          hourlyRate: selectedTimesheet.resource?.hourlyRate || 0,
          billingPeriodStart: selectedTimesheet.weekStarting?.split('T')[0] || '',
          billingPeriodEnd: selectedTimesheet.weekEnding?.split('T')[0] || '',
        }));
      }
    }
  }, [formData.timesheetId, timesheets]);

  const createMutation = useMutation({
    mutationFn: (data: any) => invoicesApi.create(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['invoices'] });
      toast.success('Invoice created successfully');
      onClose();
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to create invoice');
    },
  });

  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) => invoicesApi.update(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['invoices'] });
      toast.success('Invoice updated successfully');
      onClose();
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to update invoice');
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const submitData = {
      ...formData,
      billingPeriodStart: formData.billingPeriodStart ? new Date(formData.billingPeriodStart).toISOString() : undefined,
      billingPeriodEnd: formData.billingPeriodEnd ? new Date(formData.billingPeriodEnd).toISOString() : undefined,
      dueDate: formData.dueDate ? new Date(formData.dueDate).toISOString() : undefined,
    };

    if (mode === 'create') {
      createMutation.mutate(submitData);
    } else if (mode === 'edit') {
      updateMutation.mutate({ id: invoice.id, data: submitData });
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const calculateMonthlyInvoice = async () => {
    if (!formData.projectId || !formData.resourceId || !formData.billingPeriodStart || !formData.billingPeriodEnd) {
      toast.error('Please select project, resource, and billing period');
      return;
    }

    try {
      // This would call a backend endpoint to calculate monthly invoice
      // For now, we'll simulate the calculation
      const approvedTimesheets = timesheets.filter(ts => 
        ts.status === 'APPROVED' &&
        new Date(ts.weekStarting) >= new Date(formData.billingPeriodStart) &&
        new Date(ts.weekEnding) <= new Date(formData.billingPeriodEnd)
      );

      const totalHours = approvedTimesheets.reduce((sum, ts) => sum + (ts.totalHours || 0), 0);
      const selectedResource = resources.find(r => r.id === formData.resourceId);
      const hourlyRate = selectedResource?.hourlyRate || 0;

      setFormData(prev => ({
        ...prev,
        totalHours,
        hourlyRate,
      }));

      toast.success(`Calculated ${totalHours} hours for the billing period`);
    } catch (error) {
      toast.error('Failed to calculate monthly invoice');
    }
  };

  const isLoading = createMutation.isPending || updateMutation.isPending;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[95vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <DollarSign className="h-5 w-5" />
            <span>{mode === 'create' ? 'Create New Invoice' : 'Edit Invoice'}</span>
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <Tabs defaultValue="basic" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="basic">Basic Info</TabsTrigger>
              <TabsTrigger value="calculation">Calculation</TabsTrigger>
              <TabsTrigger value="penalties">Penalties & Arrears</TabsTrigger>
              <TabsTrigger value="summary">Summary</TabsTrigger>
            </TabsList>

            {/* Basic Information Tab */}
            <TabsContent value="basic" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="invoiceType">Invoice Type</Label>
                    <select
                      id="invoiceType"
                      value={formData.invoiceType}
                      onChange={(e) => handleInputChange('invoiceType', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                    >
                      <option value="MONTHLY">Monthly Invoice</option>
                      <option value="WEEKLY">Weekly Invoice</option>
                      <option value="CUSTOM">Custom Period</option>
                    </select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="projectId">Project *</Label>
                    <select
                      id="projectId"
                      value={formData.projectId}
                      onChange={(e) => handleInputChange('projectId', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                      required
                    >
                      <option value="">Select Project</option>
                      {projects.map((project: any) => (
                        <option key={project.id} value={project.id}>
                          {project.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="resourceId">Resource *</Label>
                    <select
                      id="resourceId"
                      value={formData.resourceId}
                      onChange={(e) => handleInputChange('resourceId', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                      required
                    >
                      <option value="">Select Resource</option>
                      {resources.map((resource: any) => (
                        <option key={resource.id} value={resource.id}>
                          {resource.user?.firstName} {resource.user?.lastName} - {resource.designation}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="timesheetId">Timesheet (Optional)</Label>
                    <select
                      id="timesheetId"
                      value={formData.timesheetId}
                      onChange={(e) => handleInputChange('timesheetId', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                    >
                      <option value="">Select Timesheet</option>
                      {timesheets.map((timesheet: any) => (
                        <option key={timesheet.id} value={timesheet.id}>
                          {formatDate(timesheet.weekStarting)} - {formatDate(timesheet.weekEnding)} ({timesheet.totalHours}h)
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="billingPeriodStart">Billing Period Start *</Label>
                    <Input
                      id="billingPeriodStart"
                      type="date"
                      value={formData.billingPeriodStart}
                      onChange={(e) => handleInputChange('billingPeriodStart', e.target.value)}
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="billingPeriodEnd">Billing Period End *</Label>
                    <Input
                      id="billingPeriodEnd"
                      type="date"
                      value={formData.billingPeriodEnd}
                      onChange={(e) => handleInputChange('billingPeriodEnd', e.target.value)}
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="dueDate">Due Date *</Label>
                    <Input
                      id="dueDate"
                      type="date"
                      value={formData.dueDate}
                      onChange={(e) => handleInputChange('dueDate', e.target.value)}
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="currency">Currency</Label>
                    <select
                      id="currency"
                      value={formData.currency}
                      onChange={(e) => handleInputChange('currency', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                    >
                      <option value="USD">USD</option>
                      <option value="EUR">EUR</option>
                      <option value="GBP">GBP</option>
                      <option value="INR">INR</option>
                    </select>
                  </div>
                </div>
              </div>

              {formData.invoiceType === 'MONTHLY' && (
                <Card className="bg-blue-50 border-blue-200">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-medium text-blue-900">Monthly Invoice Calculation</h3>
                        <p className="text-sm text-blue-700">
                          Calculate invoice based on approved timesheets for the selected period
                        </p>
                      </div>
                      <Button
                        type="button"
                        onClick={calculateMonthlyInvoice}
                        className="bg-blue-600 hover:bg-blue-700"
                        disabled={!formData.projectId || !formData.resourceId}
                      >
                        <Calculator className="h-4 w-4 mr-2" />
                        Calculate
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            {/* Calculation Tab */}
            <TabsContent value="calculation" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-gray-900">Time & Rate Details</h3>

                  <div className="space-y-2">
                    <Label htmlFor="totalHours">Total Hours *</Label>
                    <Input
                      id="totalHours"
                      type="number"
                      step="0.25"
                      value={formData.totalHours}
                      onChange={(e) => handleInputChange('totalHours', parseFloat(e.target.value) || 0)}
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="hourlyRate">Hourly Rate ({formData.currency}) *</Label>
                    <Input
                      id="hourlyRate"
                      type="number"
                      step="0.01"
                      value={formData.hourlyRate}
                      onChange={(e) => handleInputChange('hourlyRate', parseFloat(e.target.value) || 0)}
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="taxRate">Tax Rate (%)</Label>
                    <Input
                      id="taxRate"
                      type="number"
                      step="0.01"
                      value={formData.taxRate}
                      onChange={(e) => handleInputChange('taxRate', parseFloat(e.target.value) || 0)}
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-gray-900">Amount Breakdown</h3>

                  <div className="space-y-3 p-4 bg-gray-50 rounded-lg">
                    <div className="flex justify-between">
                      <span>Subtotal:</span>
                      <span className="font-medium">{formatCurrency(formData.subtotal, formData.currency)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Tax ({formData.taxRate}%):</span>
                      <span className="font-medium">{formatCurrency(formData.tax, formData.currency)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Penalties:</span>
                      <span className="font-medium text-red-600">{formatCurrency(formData.penalties, formData.currency)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Arrears:</span>
                      <span className="font-medium text-orange-600">{formatCurrency(formData.arrears, formData.currency)}</span>
                    </div>
                    <div className="border-t pt-2">
                      <div className="flex justify-between text-lg font-bold">
                        <span>Total:</span>
                        <span>{formatCurrency(formData.total, formData.currency)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            {/* Penalties & Arrears Tab */}
            <TabsContent value="penalties" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card className="border-red-200">
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2 text-red-700">
                      <AlertTriangle className="h-5 w-5" />
                      <span>Penalties</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="penalties">Penalty Amount ({formData.currency})</Label>
                      <Input
                        id="penalties"
                        type="number"
                        step="0.01"
                        value={formData.penalties}
                        onChange={(e) => handleInputChange('penalties', parseFloat(e.target.value) || 0)}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="penaltyReason">Penalty Reason</Label>
                      <Textarea
                        id="penaltyReason"
                        value={formData.penaltyReason}
                        onChange={(e) => handleInputChange('penaltyReason', e.target.value)}
                        placeholder="e.g., SLA breach, late delivery, quality issues"
                        rows={3}
                      />
                    </div>

                    <div className="p-3 bg-red-50 rounded-lg">
                      <h4 className="font-medium text-red-800 mb-2">Common Penalty Types:</h4>
                      <ul className="text-sm text-red-700 space-y-1">
                        <li>• SLA Response Time Breach</li>
                        <li>• Missed Delivery Deadlines</li>
                        <li>• Quality Standard Violations</li>
                        <li>• Attendance/Availability Issues</li>
                      </ul>
                    </div>
                  </CardContent>
                </Card>

                <Card className="border-orange-200">
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2 text-orange-700">
                      <Clock className="h-5 w-5" />
                      <span>Arrears</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="arrears">Arrears Amount ({formData.currency})</Label>
                      <Input
                        id="arrears"
                        type="number"
                        step="0.01"
                        value={formData.arrears}
                        onChange={(e) => handleInputChange('arrears', parseFloat(e.target.value) || 0)}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="arrearsReason">Arrears Reason</Label>
                      <Textarea
                        id="arrearsReason"
                        value={formData.arrearsReason}
                        onChange={(e) => handleInputChange('arrearsReason', e.target.value)}
                        placeholder="e.g., Previous month adjustments, bonus payments"
                        rows={3}
                      />
                    </div>

                    <div className="p-3 bg-orange-50 rounded-lg">
                      <h4 className="font-medium text-orange-800 mb-2">Common Arrears Types:</h4>
                      <ul className="text-sm text-orange-700 space-y-1">
                        <li>• Previous Period Adjustments</li>
                        <li>• Overtime Payments</li>
                        <li>• Bonus/Incentive Payments</li>
                        <li>• Expense Reimbursements</li>
                      </ul>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* Summary Tab */}
            <TabsContent value="summary" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Invoice Summary</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Invoice Type:</span>
                        <span className="font-medium">{formData.invoiceType}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Billing Period:</span>
                        <span className="font-medium">
                          {formData.billingPeriodStart && formData.billingPeriodEnd
                            ? `${formatDate(formData.billingPeriodStart)} - ${formatDate(formData.billingPeriodEnd)}`
                            : 'Not set'
                          }
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Total Hours:</span>
                        <span className="font-medium">{formData.totalHours}h</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Hourly Rate:</span>
                        <span className="font-medium">{formatCurrency(formData.hourlyRate, formData.currency)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Due Date:</span>
                        <span className="font-medium">
                          {formData.dueDate ? formatDate(formData.dueDate) : 'Not set'}
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Amount Breakdown</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span>Base Amount:</span>
                        <span className="font-medium">{formatCurrency(formData.subtotal, formData.currency)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Tax ({formData.taxRate}%):</span>
                        <span className="font-medium">{formatCurrency(formData.tax, formData.currency)}</span>
                      </div>
                      {formData.penalties > 0 && (
                        <div className="flex justify-between">
                          <span className="text-red-600">Penalties:</span>
                          <span className="font-medium text-red-600">-{formatCurrency(formData.penalties, formData.currency)}</span>
                        </div>
                      )}
                      {formData.arrears > 0 && (
                        <div className="flex justify-between">
                          <span className="text-orange-600">Arrears:</span>
                          <span className="font-medium text-orange-600">+{formatCurrency(formData.arrears, formData.currency)}</span>
                        </div>
                      )}
                      <div className="border-t pt-3">
                        <div className="flex justify-between text-xl font-bold">
                          <span>Total Amount:</span>
                          <span className="text-green-600">{formatCurrency(formData.total, formData.currency)}</span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="space-y-2">
                <Label htmlFor="notes">Additional Notes</Label>
                <Textarea
                  id="notes"
                  value={formData.notes}
                  onChange={(e) => handleInputChange('notes', e.target.value)}
                  placeholder="Any additional notes or comments for this invoice..."
                  rows={4}
                />
              </div>
            </TabsContent>
          </Tabs>

          {/* Form Actions */}
          <div className="flex justify-end space-x-3 pt-4 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="bg-red-600 hover:bg-red-700"
              disabled={isLoading}
            >
              <Save className="h-4 w-4 mr-2" />
              {isLoading
                ? (mode === 'create' ? 'Creating...' : 'Updating...')
                : (mode === 'create' ? 'Create Invoice' : 'Update Invoice')
              }
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
