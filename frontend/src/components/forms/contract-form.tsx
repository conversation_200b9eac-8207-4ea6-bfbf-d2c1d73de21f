'use client';

import { useState, useEffect } from 'react';
import { useMutation, useQueryClient, useQuery } from '@tanstack/react-query';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { contractsApi, usersApi, skillsApi, projectsApi } from '@/lib/api';
import { useAuth } from '@/hooks/useAuth';
import { formatCurrency } from '@/lib/utils';
import {
  FileText,
  User,
  Calendar,
  DollarSign,
  Settings,
  Shield,
  Users,
  Target,
  Zap,
  Plus,
  Trash2,
  AlertTriangle,
  CheckCircle,
  Clock,
  Building
} from 'lucide-react';
import toast from 'react-hot-toast';

interface ContractFormProps {
  isOpen: boolean;
  onClose: () => void;
  contract?: any;
  mode: 'create' | 'edit' | 'view';
}

export function ContractForm({ 
  isOpen, 
  onClose, 
  contract, 
  mode 
}: ContractFormProps) {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  
  const [formData, setFormData] = useState({
    // Basic Information
    title: '',
    description: '',
    clientId: '',
    type: 'FIXED_PRICE',
    contractCategory: 'DEVELOPMENT',
    priority: 'MEDIUM',
    riskLevel: 'LOW',
    complexityLevel: 'MEDIUM',

    // Timeline & Financial
    startDate: '',
    endDate: '',
    value: 0,
    currency: 'USD',
    paymentTerms: 'NET30',
    estimatedEffort: 0,

    // Legal & Compliance
    terms: '',
    securityLevel: 'STANDARD',
    confidentialityLevel: 'STANDARD',
    governingLaw: '',
    disputeResolution: '',
    ipRights: '',

    // Technical Requirements
    technologyStack: [],
    deliverables: [],
    milestones: [],
    qualityStandards: [],
    testingRequirements: [],

    // Resource Planning
    resourceRequirements: [],
    skillRequirements: [],
    projectTemplate: '',
    autoGenerateProject: false,

    // Performance & SLA
    slaRequirements: [],
    performanceMetrics: [],
    successCriteria: [],
    kpis: [],

    // Support & Maintenance
    supportLevel: 'STANDARD',
    maintenanceTerms: [],
    trainingRequirements: [],
    documentationRequirements: [],

    // Stakeholders & Communication
    clientContact: {},
    stakeholders: [],
    communicationPlan: {},
    escalationMatrix: [],

    // Risk & Compliance
    riskAssessment: [],
    complianceRequirements: [],
    contingencyPlan: '',

    // Contract Management
    penaltyClause: '',
    bonusClause: '',
    terminationClause: '',
    renewalTerms: '',
    changeManagement: {},

    // Dependencies & Constraints
    dependencies: [],
    assumptions: [],
    constraints: [],
    exclusions: [],
  });

  const [activeTab, setActiveTab] = useState('basic');
  const [resourcePlan, setResourcePlan] = useState([]);
  const [selectedSkills, setSelectedSkills] = useState([]);

  // Fetch clients for dropdown
  const { data: clientsData } = useQuery({
    queryKey: ['users', { role: 'CLIENT' }],
    queryFn: () => usersApi.getAll({ role: 'CLIENT', limit: 100 }),
    enabled: isOpen && mode !== 'view',
  });

  // Fetch skills for resource planning
  const { data: skillsData } = useQuery({
    queryKey: ['skills'],
    queryFn: () => skillsApi.getAll({ limit: 100 }),
    enabled: isOpen,
  });

  // Fetch project managers
  const { data: managersData } = useQuery({
    queryKey: ['users', { role: 'PROJECT_MANAGER' }],
    queryFn: () => usersApi.getAll({ role: 'PROJECT_MANAGER', limit: 100 }),
    enabled: isOpen && mode !== 'view',
  });

  const clients = clientsData?.data || [];
  const skills = skillsData?.data || [];
  const managers = managersData?.data || [];

  // Reset form when modal opens/closes or contract changes
  useEffect(() => {
    if (isOpen && contract && (mode === 'edit' || mode === 'view')) {
      setFormData({
        // Basic Information
        title: contract.title || '',
        description: contract.description || '',
        clientId: contract.clientId || '',
        type: contract.type || 'FIXED_PRICE',
        contractCategory: contract.contractCategory || 'DEVELOPMENT',
        priority: contract.priority || 'MEDIUM',
        riskLevel: contract.riskLevel || 'LOW',
        complexityLevel: contract.complexityLevel || 'MEDIUM',

        // Timeline & Financial
        startDate: contract.startDate ? new Date(contract.startDate).toISOString().split('T')[0] : '',
        endDate: contract.endDate ? new Date(contract.endDate).toISOString().split('T')[0] : '',
        value: contract.value || 0,
        currency: contract.currency || 'USD',
        paymentTerms: contract.paymentTerms || 'NET30',
        estimatedEffort: contract.estimatedEffort || 0,

        // Legal & Compliance
        terms: contract.terms || '',
        securityLevel: contract.securityLevel || 'STANDARD',
        confidentialityLevel: contract.confidentialityLevel || 'STANDARD',
        governingLaw: contract.governingLaw || '',
        disputeResolution: contract.disputeResolution || '',
        ipRights: contract.ipRights || '',

        // Technical Requirements
        technologyStack: contract.technologyStack ? JSON.parse(contract.technologyStack) : [],
        deliverables: contract.deliverables ? JSON.parse(contract.deliverables) : [],
        milestones: contract.milestones ? JSON.parse(contract.milestones) : [],
        qualityStandards: contract.qualityStandards ? JSON.parse(contract.qualityStandards) : [],
        testingRequirements: contract.testingRequirements ? JSON.parse(contract.testingRequirements) : [],

        // Resource Planning
        resourceRequirements: contract.resourceRequirements ? JSON.parse(contract.resourceRequirements) : [],
        skillRequirements: contract.skillRequirements ? JSON.parse(contract.skillRequirements) : [],
        projectTemplate: contract.projectTemplate || '',
        autoGenerateProject: contract.autoGenerateProject || false,

        // Performance & SLA
        slaRequirements: contract.slaRequirements ? JSON.parse(contract.slaRequirements) : [],
        performanceMetrics: contract.performanceMetrics ? JSON.parse(contract.performanceMetrics) : [],
        successCriteria: contract.successCriteria ? JSON.parse(contract.successCriteria) : [],
        kpis: contract.kpis ? JSON.parse(contract.kpis) : [],

        // Support & Maintenance
        supportLevel: contract.supportLevel || 'STANDARD',
        maintenanceTerms: contract.maintenanceTerms ? JSON.parse(contract.maintenanceTerms) : [],
        trainingRequirements: contract.trainingRequirements ? JSON.parse(contract.trainingRequirements) : [],
        documentationRequirements: contract.documentationRequirements ? JSON.parse(contract.documentationRequirements) : [],

        // Stakeholders & Communication
        clientContact: contract.clientContact ? JSON.parse(contract.clientContact) : {},
        stakeholders: contract.stakeholders ? JSON.parse(contract.stakeholders) : [],
        communicationPlan: contract.communicationPlan ? JSON.parse(contract.communicationPlan) : {},
        escalationMatrix: contract.escalationMatrix ? JSON.parse(contract.escalationMatrix) : [],

        // Risk & Compliance
        riskAssessment: contract.riskAssessment ? JSON.parse(contract.riskAssessment) : [],
        complianceRequirements: contract.complianceRequirements ? JSON.parse(contract.complianceRequirements) : [],
        contingencyPlan: contract.contingencyPlan || '',

        // Contract Management
        penaltyClause: contract.penaltyClause || '',
        bonusClause: contract.bonusClause || '',
        terminationClause: contract.terminationClause || '',
        renewalTerms: contract.renewalTerms || '',
        changeManagement: contract.changeManagement ? JSON.parse(contract.changeManagement) : {},

        // Dependencies & Constraints
        dependencies: contract.dependencies ? JSON.parse(contract.dependencies) : [],
        assumptions: contract.assumptions ? JSON.parse(contract.assumptions) : [],
        constraints: contract.constraints ? JSON.parse(contract.constraints) : [],
        exclusions: contract.exclusions ? JSON.parse(contract.exclusions) : [],
      });

      setResourcePlan(contract.resourceRequirements ? JSON.parse(contract.resourceRequirements) : []);
      setSelectedSkills(contract.skillRequirements ? JSON.parse(contract.skillRequirements) : []);
    } else if (isOpen && mode === 'create') {
      // Reset to default values for create mode
      setFormData({
        // Basic Information
        title: '',
        description: '',
        clientId: '',
        type: 'FIXED_PRICE',
        contractCategory: 'DEVELOPMENT',
        priority: 'MEDIUM',
        riskLevel: 'LOW',
        complexityLevel: 'MEDIUM',

        // Timeline & Financial
        startDate: '',
        endDate: '',
        value: 0,
        currency: 'USD',
        paymentTerms: 'NET30',
        estimatedEffort: 0,

        // Legal & Compliance
        terms: '',
        securityLevel: 'STANDARD',
        confidentialityLevel: 'STANDARD',
        governingLaw: '',
        disputeResolution: '',
        ipRights: '',

        // Technical Requirements
        technologyStack: [],
        deliverables: [],
        milestones: [],
        qualityStandards: [],
        testingRequirements: [],

        // Resource Planning
        resourceRequirements: [],
        skillRequirements: [],
        projectTemplate: '',
        autoGenerateProject: false,

        // Performance & SLA
        slaRequirements: [],
        performanceMetrics: [],
        successCriteria: [],
        kpis: [],

        // Support & Maintenance
        supportLevel: 'STANDARD',
        maintenanceTerms: [],
        trainingRequirements: [],
        documentationRequirements: [],

        // Stakeholders & Communication
        clientContact: {},
        stakeholders: [],
        communicationPlan: {},
        escalationMatrix: [],

        // Risk & Compliance
        riskAssessment: [],
        complianceRequirements: [],
        contingencyPlan: '',

        // Contract Management
        penaltyClause: '',
        bonusClause: '',
        terminationClause: '',
        renewalTerms: '',
        changeManagement: {},

        // Dependencies & Constraints
        dependencies: [],
        assumptions: [],
        constraints: [],
        exclusions: [],
      });

      setResourcePlan([]);
      setSelectedSkills([]);
      setActiveTab('basic');
    }
  }, [isOpen, contract, mode]);

  const createContractMutation = useMutation({
    mutationFn: (data: any) => contractsApi.create(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['contracts'] });
      toast.success('Contract created successfully');
      onClose();
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to create contract');
    },
  });

  const updateContractMutation = useMutation({
    mutationFn: (data: any) => contractsApi.update(contract.id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['contracts'] });
      toast.success('Contract updated successfully');
      onClose();
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to update contract');
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (mode === 'view') return;

    const submitData = {
      ...formData,
      startDate: new Date(formData.startDate).toISOString(),
      endDate: new Date(formData.endDate).toISOString(),

      // Convert arrays and objects to JSON strings
      technologyStack: JSON.stringify(formData.technologyStack),
      deliverables: JSON.stringify(formData.deliverables),
      milestones: JSON.stringify(formData.milestones),
      qualityStandards: JSON.stringify(formData.qualityStandards),
      testingRequirements: JSON.stringify(formData.testingRequirements),
      resourceRequirements: JSON.stringify(resourcePlan),
      skillRequirements: JSON.stringify(selectedSkills),
      slaRequirements: JSON.stringify(formData.slaRequirements),
      performanceMetrics: JSON.stringify(formData.performanceMetrics),
      successCriteria: JSON.stringify(formData.successCriteria),
      kpis: JSON.stringify(formData.kpis),
      maintenanceTerms: JSON.stringify(formData.maintenanceTerms),
      trainingRequirements: JSON.stringify(formData.trainingRequirements),
      documentationRequirements: JSON.stringify(formData.documentationRequirements),
      clientContact: JSON.stringify(formData.clientContact),
      stakeholders: JSON.stringify(formData.stakeholders),
      communicationPlan: JSON.stringify(formData.communicationPlan),
      escalationMatrix: JSON.stringify(formData.escalationMatrix),
      riskAssessment: JSON.stringify(formData.riskAssessment),
      complianceRequirements: JSON.stringify(formData.complianceRequirements),
      changeManagement: JSON.stringify(formData.changeManagement),
      dependencies: JSON.stringify(formData.dependencies),
      assumptions: JSON.stringify(formData.assumptions),
      constraints: JSON.stringify(formData.constraints),
      exclusions: JSON.stringify(formData.exclusions),
    };

    if (mode === 'create') {
      createContractMutation.mutate(submitData);
    } else if (mode === 'edit') {
      updateContractMutation.mutate(submitData);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const isLoading = createContractMutation.isPending || updateContractMutation.isPending;
  const isReadOnly = mode === 'view';

  const getTitle = () => {
    switch (mode) {
      case 'create': return 'Create New Contract';
      case 'edit': return 'Edit Contract';
      case 'view': return 'Contract Details';
      default: return 'Contract';
    }
  };

  const getSelectedClient = () => {
    return clients.find((client: any) => client.id === formData.clientId);
  };

  // Resource Plan Management
  const addResourceRole = () => {
    const newRole = {
      id: Date.now().toString(),
      role: '',
      skillId: '',
      requiredCount: 1,
      allocationPercent: 100,
      minExperience: 0,
      maxBudget: 0,
      description: '',
    };
    setResourcePlan([...resourcePlan, newRole]);
  };

  const updateResourceRole = (id: string, field: string, value: any) => {
    setResourcePlan(resourcePlan.map((role: any) =>
      role.id === id ? { ...role, [field]: value } : role
    ));
  };

  const removeResourceRole = (id: string) => {
    setResourcePlan(resourcePlan.filter((role: any) => role.id !== id));
  };

  // Skill Management
  const addSkillRequirement = (skillId: string) => {
    if (!selectedSkills.find((s: any) => s.skillId === skillId)) {
      const skill = skills.find((s: any) => s.id === skillId);
      if (skill) {
        setSelectedSkills([...selectedSkills, {
          skillId: skill.id,
          skillName: skill.name,
          level: 'INTERMEDIATE',
          mandatory: true,
        }]);
      }
    }
  };

  const updateSkillRequirement = (skillId: string, field: string, value: any) => {
    setSelectedSkills(selectedSkills.map((skill: any) =>
      skill.skillId === skillId ? { ...skill, [field]: value } : skill
    ));
  };

  const removeSkillRequirement = (skillId: string) => {
    setSelectedSkills(selectedSkills.filter((skill: any) => skill.skillId !== skillId));
  };

  // Array field helpers
  const addArrayItem = (field: string, item: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: [...prev[field], item]
    }));
  };

  const updateArrayItem = (field: string, index: number, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field].map((item: any, i: number) => i === index ? value : item)
    }));
  };

  const removeArrayItem = (field: string, index: number) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field].filter((_: any, i: number) => i !== index)
    }));
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[95vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <FileText className="h-5 w-5" />
            <span>{getTitle()}</span>
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-6">
              <TabsTrigger value="basic">Basic Info</TabsTrigger>
              <TabsTrigger value="technical">Technical</TabsTrigger>
              <TabsTrigger value="resources">Resources</TabsTrigger>
              <TabsTrigger value="performance">Performance</TabsTrigger>
              <TabsTrigger value="legal">Legal</TabsTrigger>
              <TabsTrigger value="management">Management</TabsTrigger>
            </TabsList>

            {/* Basic Information Tab */}
            <TabsContent value="basic" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <FileText className="h-5 w-5" />
                    <span>Contract Information</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="title">Contract Title *</Label>
                      <Input
                        id="title"
                        value={formData.title}
                        onChange={(e) => handleInputChange('title', e.target.value)}
                        placeholder="Enter contract title"
                        required
                        readOnly={isReadOnly}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="type">Contract Type *</Label>
                      <select
                        id="type"
                        value={formData.type}
                        onChange={(e) => handleInputChange('type', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                        required
                        disabled={isReadOnly}
                      >
                        <option value="FIXED_PRICE">Fixed Price</option>
                        <option value="TIME_AND_MATERIAL">Time & Material</option>
                        <option value="RETAINER">Retainer</option>
                      </select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="contractCategory">Category *</Label>
                      <select
                        id="contractCategory"
                        value={formData.contractCategory}
                        onChange={(e) => handleInputChange('contractCategory', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                        required
                        disabled={isReadOnly}
                      >
                        <option value="DEVELOPMENT">Development</option>
                        <option value="MAINTENANCE">Maintenance</option>
                        <option value="CONSULTING">Consulting</option>
                        <option value="SUPPORT">Support</option>
                        <option value="TRAINING">Training</option>
                        <option value="INTEGRATION">Integration</option>
                        <option value="MIGRATION">Migration</option>
                        <option value="TESTING">Testing</option>
                        <option value="INFRASTRUCTURE">Infrastructure</option>
                      </select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="priority">Priority *</Label>
                      <select
                        id="priority"
                        value={formData.priority}
                        onChange={(e) => handleInputChange('priority', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                        required
                        disabled={isReadOnly}
                      >
                        <option value="LOW">Low</option>
                        <option value="MEDIUM">Medium</option>
                        <option value="HIGH">High</option>
                        <option value="CRITICAL">Critical</option>
                      </select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="riskLevel">Risk Level *</Label>
                      <select
                        id="riskLevel"
                        value={formData.riskLevel}
                        onChange={(e) => handleInputChange('riskLevel', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                        required
                        disabled={isReadOnly}
                      >
                        <option value="LOW">Low</option>
                        <option value="MEDIUM">Medium</option>
                        <option value="HIGH">High</option>
                        <option value="CRITICAL">Critical</option>
                      </select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="complexityLevel">Complexity Level *</Label>
                      <select
                        id="complexityLevel"
                        value={formData.complexityLevel}
                        onChange={(e) => handleInputChange('complexityLevel', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                        required
                        disabled={isReadOnly}
                      >
                        <option value="LOW">Low</option>
                        <option value="MEDIUM">Medium</option>
                        <option value="HIGH">High</option>
                        <option value="VERY_HIGH">Very High</option>
                      </select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="description">Description *</Label>
                    <Textarea
                      id="description"
                      value={formData.description}
                      onChange={(e) => handleInputChange('description', e.target.value)}
                      placeholder="Enter detailed contract description"
                      rows={4}
                      required
                      readOnly={isReadOnly}
                    />
                  </div>
                </CardContent>
              </Card>

          {/* Client Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <User className="h-5 w-5" />
                <span>Client Information</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {mode === 'view' && contract ? (
                <div className="p-4 bg-gray-50 rounded-lg">
                  <h4 className="font-medium text-gray-900">
                    {contract.firstName} {contract.lastName}
                  </h4>
                  <p className="text-sm text-gray-600">{contract.email}</p>
                  <p className="text-sm text-gray-600">{contract.phone}</p>
                </div>
              ) : (
                <div className="space-y-2">
                  <Label htmlFor="clientId">Client *</Label>
                  <select
                    id="clientId"
                    value={formData.clientId}
                    onChange={(e) => handleInputChange('clientId', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                    required
                    disabled={isReadOnly}
                  >
                    <option value="">Select a client</option>
                    {clients.map((client: any) => (
                      <option key={client.id} value={client.id}>
                        {client.firstName} {client.lastName} ({client.email})
                      </option>
                    ))}
                  </select>
                  {formData.clientId && getSelectedClient() && (
                    <div className="mt-2 p-3 bg-blue-50 rounded-lg">
                      <p className="text-sm text-blue-800">
                        <strong>Selected Client:</strong> {getSelectedClient()?.firstName} {getSelectedClient()?.lastName}
                      </p>
                      <p className="text-sm text-blue-600">{getSelectedClient()?.email}</p>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Financial Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <DollarSign className="h-5 w-5" />
                <span>Financial Details</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="value">Contract Value *</Label>
                  <Input
                    id="value"
                    type="number"
                    step="0.01"
                    value={formData.value}
                    onChange={(e) => handleInputChange('value', parseFloat(e.target.value) || 0)}
                    placeholder="0.00"
                    required
                    readOnly={isReadOnly}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="currency">Currency *</Label>
                  <select
                    id="currency"
                    value={formData.currency}
                    onChange={(e) => handleInputChange('currency', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                    required
                    disabled={isReadOnly}
                  >
                    <option value="USD">USD - US Dollar</option>
                    <option value="EUR">EUR - Euro</option>
                    <option value="GBP">GBP - British Pound</option>
                    <option value="INR">INR - Indian Rupee</option>
                    <option value="CAD">CAD - Canadian Dollar</option>
                    <option value="AUD">AUD - Australian Dollar</option>
                  </select>
                </div>
              </div>

              {mode === 'view' && (
                <div className="p-4 bg-green-50 rounded-lg">
                  <p className="text-lg font-semibold text-green-800">
                    Total Contract Value: {formatCurrency(formData.value, formData.currency)}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Timeline Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Calendar className="h-5 w-5" />
                <span>Contract Timeline</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="startDate">Start Date *</Label>
                  <Input
                    id="startDate"
                    type="date"
                    value={formData.startDate}
                    onChange={(e) => handleInputChange('startDate', e.target.value)}
                    required
                    readOnly={isReadOnly}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="endDate">End Date *</Label>
                  <Input
                    id="endDate"
                    type="date"
                    value={formData.endDate}
                    onChange={(e) => handleInputChange('endDate', e.target.value)}
                    required
                    readOnly={isReadOnly}
                  />
                </div>
              </div>

              {formData.startDate && formData.endDate && (
                <div className="p-3 bg-blue-50 rounded-lg">
                  <p className="text-sm text-blue-800">
                    <strong>Duration:</strong> {
                      Math.ceil((new Date(formData.endDate).getTime() - new Date(formData.startDate).getTime()) / (1000 * 60 * 60 * 24))
                    } days
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Terms and Conditions */}
          <Card>
            <CardHeader>
              <CardTitle>Terms and Conditions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="terms">Contract Terms *</Label>
                <Textarea
                  id="terms"
                  value={formData.terms}
                  onChange={(e) => handleInputChange('terms', e.target.value)}
                  placeholder="Enter contract terms and conditions..."
                  rows={6}
                  required
                  readOnly={isReadOnly}
                />
              </div>
            </CardContent>
          </Card>

          {/* Form Actions */}
          <div className="flex justify-end space-x-3 pt-4 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isLoading}
            >
              {mode === 'view' ? 'Close' : 'Cancel'}
            </Button>
            {mode !== 'view' && (
              <Button
                type="submit"
                className="bg-red-600 hover:bg-red-700"
                disabled={isLoading}
              >
                {isLoading
                  ? (mode === 'create' ? 'Creating...' : 'Updating...')
                  : (mode === 'create' ? 'Create Contract' : 'Update Contract')
                }
              </Button>
            )}
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
