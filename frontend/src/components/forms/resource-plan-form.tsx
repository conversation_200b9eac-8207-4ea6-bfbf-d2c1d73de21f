'use client';

import { useState, useEffect } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { resourcePlansApi, skillsApi } from '@/lib/api';
import { useAuth } from '@/hooks/useAuth';
import { Save, X } from 'lucide-react';
import toast from 'react-hot-toast';

interface ResourcePlanFormProps {
  isOpen: boolean;
  onClose: () => void;
  resourcePlan?: any;
  projectId: string;
  mode: 'create' | 'edit';
}

export function ResourcePlanForm({ 
  isOpen, 
  onClose, 
  resourcePlan, 
  projectId, 
  mode 
}: ResourcePlanFormProps) {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  
  const [formData, setFormData] = useState({
    skillId: '',
    role: '',
    allocationPercent: 100,
    requiredCount: 1,
    startDate: '',
    endDate: '',
    minExperience: 0,
    maxBudget: '',
    description: '',
    status: 'DRAFT',
  });

  // Fetch skills for dropdown
  const { data: skillsData } = useQuery({
    queryKey: ['skills'],
    queryFn: () => skillsApi.getAll({ limit: 100 }),
  });

  const skills = skillsData?.data || [];

  // Initialize form data when editing
  useEffect(() => {
    if (mode === 'edit' && resourcePlan) {
      setFormData({
        skillId: resourcePlan.skillId || '',
        role: resourcePlan.role || '',
        allocationPercent: resourcePlan.allocationPercent || 100,
        requiredCount: resourcePlan.requiredCount || 1,
        startDate: resourcePlan.startDate ? new Date(resourcePlan.startDate).toISOString().split('T')[0] : '',
        endDate: resourcePlan.endDate ? new Date(resourcePlan.endDate).toISOString().split('T')[0] : '',
        minExperience: resourcePlan.minExperience || 0,
        maxBudget: resourcePlan.maxBudget ? resourcePlan.maxBudget.toString() : '',
        description: resourcePlan.description || '',
        status: resourcePlan.status || 'DRAFT',
      });
    } else if (mode === 'create') {
      setFormData({
        skillId: '',
        role: '',
        allocationPercent: 100,
        requiredCount: 1,
        startDate: '',
        endDate: '',
        minExperience: 0,
        maxBudget: '',
        description: '',
        status: 'DRAFT',
      });
    }
  }, [mode, resourcePlan, isOpen]);

  const createMutation = useMutation({
    mutationFn: (data: any) => resourcePlansApi.create(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['resource-plans'] });
      queryClient.invalidateQueries({ queryKey: ['project', projectId] });
      toast.success('Resource plan created successfully');
      onClose();
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to create resource plan');
    },
  });

  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) => 
      resourcePlansApi.update(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['resource-plans'] });
      queryClient.invalidateQueries({ queryKey: ['project', projectId] });
      queryClient.invalidateQueries({ queryKey: ['resource-plan', resourcePlan?.id] });
      toast.success('Resource plan updated successfully');
      onClose();
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to update resource plan');
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const submitData = {
      ...formData,
      projectId,
      allocationPercent: Number(formData.allocationPercent),
      requiredCount: Number(formData.requiredCount),
      minExperience: Number(formData.minExperience),
      maxBudget: formData.maxBudget ? Number(formData.maxBudget) : undefined,
      startDate: new Date(formData.startDate).toISOString(),
      endDate: new Date(formData.endDate).toISOString(),
    };

    if (mode === 'create') {
      createMutation.mutate(submitData);
    } else if (mode === 'edit') {
      updateMutation.mutate({ id: resourcePlan.id, data: submitData });
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const isLoading = createMutation.isPending || updateMutation.isPending;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {mode === 'create' ? 'Create Resource Plan' : 'Edit Resource Plan'}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Skill Selection */}
            <div className="space-y-2">
              <Label htmlFor="skillId">Required Skill *</Label>
              <select
                id="skillId"
                value={formData.skillId}
                onChange={(e) => handleInputChange('skillId', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                required
              >
                <option value="">Select a skill</option>
                {skills.map((skill: any) => (
                  <option key={skill.id} value={skill.id}>
                    {skill.name} ({skill.category})
                  </option>
                ))}
              </select>
            </div>

            {/* Role */}
            <div className="space-y-2">
              <Label htmlFor="role">Role/Position *</Label>
              <Input
                id="role"
                value={formData.role}
                onChange={(e) => handleInputChange('role', e.target.value)}
                placeholder="e.g., Senior Developer, Project Lead"
                required
              />
            </div>

            {/* Allocation Percentage */}
            <div className="space-y-2">
              <Label htmlFor="allocationPercent">Allocation % *</Label>
              <Input
                id="allocationPercent"
                type="number"
                min="1"
                max="100"
                value={formData.allocationPercent}
                onChange={(e) => handleInputChange('allocationPercent', e.target.value)}
                required
              />
            </div>

            {/* Required Count */}
            <div className="space-y-2">
              <Label htmlFor="requiredCount">Required Count *</Label>
              <Input
                id="requiredCount"
                type="number"
                min="1"
                value={formData.requiredCount}
                onChange={(e) => handleInputChange('requiredCount', e.target.value)}
                required
              />
            </div>

            {/* Start Date */}
            <div className="space-y-2">
              <Label htmlFor="startDate">Start Date *</Label>
              <Input
                id="startDate"
                type="date"
                value={formData.startDate}
                onChange={(e) => handleInputChange('startDate', e.target.value)}
                required
              />
            </div>

            {/* End Date */}
            <div className="space-y-2">
              <Label htmlFor="endDate">End Date *</Label>
              <Input
                id="endDate"
                type="date"
                value={formData.endDate}
                onChange={(e) => handleInputChange('endDate', e.target.value)}
                required
              />
            </div>

            {/* Minimum Experience */}
            <div className="space-y-2">
              <Label htmlFor="minExperience">Min Experience (Years)</Label>
              <Input
                id="minExperience"
                type="number"
                min="0"
                value={formData.minExperience}
                onChange={(e) => handleInputChange('minExperience', e.target.value)}
              />
            </div>

            {/* Maximum Budget */}
            <div className="space-y-2">
              <Label htmlFor="maxBudget">Max Budget (per hour)</Label>
              <Input
                id="maxBudget"
                type="number"
                min="0"
                step="0.01"
                value={formData.maxBudget}
                onChange={(e) => handleInputChange('maxBudget', e.target.value)}
                placeholder="Optional"
              />
            </div>

            {/* Status (only for edit mode) */}
            {mode === 'edit' && (
              <div className="space-y-2">
                <Label htmlFor="status">Status</Label>
                <select
                  id="status"
                  value={formData.status}
                  onChange={(e) => handleInputChange('status', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                >
                  <option value="DRAFT">Draft</option>
                  <option value="ACTIVE">Active</option>
                  <option value="FULFILLED">Fulfilled</option>
                  <option value="CANCELLED">Cancelled</option>
                </select>
              </div>
            )}
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Additional requirements or notes..."
              rows={3}
            />
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-3 pt-4 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="bg-red-600 hover:bg-red-700"
              disabled={isLoading}
            >
              <Save className="h-4 w-4 mr-2" />
              {isLoading 
                ? (mode === 'create' ? 'Creating...' : 'Updating...') 
                : (mode === 'create' ? 'Create Plan' : 'Update Plan')
              }
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
