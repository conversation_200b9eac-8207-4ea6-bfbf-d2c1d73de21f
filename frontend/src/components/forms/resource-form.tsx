'use client';

import { useState, useEffect } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { resourcesApi, vendorsApi, skillsApi } from '@/lib/api';
import { useAuth } from '@/hooks/useAuth';
import { Save, User, GraduationCap, Award, Languages, Shield, Building, CreditCard } from 'lucide-react';
import toast from 'react-hot-toast';

interface ResourceFormProps {
  isOpen: boolean;
  onClose: () => void;
  resource?: any;
  mode: 'create' | 'edit';
}

export function ResourceForm({ 
  isOpen, 
  onClose, 
  resource, 
  mode 
}: ResourceFormProps) {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  
  const [formData, setFormData] = useState({
    // Personal Information
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    dateOfBirth: '',
    gender: '',
    maritalStatus: '',
    nationality: '',
    emergencyContact: '',

    // Professional Information
    designation: '',
    department: '',
    employmentType: 'FULL_TIME',
    employeeId: '',
    hourlyRate: '',
    salary: '',
    location: '',
    workLocation: '',
    joiningDate: '',
    confirmationDate: '',
    totalExperience: '',
    previousCompany: '',
    reportingManager: '',
    vendorId: '',
    status: 'AVAILABLE',

    // Identity & Documents
    panNumber: '',
    aadharNumber: '',
    passportNumber: '',
    drivingLicense: '',
    documentsVerified: false,

    // Banking Information
    bankDetails: '',

    // Security & Clearance
    backgroundCheck: false,
    backgroundCheckDate: '',
    securityClearance: '',
    clearanceExpiry: '',
    entryPass: '',
  });

  const [educations, setEducations] = useState([{
    degree: '',
    fieldOfStudy: '',
    institution: '',
    university: '',
    startYear: '',
    endYear: '',
    percentage: '',
    cgpa: ''
  }]);

  const [certifications, setCertifications] = useState([{
    name: '',
    issuingOrg: '',
    issueDate: '',
    expiryDate: '',
    credentialId: '',
    credentialUrl: '',
    verified: false
  }]);

  const [languages, setLanguages] = useState([{
    language: '',
    speaking: 'BEGINNER',
    reading: 'BEGINNER',
    writing: 'BEGINNER'
  }]);

  const [skills, setSkills] = useState([{
    skillId: '',
    proficiencyLevel: 1,
    yearsOfExperience: 0,
    certified: false
  }]);

  // Fetch vendors for dropdown
  const { data: vendorsData } = useQuery({
    queryKey: ['vendors'],
    queryFn: () => vendorsApi.getAll({ limit: 100 }),
  });

  // Fetch skills for dropdown
  const { data: skillsData } = useQuery({
    queryKey: ['skills'],
    queryFn: () => skillsApi.getAll({ limit: 100 }),
  });

  const vendors = vendorsData?.data || [];
  const availableSkills = skillsData?.data || [];

  // Reset form when modal opens/closes or mode changes
  useEffect(() => {
    if (isOpen && mode === 'edit' && resource) {
      setFormData({
        // Personal Information
        firstName: resource.user?.firstName || '',
        lastName: resource.user?.lastName || '',
        email: resource.user?.email || '',
        phone: resource.user?.phone || '',
        dateOfBirth: resource.dateOfBirth ? resource.dateOfBirth.split('T')[0] : '',
        gender: resource.gender || '',
        maritalStatus: resource.maritalStatus || '',
        nationality: resource.nationality || '',
        emergencyContact: resource.emergencyContact || '',

        // Professional Information
        designation: resource.designation || '',
        department: resource.department || '',
        employmentType: resource.employmentType || 'FULL_TIME',
        employeeId: resource.employeeId || '',
        hourlyRate: resource.hourlyRate?.toString() || '',
        salary: resource.salary?.toString() || '',
        location: resource.location || '',
        workLocation: resource.workLocation || '',
        joiningDate: resource.joiningDate ? resource.joiningDate.split('T')[0] : '',
        confirmationDate: resource.confirmationDate ? resource.confirmationDate.split('T')[0] : '',
        totalExperience: resource.totalExperience?.toString() || '',
        previousCompany: resource.previousCompany || '',
        reportingManager: resource.reportingManager || '',
        vendorId: resource.vendorId || '',
        status: resource.status || 'AVAILABLE',

        // Identity & Documents
        panNumber: resource.panNumber || '',
        aadharNumber: resource.aadharNumber || '',
        passportNumber: resource.passportNumber || '',
        drivingLicense: resource.drivingLicense || '',
        documentsVerified: resource.documentsVerified || false,

        // Banking Information
        bankDetails: resource.bankDetails || '',

        // Security & Clearance
        backgroundCheck: resource.backgroundCheck || false,
        backgroundCheckDate: resource.backgroundCheckDate ? resource.backgroundCheckDate.split('T')[0] : '',
        securityClearance: resource.securityClearance || '',
        clearanceExpiry: resource.clearanceExpiry ? resource.clearanceExpiry.split('T')[0] : '',
        entryPass: resource.entryPass || '',
      });

      // Set educations, certifications, languages, skills if available
      if (resource.educations) setEducations(resource.educations);
      if (resource.certifications) setCertifications(resource.certifications);
      if (resource.languages) setLanguages(resource.languages);
      if (resource.skills) setSkills(resource.skills);
    } else if (mode === 'create') {
      // Reset to default values for create mode
      setFormData({
        firstName: '', lastName: '', email: '', phone: '', dateOfBirth: '', gender: '', maritalStatus: '', nationality: '', emergencyContact: '',
        designation: '', department: '', employmentType: 'FULL_TIME', employeeId: '', hourlyRate: '', salary: '', location: '', workLocation: '',
        joiningDate: '', confirmationDate: '', totalExperience: '', previousCompany: '', reportingManager: '', vendorId: '', status: 'AVAILABLE',
        panNumber: '', aadharNumber: '', passportNumber: '', drivingLicense: '', documentsVerified: false, bankDetails: '',
        backgroundCheck: false, backgroundCheckDate: '', securityClearance: '', clearanceExpiry: '', entryPass: ''
      });
      setEducations([{ degree: '', fieldOfStudy: '', institution: '', university: '', startYear: '', endYear: '', percentage: '', cgpa: '' }]);
      setCertifications([{ name: '', issuingOrg: '', issueDate: '', expiryDate: '', credentialId: '', credentialUrl: '', verified: false }]);
      setLanguages([{ language: '', speaking: 'BEGINNER', reading: 'BEGINNER', writing: 'BEGINNER' }]);
      setSkills([{ skillId: '', proficiencyLevel: 1, yearsOfExperience: 0, certified: false }]);
    }
  }, [mode, resource, isOpen]);

  const createMutation = useMutation({
    mutationFn: (data: any) => resourcesApi.create(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['resources'] });
      toast.success('Resource created successfully');
      onClose();
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to create resource');
    },
  });

  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) => resourcesApi.update(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['resources'] });
      toast.success('Resource updated successfully');
      onClose();
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to update resource');
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    const submitData = {
      ...formData,
      hourlyRate: formData.hourlyRate ? Number(formData.hourlyRate) : undefined,
      salary: formData.salary ? Number(formData.salary) : undefined,
      totalExperience: formData.totalExperience ? Number(formData.totalExperience) : undefined,
      vendorId: formData.vendorId || undefined,
      dateOfBirth: formData.dateOfBirth ? new Date(formData.dateOfBirth).toISOString() : undefined,
      joiningDate: formData.joiningDate ? new Date(formData.joiningDate).toISOString() : undefined,
      confirmationDate: formData.confirmationDate ? new Date(formData.confirmationDate).toISOString() : undefined,
      backgroundCheckDate: formData.backgroundCheckDate ? new Date(formData.backgroundCheckDate).toISOString() : undefined,
      clearanceExpiry: formData.clearanceExpiry ? new Date(formData.clearanceExpiry).toISOString() : undefined,
      educations: educations.filter(edu => edu.degree && edu.fieldOfStudy && edu.institution),
      certifications: certifications.filter(cert => cert.name && cert.issuingOrg),
      languages: languages.filter(lang => lang.language),
      skills: skills.filter(skill => skill.skillId),
    };

    if (mode === 'create') {
      createMutation.mutate(submitData);
    } else if (mode === 'edit') {
      updateMutation.mutate({ id: resource.id, data: submitData });
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  // Helper functions for dynamic arrays
  const addEducation = () => {
    setEducations([...educations, { degree: '', fieldOfStudy: '', institution: '', university: '', startYear: '', endYear: '', percentage: '', cgpa: '' }]);
  };

  const removeEducation = (index: number) => {
    setEducations(educations.filter((_, i) => i !== index));
  };

  const updateEducation = (index: number, field: string, value: any) => {
    const updated = [...educations];
    updated[index] = { ...updated[index], [field]: value };
    setEducations(updated);
  };

  const addCertification = () => {
    setCertifications([...certifications, { name: '', issuingOrg: '', issueDate: '', expiryDate: '', credentialId: '', credentialUrl: '', verified: false }]);
  };

  const removeCertification = (index: number) => {
    setCertifications(certifications.filter((_, i) => i !== index));
  };

  const updateCertification = (index: number, field: string, value: any) => {
    const updated = [...certifications];
    updated[index] = { ...updated[index], [field]: value };
    setCertifications(updated);
  };

  const addLanguage = () => {
    setLanguages([...languages, { language: '', speaking: 'BEGINNER', reading: 'BEGINNER', writing: 'BEGINNER' }]);
  };

  const removeLanguage = (index: number) => {
    setLanguages(languages.filter((_, i) => i !== index));
  };

  const updateLanguage = (index: number, field: string, value: any) => {
    const updated = [...languages];
    updated[index] = { ...updated[index], [field]: value };
    setLanguages(updated);
  };

  const addSkill = () => {
    setSkills([...skills, { skillId: '', proficiencyLevel: 1, yearsOfExperience: 0, certified: false }]);
  };

  const removeSkill = (index: number) => {
    setSkills(skills.filter((_, i) => i !== index));
  };

  const updateSkill = (index: number, field: string, value: any) => {
    const updated = [...skills];
    updated[index] = { ...updated[index], [field]: value };
    setSkills(updated);
  };

  const isLoading = createMutation.isPending || updateMutation.isPending;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[95vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <User className="h-5 w-5" />
            <span>{mode === 'create' ? 'Add New Resource' : 'Edit Resource'}</span>
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <Tabs defaultValue="personal" className="w-full">
            <TabsList className="grid w-full grid-cols-6">
              <TabsTrigger value="personal" className="flex items-center space-x-1">
                <User className="h-4 w-4" />
                <span>Personal</span>
              </TabsTrigger>
              <TabsTrigger value="professional" className="flex items-center space-x-1">
                <Building className="h-4 w-4" />
                <span>Professional</span>
              </TabsTrigger>
              <TabsTrigger value="education" className="flex items-center space-x-1">
                <GraduationCap className="h-4 w-4" />
                <span>Education</span>
              </TabsTrigger>
              <TabsTrigger value="certifications" className="flex items-center space-x-1">
                <Award className="h-4 w-4" />
                <span>Certifications</span>
              </TabsTrigger>
              <TabsTrigger value="languages" className="flex items-center space-x-1">
                <Languages className="h-4 w-4" />
                <span>Languages</span>
              </TabsTrigger>
              <TabsTrigger value="security" className="flex items-center space-x-1">
                <Shield className="h-4 w-4" />
                <span>Security</span>
              </TabsTrigger>
            </TabsList>

            {/* Personal Information Tab */}
            <TabsContent value="personal" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="firstName">First Name *</Label>
                  <Input
                    id="firstName"
                    value={formData.firstName}
                    onChange={(e) => handleInputChange('firstName', e.target.value)}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="lastName">Last Name *</Label>
                  <Input
                    id="lastName"
                    value={formData.lastName}
                    onChange={(e) => handleInputChange('lastName', e.target.value)}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email">Email *</Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="phone">Phone</Label>
                  <Input
                    id="phone"
                    value={formData.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="dateOfBirth">Date of Birth</Label>
                  <Input
                    id="dateOfBirth"
                    type="date"
                    value={formData.dateOfBirth}
                    onChange={(e) => handleInputChange('dateOfBirth', e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="gender">Gender</Label>
                  <select
                    id="gender"
                    value={formData.gender}
                    onChange={(e) => handleInputChange('gender', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                  >
                    <option value="">Select Gender</option>
                    <option value="MALE">Male</option>
                    <option value="FEMALE">Female</option>
                    <option value="OTHER">Other</option>
                  </select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="maritalStatus">Marital Status</Label>
                  <select
                    id="maritalStatus"
                    value={formData.maritalStatus}
                    onChange={(e) => handleInputChange('maritalStatus', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                  >
                    <option value="">Select Status</option>
                    <option value="SINGLE">Single</option>
                    <option value="MARRIED">Married</option>
                    <option value="DIVORCED">Divorced</option>
                    <option value="WIDOWED">Widowed</option>
                  </select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="nationality">Nationality</Label>
                  <Input
                    id="nationality"
                    value={formData.nationality}
                    onChange={(e) => handleInputChange('nationality', e.target.value)}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="emergencyContact">Emergency Contact (JSON format)</Label>
                <Textarea
                  id="emergencyContact"
                  value={formData.emergencyContact}
                  onChange={(e) => handleInputChange('emergencyContact', e.target.value)}
                  placeholder='{"name": "John Doe", "relationship": "Father", "phone": "+**********", "email": "<EMAIL>"}'
                  rows={3}
                />
              </div>

              {/* Identity Documents */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">Identity Documents</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="panNumber">PAN Number</Label>
                    <Input
                      id="panNumber"
                      value={formData.panNumber}
                      onChange={(e) => handleInputChange('panNumber', e.target.value)}
                      placeholder="**********"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="aadharNumber">Aadhar Number</Label>
                    <Input
                      id="aadharNumber"
                      value={formData.aadharNumber}
                      onChange={(e) => handleInputChange('aadharNumber', e.target.value)}
                      placeholder="1234 5678 9012"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="passportNumber">Passport Number</Label>
                    <Input
                      id="passportNumber"
                      value={formData.passportNumber}
                      onChange={(e) => handleInputChange('passportNumber', e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="drivingLicense">Driving License</Label>
                    <Input
                      id="drivingLicense"
                      value={formData.drivingLicense}
                      onChange={(e) => handleInputChange('drivingLicense', e.target.value)}
                    />
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="documentsVerified"
                    checked={formData.documentsVerified}
                    onChange={(e) => handleInputChange('documentsVerified', e.target.checked)}
                    className="rounded border-gray-300 text-red-600 focus:ring-red-500"
                  />
                  <Label htmlFor="documentsVerified">Documents Verified</Label>
                </div>
              </div>
            </TabsContent>

            {/* Professional Information Tab */}
            <TabsContent value="professional" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="designation">Designation *</Label>
                  <Input
                    id="designation"
                    value={formData.designation}
                    onChange={(e) => handleInputChange('designation', e.target.value)}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="department">Department *</Label>
                  <Input
                    id="department"
                    value={formData.department}
                    onChange={(e) => handleInputChange('department', e.target.value)}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="employmentType">Employment Type</Label>
                  <select
                    id="employmentType"
                    value={formData.employmentType}
                    onChange={(e) => handleInputChange('employmentType', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                  >
                    <option value="FULL_TIME">Full Time</option>
                    <option value="CONTRACTOR">Contractor</option>
                    <option value="VENDOR">Vendor</option>
                  </select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="employeeId">Employee ID</Label>
                  <Input
                    id="employeeId"
                    value={formData.employeeId}
                    onChange={(e) => handleInputChange('employeeId', e.target.value)}
                    placeholder="Auto-generated if empty"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="hourlyRate">Hourly Rate ($)</Label>
                  <Input
                    id="hourlyRate"
                    type="number"
                    step="0.01"
                    value={formData.hourlyRate}
                    onChange={(e) => handleInputChange('hourlyRate', e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="salary">Annual Salary ($)</Label>
                  <Input
                    id="salary"
                    type="number"
                    step="0.01"
                    value={formData.salary}
                    onChange={(e) => handleInputChange('salary', e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="location">Location</Label>
                  <Input
                    id="location"
                    value={formData.location}
                    onChange={(e) => handleInputChange('location', e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="workLocation">Work Location</Label>
                  <Input
                    id="workLocation"
                    value={formData.workLocation}
                    onChange={(e) => handleInputChange('workLocation', e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="joiningDate">Joining Date</Label>
                  <Input
                    id="joiningDate"
                    type="date"
                    value={formData.joiningDate}
                    onChange={(e) => handleInputChange('joiningDate', e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="confirmationDate">Confirmation Date</Label>
                  <Input
                    id="confirmationDate"
                    type="date"
                    value={formData.confirmationDate}
                    onChange={(e) => handleInputChange('confirmationDate', e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="totalExperience">Total Experience (Years)</Label>
                  <Input
                    id="totalExperience"
                    type="number"
                    step="0.1"
                    value={formData.totalExperience}
                    onChange={(e) => handleInputChange('totalExperience', e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="previousCompany">Previous Company</Label>
                  <Input
                    id="previousCompany"
                    value={formData.previousCompany}
                    onChange={(e) => handleInputChange('previousCompany', e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="reportingManager">Reporting Manager</Label>
                  <Input
                    id="reportingManager"
                    value={formData.reportingManager}
                    onChange={(e) => handleInputChange('reportingManager', e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="vendorId">Vendor (for contractors)</Label>
                  <select
                    id="vendorId"
                    value={formData.vendorId}
                    onChange={(e) => handleInputChange('vendorId', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                  >
                    <option value="">Select a vendor (optional)</option>
                    {vendors.map((vendor: any) => (
                      <option key={vendor.id} value={vendor.id}>
                        {vendor.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="status">Status</Label>
                  <select
                    id="status"
                    value={formData.status}
                    onChange={(e) => handleInputChange('status', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                  >
                    <option value="AVAILABLE">Available</option>
                    <option value="ALLOCATED">Allocated</option>
                    <option value="ON_LEAVE">On Leave</option>
                    <option value="INACTIVE">Inactive</option>
                  </select>
                </div>
              </div>

              {/* Banking Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">Banking Information</h3>
                <div className="space-y-2">
                  <Label htmlFor="bankDetails">Bank Details (JSON format)</Label>
                  <Textarea
                    id="bankDetails"
                    value={formData.bankDetails}
                    onChange={(e) => handleInputChange('bankDetails', e.target.value)}
                    placeholder='{"bankName": "ABC Bank", "accountNumber": "**********", "ifscCode": "ABCD0123456", "accountType": "Savings"}'
                    rows={3}
                  />
                </div>
              </div>
            </TabsContent>

            {/* Education Tab */}
            <TabsContent value="education" className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900">Education Details</h3>
                <Button type="button" onClick={addEducation} variant="outline" size="sm">
                  Add Education
                </Button>
              </div>
              {educations.map((education, index) => (
                <div key={index} className="p-4 border rounded-lg space-y-4">
                  <div className="flex justify-between items-center">
                    <h4 className="font-medium">Education {index + 1}</h4>
                    {educations.length > 1 && (
                      <Button type="button" onClick={() => removeEducation(index)} variant="outline" size="sm">
                        Remove
                      </Button>
                    )}
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Input
                      placeholder="Degree"
                      value={education.degree}
                      onChange={(e) => updateEducation(index, 'degree', e.target.value)}
                    />
                    <Input
                      placeholder="Field of Study"
                      value={education.fieldOfStudy}
                      onChange={(e) => updateEducation(index, 'fieldOfStudy', e.target.value)}
                    />
                    <Input
                      placeholder="Institution"
                      value={education.institution}
                      onChange={(e) => updateEducation(index, 'institution', e.target.value)}
                    />
                    <Input
                      placeholder="University"
                      value={education.university}
                      onChange={(e) => updateEducation(index, 'university', e.target.value)}
                    />
                    <Input
                      placeholder="Start Year"
                      type="number"
                      value={education.startYear}
                      onChange={(e) => updateEducation(index, 'startYear', e.target.value)}
                    />
                    <Input
                      placeholder="End Year"
                      type="number"
                      value={education.endYear}
                      onChange={(e) => updateEducation(index, 'endYear', e.target.value)}
                    />
                    <Input
                      placeholder="Percentage"
                      type="number"
                      step="0.01"
                      value={education.percentage}
                      onChange={(e) => updateEducation(index, 'percentage', e.target.value)}
                    />
                    <Input
                      placeholder="CGPA"
                      type="number"
                      step="0.01"
                      value={education.cgpa}
                      onChange={(e) => updateEducation(index, 'cgpa', e.target.value)}
                    />
                  </div>
                </div>
              ))}
            </TabsContent>

            {/* Certifications Tab */}
            <TabsContent value="certifications" className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900">Certifications</h3>
                <Button type="button" onClick={addCertification} variant="outline" size="sm">
                  Add Certification
                </Button>
              </div>
              {certifications.map((cert, index) => (
                <div key={index} className="p-4 border rounded-lg space-y-4">
                  <div className="flex justify-between items-center">
                    <h4 className="font-medium">Certification {index + 1}</h4>
                    {certifications.length > 1 && (
                      <Button type="button" onClick={() => removeCertification(index)} variant="outline" size="sm">
                        Remove
                      </Button>
                    )}
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Input
                      placeholder="Certification Name"
                      value={cert.name}
                      onChange={(e) => updateCertification(index, 'name', e.target.value)}
                    />
                    <Input
                      placeholder="Issuing Organization"
                      value={cert.issuingOrg}
                      onChange={(e) => updateCertification(index, 'issuingOrg', e.target.value)}
                    />
                    <Input
                      placeholder="Issue Date"
                      type="date"
                      value={cert.issueDate}
                      onChange={(e) => updateCertification(index, 'issueDate', e.target.value)}
                    />
                    <Input
                      placeholder="Expiry Date"
                      type="date"
                      value={cert.expiryDate}
                      onChange={(e) => updateCertification(index, 'expiryDate', e.target.value)}
                    />
                    <Input
                      placeholder="Credential ID"
                      value={cert.credentialId}
                      onChange={(e) => updateCertification(index, 'credentialId', e.target.value)}
                    />
                    <Input
                      placeholder="Credential URL"
                      value={cert.credentialUrl}
                      onChange={(e) => updateCertification(index, 'credentialUrl', e.target.value)}
                    />
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={cert.verified}
                      onChange={(e) => updateCertification(index, 'verified', e.target.checked)}
                      className="rounded border-gray-300 text-red-600 focus:ring-red-500"
                    />
                    <Label>Verified</Label>
                  </div>
                </div>
              ))}
            </TabsContent>

            {/* Languages Tab */}
            <TabsContent value="languages" className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900">Language Proficiency</h3>
                <Button type="button" onClick={addLanguage} variant="outline" size="sm">
                  Add Language
                </Button>
              </div>
              {languages.map((lang, index) => (
                <div key={index} className="p-4 border rounded-lg space-y-4">
                  <div className="flex justify-between items-center">
                    <h4 className="font-medium">Language {index + 1}</h4>
                    {languages.length > 1 && (
                      <Button type="button" onClick={() => removeLanguage(index)} variant="outline" size="sm">
                        Remove
                      </Button>
                    )}
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <Input
                      placeholder="Language"
                      value={lang.language}
                      onChange={(e) => updateLanguage(index, 'language', e.target.value)}
                    />
                    <select
                      value={lang.speaking}
                      onChange={(e) => updateLanguage(index, 'speaking', e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                    >
                      <option value="BEGINNER">Beginner</option>
                      <option value="INTERMEDIATE">Intermediate</option>
                      <option value="ADVANCED">Advanced</option>
                      <option value="NATIVE">Native</option>
                    </select>
                    <select
                      value={lang.reading}
                      onChange={(e) => updateLanguage(index, 'reading', e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                    >
                      <option value="BEGINNER">Beginner</option>
                      <option value="INTERMEDIATE">Intermediate</option>
                      <option value="ADVANCED">Advanced</option>
                      <option value="NATIVE">Native</option>
                    </select>
                    <select
                      value={lang.writing}
                      onChange={(e) => updateLanguage(index, 'writing', e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                    >
                      <option value="BEGINNER">Beginner</option>
                      <option value="INTERMEDIATE">Intermediate</option>
                      <option value="ADVANCED">Advanced</option>
                      <option value="NATIVE">Native</option>
                    </select>
                  </div>
                </div>
              ))}
            </TabsContent>

            {/* Security Tab */}
            <TabsContent value="security" className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">Security & Clearance</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="securityClearance">Security Clearance</Label>
                    <Input
                      id="securityClearance"
                      value={formData.securityClearance}
                      onChange={(e) => handleInputChange('securityClearance', e.target.value)}
                      placeholder="SC Cleared, DV Cleared, etc."
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="clearanceExpiry">Clearance Expiry</Label>
                    <Input
                      id="clearanceExpiry"
                      type="date"
                      value={formData.clearanceExpiry}
                      onChange={(e) => handleInputChange('clearanceExpiry', e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="entryPass">Entry Pass</Label>
                    <Input
                      id="entryPass"
                      value={formData.entryPass}
                      onChange={(e) => handleInputChange('entryPass', e.target.value)}
                      placeholder="Building/Site specific entry pass"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="backgroundCheckDate">Background Check Date</Label>
                    <Input
                      id="backgroundCheckDate"
                      type="date"
                      value={formData.backgroundCheckDate}
                      onChange={(e) => handleInputChange('backgroundCheckDate', e.target.value)}
                    />
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="backgroundCheck"
                    checked={formData.backgroundCheck}
                    onChange={(e) => handleInputChange('backgroundCheck', e.target.checked)}
                    className="rounded border-gray-300 text-red-600 focus:ring-red-500"
                  />
                  <Label htmlFor="backgroundCheck">Background Check Completed</Label>
                </div>
              </div>

              {/* Skills Section */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium text-gray-900">Skills</h3>
                  <Button type="button" onClick={addSkill} variant="outline" size="sm">
                    Add Skill
                  </Button>
                </div>
                {skills.map((skill, index) => (
                  <div key={index} className="p-4 border rounded-lg space-y-4">
                    <div className="flex justify-between items-center">
                      <h4 className="font-medium">Skill {index + 1}</h4>
                      {skills.length > 1 && (
                        <Button type="button" onClick={() => removeSkill(index)} variant="outline" size="sm">
                          Remove
                        </Button>
                      )}
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                      <select
                        value={skill.skillId}
                        onChange={(e) => updateSkill(index, 'skillId', e.target.value)}
                        className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                      >
                        <option value="">Select Skill</option>
                        {availableSkills.map((s: any) => (
                          <option key={s.id} value={s.id}>
                            {s.name} ({s.category})
                          </option>
                        ))}
                      </select>
                      <Input
                        placeholder="Proficiency (1-5)"
                        type="number"
                        min="1"
                        max="5"
                        value={skill.proficiencyLevel}
                        onChange={(e) => updateSkill(index, 'proficiencyLevel', parseInt(e.target.value))}
                      />
                      <Input
                        placeholder="Years of Experience"
                        type="number"
                        min="0"
                        value={skill.yearsOfExperience}
                        onChange={(e) => updateSkill(index, 'yearsOfExperience', parseInt(e.target.value))}
                      />
                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={skill.certified}
                          onChange={(e) => updateSkill(index, 'certified', e.target.checked)}
                          className="rounded border-gray-300 text-red-600 focus:ring-red-500"
                        />
                        <Label>Certified</Label>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </TabsContent>
          </Tabs>

          {/* Form Actions */}
          <div className="flex justify-end space-x-3 pt-4 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="bg-red-600 hover:bg-red-700"
              disabled={isLoading}
            >
              <Save className="h-4 w-4 mr-2" />
              {isLoading
                ? (mode === 'create' ? 'Creating...' : 'Updating...')
                : (mode === 'create' ? 'Create Resource' : 'Update Resource')
              }
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
