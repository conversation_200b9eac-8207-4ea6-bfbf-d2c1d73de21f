'use client';

import { useState, useEffect } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { paymentsApi } from '@/lib/api';
import { useAuth } from '@/hooks/useAuth';
import { formatCurrency, formatDate } from '@/lib/utils';
import { 
  Send, 
  Building, 
  User, 
  CreditCard, 
  Calculator, 
  AlertTriangle,
  CheckCircle,
  DollarSign
} from 'lucide-react';
import toast from 'react-hot-toast';

interface PayrollProcessFormProps {
  isOpen: boolean;
  onClose: () => void;
  invoice?: any;
}

export function PayrollProcessForm({ 
  isOpen, 
  onClose, 
  invoice 
}: PayrollProcessFormProps) {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  
  const [formData, setFormData] = useState({
    paymentType: 'PAYROLL',
    paymentMethod: 'BANK_TRANSFER',
    amount: 0,
    grossAmount: 0,
    taxDeducted: 0,
    netAmount: 0,
    taxRate: 10,
    currency: 'USD',
    paymentDate: '',
    notes: '',
    bankDetails: '',
  });

  const [paymentDetails, setPaymentDetails] = useState({
    isContractor: false,
    hasVendor: false,
    vendorInfo: null,
    resourceInfo: null,
    paymentFlow: 'DIRECT',
  });

  // Reset form when modal opens/closes or invoice changes
  useEffect(() => {
    if (isOpen && invoice) {
      const isContractor = invoice.resource?.employmentType === 'CONTRACTOR';
      const hasVendor = isContractor && invoice.resource?.vendor;
      const vendorInfo = hasVendor ? invoice.resource.vendor : null;
      
      // Determine payment type based on resource type
      let paymentType = 'PAYROLL';
      if (isContractor && hasVendor) {
        paymentType = 'VENDOR_PAYMENT';
      } else if (isContractor) {
        paymentType = 'CONTRACTOR_DIRECT';
      }

      // Calculate tax and net amount
      const grossAmount = invoice.total || 0;
      const taxRate = isContractor ? 0 : 10; // Contractors handle their own taxes
      const taxDeducted = grossAmount * (taxRate / 100);
      const netAmount = grossAmount - taxDeducted;

      setFormData({
        paymentType,
        paymentMethod: 'BANK_TRANSFER',
        amount: grossAmount,
        grossAmount,
        taxDeducted,
        netAmount,
        taxRate,
        currency: invoice.currency || 'USD',
        paymentDate: new Date().toISOString().split('T')[0],
        notes: `Payment for ${invoice.invoiceNumber} - ${invoice.project?.name}`,
        bankDetails: hasVendor ? vendorInfo.bankDetails : invoice.resource?.bankDetails || '',
      });

      setPaymentDetails({
        isContractor,
        hasVendor,
        vendorInfo,
        resourceInfo: invoice.resource,
        paymentFlow: hasVendor ? 'VENDOR' : 'DIRECT',
      });
    } else if (isOpen && !invoice) {
      // Reset for batch processing
      setFormData({
        paymentType: 'PAYROLL',
        paymentMethod: 'BANK_TRANSFER',
        amount: 0,
        grossAmount: 0,
        taxDeducted: 0,
        netAmount: 0,
        taxRate: 10,
        currency: 'USD',
        paymentDate: new Date().toISOString().split('T')[0],
        notes: '',
        bankDetails: '',
      });
      
      setPaymentDetails({
        isContractor: false,
        hasVendor: false,
        vendorInfo: null,
        resourceInfo: null,
        paymentFlow: 'DIRECT',
      });
    }
  }, [isOpen, invoice]);

  // Recalculate amounts when tax rate changes
  useEffect(() => {
    if (formData.grossAmount > 0) {
      const taxDeducted = formData.grossAmount * (formData.taxRate / 100);
      const netAmount = formData.grossAmount - taxDeducted;
      
      setFormData(prev => ({
        ...prev,
        taxDeducted,
        netAmount,
        amount: formData.grossAmount,
      }));
    }
  }, [formData.grossAmount, formData.taxRate]);

  const processPaymentMutation = useMutation({
    mutationFn: (data: any) => paymentsApi.processPayment(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['payments'] });
      queryClient.invalidateQueries({ queryKey: ['invoices'] });
      queryClient.invalidateQueries({ queryKey: ['billing-stats'] });
      toast.success('Payment processed successfully');
      onClose();
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to process payment');
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!invoice) {
      toast.error('Please select an invoice to process');
      return;
    }

    const submitData = {
      invoiceId: invoice.id,
      resourceId: invoice.resourceId,
      vendorId: paymentDetails.hasVendor ? paymentDetails.vendorInfo?.id : null,
      paymentType: formData.paymentType,
      paymentMethod: formData.paymentMethod,
      amount: formData.amount,
      grossAmount: formData.grossAmount,
      taxDeducted: formData.taxDeducted,
      netAmount: formData.netAmount,
      currency: formData.currency,
      paymentDate: new Date(formData.paymentDate).toISOString(),
      notes: formData.notes,
      bankDetails: formData.bankDetails,
    };

    processPaymentMutation.mutate(submitData);
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const isLoading = processPaymentMutation.isPending;

  const getPaymentTypeInfo = () => {
    if (paymentDetails.hasVendor) {
      return {
        title: 'Vendor Payment',
        description: 'Payment will be made to the vendor who will handle contractor payment',
        icon: <Building className="h-5 w-5" />,
        color: 'bg-purple-100 text-purple-800',
      };
    } else if (paymentDetails.isContractor) {
      return {
        title: 'Direct Contractor Payment',
        description: 'Payment will be made directly to the contractor',
        icon: <CreditCard className="h-5 w-5" />,
        color: 'bg-blue-100 text-blue-800',
      };
    } else {
      return {
        title: 'Employee Payroll',
        description: 'Payment will be processed through regular payroll',
        icon: <User className="h-5 w-5" />,
        color: 'bg-green-100 text-green-800',
      };
    }
  };

  const paymentTypeInfo = getPaymentTypeInfo();

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[95vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Send className="h-5 w-5" />
            <span>Process Payment</span>
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* No Invoice Selected Message */}
          {!invoice && (
            <Card className="bg-yellow-50 border-yellow-200">
              <CardContent className="p-4">
                <div className="flex items-center space-x-3">
                  <AlertTriangle className="h-5 w-5 text-yellow-600" />
                  <div>
                    <h3 className="font-medium text-yellow-900">No Invoice Selected</h3>
                    <p className="text-sm text-yellow-700">
                      Please close this dialog and select an invoice from the billing page to process payment.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Invoice Information */}
          {invoice && (
            <Card className="bg-gray-50">
              <CardHeader>
                <CardTitle className="text-lg">Invoice Details</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-gray-600">Invoice Number</p>
                    <p className="font-medium">{invoice.invoiceNumber}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Project</p>
                    <p className="font-medium">{invoice.project?.name}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Resource</p>
                    <p className="font-medium">
                      {invoice.resource?.user?.firstName} {invoice.resource?.user?.lastName}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Total Amount</p>
                    <p className="font-medium text-lg">
                      {formatCurrency(invoice.total, invoice.currency)}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Payment Type Information */}
          <Card className="border-l-4 border-l-blue-500">
            <CardContent className="p-4">
              <div className="flex items-center space-x-3">
                <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${paymentTypeInfo.color}`}>
                  {paymentTypeInfo.icon}
                </div>
                <div>
                  <h3 className="font-medium text-gray-900">{paymentTypeInfo.title}</h3>
                  <p className="text-sm text-gray-600">{paymentTypeInfo.description}</p>
                </div>
              </div>
              
              {paymentDetails.hasVendor && paymentDetails.vendorInfo && (
                <div className="mt-4 p-3 bg-purple-50 rounded-lg">
                  <h4 className="font-medium text-purple-900">Vendor Information</h4>
                  <div className="mt-2 grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-purple-700">Name:</span>
                      <span className="ml-2 font-medium">{paymentDetails.vendorInfo.name}</span>
                    </div>
                    <div>
                      <span className="text-purple-700">Contact:</span>
                      <span className="ml-2 font-medium">{paymentDetails.vendorInfo.contactPerson}</span>
                    </div>
                    <div>
                      <span className="text-purple-700">Payment Terms:</span>
                      <span className="ml-2 font-medium">{paymentDetails.vendorInfo.paymentTerms || 'NET30'}</span>
                    </div>
                    <div>
                      <span className="text-purple-700">Currency:</span>
                      <span className="ml-2 font-medium">{paymentDetails.vendorInfo.currency || 'USD'}</span>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Payment Calculation */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Calculator className="h-5 w-5" />
                <span>Payment Calculation</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="grossAmount">Gross Amount ({formData.currency})</Label>
                    <Input
                      id="grossAmount"
                      type="number"
                      step="0.01"
                      value={formData.grossAmount}
                      onChange={(e) => handleInputChange('grossAmount', parseFloat(e.target.value) || 0)}
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="taxRate">Tax Rate (%)</Label>
                    <Input
                      id="taxRate"
                      type="number"
                      step="0.01"
                      value={formData.taxRate}
                      onChange={(e) => handleInputChange('taxRate', parseFloat(e.target.value) || 0)}
                      disabled={paymentDetails.isContractor}
                    />
                    {paymentDetails.isContractor && (
                      <p className="text-sm text-gray-600">
                        Contractors handle their own tax obligations
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="paymentMethod">Payment Method</Label>
                    <select
                      id="paymentMethod"
                      value={formData.paymentMethod}
                      onChange={(e) => handleInputChange('paymentMethod', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                    >
                      <option value="BANK_TRANSFER">Bank Transfer</option>
                      <option value="ACH">ACH Transfer</option>
                      <option value="WIRE_TRANSFER">Wire Transfer</option>
                      <option value="CHECK">Check</option>
                      <option value="DIGITAL_WALLET">Digital Wallet</option>
                    </select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="paymentDate">Payment Date</Label>
                    <Input
                      id="paymentDate"
                      type="date"
                      value={formData.paymentDate}
                      onChange={(e) => handleInputChange('paymentDate', e.target.value)}
                      required
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="p-4 bg-gray-50 rounded-lg space-y-3">
                    <h4 className="font-medium text-gray-900">Payment Breakdown</h4>

                    <div className="flex justify-between">
                      <span className="text-gray-600">Gross Amount:</span>
                      <span className="font-medium">{formatCurrency(formData.grossAmount, formData.currency)}</span>
                    </div>

                    <div className="flex justify-between">
                      <span className="text-gray-600">Tax Deducted ({formData.taxRate}%):</span>
                      <span className="font-medium text-red-600">
                        -{formatCurrency(formData.taxDeducted, formData.currency)}
                      </span>
                    </div>

                    <div className="border-t pt-2">
                      <div className="flex justify-between text-lg font-bold">
                        <span>Net Amount:</span>
                        <span className="text-green-600">
                          {formatCurrency(formData.netAmount, formData.currency)}
                        </span>
                      </div>
                    </div>
                  </div>

                  {!paymentDetails.isContractor && (
                    <Card className="bg-blue-50 border-blue-200">
                      <CardContent className="p-3">
                        <div className="flex items-start space-x-2">
                          <AlertTriangle className="h-4 w-4 text-blue-600 mt-0.5" />
                          <div className="text-sm text-blue-800">
                            <p className="font-medium">Payroll Processing</p>
                            <p>Tax will be deducted and remitted to tax authorities as per employment regulations.</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )}

                  {paymentDetails.isContractor && (
                    <Card className="bg-orange-50 border-orange-200">
                      <CardContent className="p-3">
                        <div className="flex items-start space-x-2">
                          <AlertTriangle className="h-4 w-4 text-orange-600 mt-0.5" />
                          <div className="text-sm text-orange-800">
                            <p className="font-medium">Contractor Payment</p>
                            <p>Contractor is responsible for their own tax obligations and compliance.</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Payment Details */}
          <Card>
            <CardHeader>
              <CardTitle>Payment Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="notes">Payment Notes</Label>
                <Textarea
                  id="notes"
                  value={formData.notes}
                  onChange={(e) => handleInputChange('notes', e.target.value)}
                  placeholder="Additional notes for this payment..."
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="bankDetails">Banking Information</Label>
                <Textarea
                  id="bankDetails"
                  value={formData.bankDetails}
                  onChange={(e) => handleInputChange('bankDetails', e.target.value)}
                  placeholder="Banking details for payment processing..."
                  rows={3}
                  readOnly={paymentDetails.hasVendor}
                />
                {paymentDetails.hasVendor && (
                  <p className="text-sm text-gray-600">
                    Using vendor banking information from vendor profile
                  </p>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Form Actions */}
          <div className="flex justify-end space-x-3 pt-4 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="bg-green-600 hover:bg-green-700"
              disabled={isLoading || !invoice}
            >
              <Send className="h-4 w-4 mr-2" />
              {!invoice
                ? 'Select Invoice First'
                : isLoading
                ? 'Processing...'
                : `Process Payment (${formatCurrency(formData.netAmount, formData.currency)})`
              }
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
