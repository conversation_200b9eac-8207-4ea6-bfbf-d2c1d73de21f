'use client';

import { useState, useEffect } from 'react';
import { useMutation, useQueryClient, useQuery } from '@tanstack/react-query';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { contractsApi, usersApi, skillsApi, projectsApi } from '@/lib/api';
import { useAuth } from '@/hooks/useAuth';
import { formatCurrency } from '@/lib/utils';
import { 
  FileText, 
  User, 
  Calendar, 
  DollarSign, 
  Settings, 
  Shield, 
  Users, 
  Target, 
  Zap,
  Plus,
  Trash2,
  AlertTriangle,
  CheckCircle,
  Clock,
  Building,
  Code,
  Award,
  Scale
} from 'lucide-react';
import toast from 'react-hot-toast';

interface EnhancedContractFormProps {
  isOpen: boolean;
  onClose: () => void;
  contract?: any;
  mode: 'create' | 'edit' | 'view';
}

export function EnhancedContractForm({ 
  isOpen, 
  onClose, 
  contract, 
  mode 
}: EnhancedContractFormProps) {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  
  const [formData, setFormData] = useState({
    // Basic Information
    title: '',
    description: '',
    clientId: '',
    type: 'FIXED_PRICE',
    contractCategory: 'DEVELOPMENT',
    priority: 'MEDIUM',
    riskLevel: 'LOW',
    complexityLevel: 'MEDIUM',
    
    // Timeline & Financial
    startDate: '',
    endDate: '',
    value: 0,
    currency: 'USD',
    paymentTerms: 'NET30',
    estimatedEffort: 0,
    
    // Legal & Compliance
    terms: '',
    securityLevel: 'STANDARD',
    confidentialityLevel: 'STANDARD',
    governingLaw: '',
    disputeResolution: '',
    ipRights: '',
    
    // Technical Requirements
    technologyStack: [],
    deliverables: [],
    milestones: [],
    qualityStandards: [],
    testingRequirements: [],
    
    // Resource Planning
    resourceRequirements: [],
    skillRequirements: [],
    projectTemplate: '',
    autoGenerateProject: false,
    
    // Performance & SLA
    slaRequirements: [],
    performanceMetrics: [],
    successCriteria: [],
    kpis: [],
    
    // Support & Maintenance
    supportLevel: 'STANDARD',
    maintenanceTerms: [],
    trainingRequirements: [],
    documentationRequirements: [],
    
    // Stakeholders & Communication
    clientContact: {},
    stakeholders: [],
    communicationPlan: {},
    escalationMatrix: [],
    
    // Risk & Compliance
    riskAssessment: [],
    complianceRequirements: [],
    contingencyPlan: '',
    
    // Contract Management
    penaltyClause: '',
    bonusClause: '',
    terminationClause: '',
    renewalTerms: '',
    changeManagement: {},
    
    // Dependencies & Constraints
    dependencies: [],
    assumptions: [],
    constraints: [],
    exclusions: [],
  });

  const [activeTab, setActiveTab] = useState('basic');
  const [resourcePlan, setResourcePlan] = useState([]);
  const [selectedSkills, setSelectedSkills] = useState([]);

  // Fetch data
  const { data: clientsData } = useQuery({
    queryKey: ['users', { role: 'CLIENT' }],
    queryFn: () => usersApi.getAll({ role: 'CLIENT', limit: 100 }),
    enabled: isOpen && mode !== 'view',
  });

  const { data: skillsData } = useQuery({
    queryKey: ['skills'],
    queryFn: () => skillsApi.getAll({ limit: 100 }),
    enabled: isOpen,
  });

  const { data: managersData } = useQuery({
    queryKey: ['users', { role: 'PROJECT_MANAGER' }],
    queryFn: () => usersApi.getAll({ role: 'PROJECT_MANAGER', limit: 100 }),
    enabled: isOpen && mode !== 'view',
  });

  const clients = clientsData?.data || [];
  const skills = skillsData?.data || [];
  const managers = managersData?.data || [];

  // Mutations
  const createContractMutation = useMutation({
    mutationFn: (data: any) => contractsApi.create(data),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ['contracts'] });
      toast.success('Contract created successfully');
      
      // If auto-generate project is enabled, trigger project creation
      if (formData.autoGenerateProject && response.data) {
        handleGenerateProject(response.data);
      } else {
        onClose();
      }
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to create contract');
    },
  });

  const updateContractMutation = useMutation({
    mutationFn: (data: any) => contractsApi.update(contract.id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['contracts'] });
      toast.success('Contract updated successfully');
      onClose();
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to update contract');
    },
  });

  // Generate project and resource plans
  const generateProjectMutation = useMutation({
    mutationFn: (data: any) => projectsApi.generateFromContract(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['projects'] });
      queryClient.invalidateQueries({ queryKey: ['resource-plans'] });
      toast.success('Project and resource plans generated successfully');
      onClose();
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to generate project');
    },
  });

  const handleGenerateProject = (contractData: any) => {
    const projectData = {
      contractId: contractData.id,
      resourcePlan,
      skillRequirements: selectedSkills,
      autoCreateResourcePlans: true,
    };
    generateProjectMutation.mutate(projectData);
  };

  // Reset form when modal opens/closes or contract changes
  useEffect(() => {
    if (isOpen && contract && (mode === 'edit' || mode === 'view')) {
      setFormData({
        // Basic Information
        title: contract.title || '',
        description: contract.description || '',
        clientId: contract.clientId || '',
        type: contract.type || 'FIXED_PRICE',
        contractCategory: contract.contractCategory || 'DEVELOPMENT',
        priority: contract.priority || 'MEDIUM',
        riskLevel: contract.riskLevel || 'LOW',
        complexityLevel: contract.complexityLevel || 'MEDIUM',

        // Timeline & Financial
        startDate: contract.startDate ? new Date(contract.startDate).toISOString().split('T')[0] : '',
        endDate: contract.endDate ? new Date(contract.endDate).toISOString().split('T')[0] : '',
        value: contract.value || 0,
        currency: contract.currency || 'USD',
        paymentTerms: contract.paymentTerms || 'NET30',
        estimatedEffort: contract.estimatedEffort || 0,

        // Legal & Compliance
        terms: contract.terms || '',
        securityLevel: contract.securityLevel || 'STANDARD',
        confidentialityLevel: contract.confidentialityLevel || 'STANDARD',
        governingLaw: contract.governingLaw || '',
        disputeResolution: contract.disputeResolution || '',
        ipRights: contract.ipRights || '',

        // Technical Requirements
        technologyStack: contract.technologyStack ? JSON.parse(contract.technologyStack) : [],
        deliverables: contract.deliverables ? JSON.parse(contract.deliverables) : [],
        milestones: contract.milestones ? JSON.parse(contract.milestones) : [],
        qualityStandards: contract.qualityStandards ? JSON.parse(contract.qualityStandards) : [],
        testingRequirements: contract.testingRequirements ? JSON.parse(contract.testingRequirements) : [],

        // Resource Planning
        resourceRequirements: contract.resourceRequirements ? JSON.parse(contract.resourceRequirements) : [],
        skillRequirements: contract.skillRequirements ? JSON.parse(contract.skillRequirements) : [],
        projectTemplate: contract.projectTemplate || '',
        autoGenerateProject: contract.autoGenerateProject || false,

        // Performance & SLA
        slaRequirements: contract.slaRequirements ? JSON.parse(contract.slaRequirements) : [],
        performanceMetrics: contract.performanceMetrics ? JSON.parse(contract.performanceMetrics) : [],
        successCriteria: contract.successCriteria ? JSON.parse(contract.successCriteria) : [],
        kpis: contract.kpis ? JSON.parse(contract.kpis) : [],

        // Support & Maintenance
        supportLevel: contract.supportLevel || 'STANDARD',
        maintenanceTerms: contract.maintenanceTerms ? JSON.parse(contract.maintenanceTerms) : [],
        trainingRequirements: contract.trainingRequirements ? JSON.parse(contract.trainingRequirements) : [],
        documentationRequirements: contract.documentationRequirements ? JSON.parse(contract.documentationRequirements) : [],

        // Stakeholders & Communication
        clientContact: contract.clientContact ? JSON.parse(contract.clientContact) : {},
        stakeholders: contract.stakeholders ? JSON.parse(contract.stakeholders) : [],
        communicationPlan: contract.communicationPlan ? JSON.parse(contract.communicationPlan) : {},
        escalationMatrix: contract.escalationMatrix ? JSON.parse(contract.escalationMatrix) : [],

        // Risk & Compliance
        riskAssessment: contract.riskAssessment ? JSON.parse(contract.riskAssessment) : [],
        complianceRequirements: contract.complianceRequirements ? JSON.parse(contract.complianceRequirements) : [],
        contingencyPlan: contract.contingencyPlan || '',

        // Contract Management
        penaltyClause: contract.penaltyClause || '',
        bonusClause: contract.bonusClause || '',
        terminationClause: contract.terminationClause || '',
        renewalTerms: contract.renewalTerms || '',
        changeManagement: contract.changeManagement ? JSON.parse(contract.changeManagement) : {},

        // Dependencies & Constraints
        dependencies: contract.dependencies ? JSON.parse(contract.dependencies) : [],
        assumptions: contract.assumptions ? JSON.parse(contract.assumptions) : [],
        constraints: contract.constraints ? JSON.parse(contract.constraints) : [],
        exclusions: contract.exclusions ? JSON.parse(contract.exclusions) : [],
      });

      setResourcePlan(contract.resourceRequirements ? JSON.parse(contract.resourceRequirements) : []);
      setSelectedSkills(contract.skillRequirements ? JSON.parse(contract.skillRequirements) : []);
    } else if (isOpen && mode === 'create') {
      // Reset to default values for create mode
      setFormData({
        // Basic Information
        title: '',
        description: '',
        clientId: '',
        type: 'FIXED_PRICE',
        contractCategory: 'DEVELOPMENT',
        priority: 'MEDIUM',
        riskLevel: 'LOW',
        complexityLevel: 'MEDIUM',

        // Timeline & Financial
        startDate: '',
        endDate: '',
        value: 0,
        currency: 'USD',
        paymentTerms: 'NET30',
        estimatedEffort: 0,

        // Legal & Compliance
        terms: '',
        securityLevel: 'STANDARD',
        confidentialityLevel: 'STANDARD',
        governingLaw: '',
        disputeResolution: '',
        ipRights: '',

        // Technical Requirements
        technologyStack: [],
        deliverables: [],
        milestones: [],
        qualityStandards: [],
        testingRequirements: [],

        // Resource Planning
        resourceRequirements: [],
        skillRequirements: [],
        projectTemplate: '',
        autoGenerateProject: false,

        // Performance & SLA
        slaRequirements: [],
        performanceMetrics: [],
        successCriteria: [],
        kpis: [],

        // Support & Maintenance
        supportLevel: 'STANDARD',
        maintenanceTerms: [],
        trainingRequirements: [],
        documentationRequirements: [],

        // Stakeholders & Communication
        clientContact: {},
        stakeholders: [],
        communicationPlan: {},
        escalationMatrix: [],

        // Risk & Compliance
        riskAssessment: [],
        complianceRequirements: [],
        contingencyPlan: '',

        // Contract Management
        penaltyClause: '',
        bonusClause: '',
        terminationClause: '',
        renewalTerms: '',
        changeManagement: {},

        // Dependencies & Constraints
        dependencies: [],
        assumptions: [],
        constraints: [],
        exclusions: [],
      });

      setResourcePlan([]);
      setSelectedSkills([]);
      setActiveTab('basic');
    }
  }, [isOpen, contract, mode]);

  // Form handlers
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (mode === 'view') return;

    const submitData = {
      ...formData,
      startDate: new Date(formData.startDate).toISOString(),
      endDate: new Date(formData.endDate).toISOString(),
      
      // Convert arrays and objects to JSON strings
      technologyStack: JSON.stringify(formData.technologyStack),
      deliverables: JSON.stringify(formData.deliverables),
      milestones: JSON.stringify(formData.milestones),
      qualityStandards: JSON.stringify(formData.qualityStandards),
      testingRequirements: JSON.stringify(formData.testingRequirements),
      resourceRequirements: JSON.stringify(resourcePlan),
      skillRequirements: JSON.stringify(selectedSkills),
      slaRequirements: JSON.stringify(formData.slaRequirements),
      performanceMetrics: JSON.stringify(formData.performanceMetrics),
      successCriteria: JSON.stringify(formData.successCriteria),
      kpis: JSON.stringify(formData.kpis),
      maintenanceTerms: JSON.stringify(formData.maintenanceTerms),
      trainingRequirements: JSON.stringify(formData.trainingRequirements),
      documentationRequirements: JSON.stringify(formData.documentationRequirements),
      clientContact: JSON.stringify(formData.clientContact),
      stakeholders: JSON.stringify(formData.stakeholders),
      communicationPlan: JSON.stringify(formData.communicationPlan),
      escalationMatrix: JSON.stringify(formData.escalationMatrix),
      riskAssessment: JSON.stringify(formData.riskAssessment),
      complianceRequirements: JSON.stringify(formData.complianceRequirements),
      changeManagement: JSON.stringify(formData.changeManagement),
      dependencies: JSON.stringify(formData.dependencies),
      assumptions: JSON.stringify(formData.assumptions),
      constraints: JSON.stringify(formData.constraints),
      exclusions: JSON.stringify(formData.exclusions),
    };

    if (mode === 'create') {
      createContractMutation.mutate(submitData);
    } else if (mode === 'edit') {
      updateContractMutation.mutate(submitData);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const isLoading = createContractMutation.isPending || updateContractMutation.isPending || generateProjectMutation.isPending;
  const isReadOnly = mode === 'view';

  const getTitle = () => {
    switch (mode) {
      case 'create': return 'Create Enterprise Contract';
      case 'edit': return 'Edit Contract';
      case 'view': return 'Contract Details';
      default: return 'Contract';
    }
  };

  const getSelectedClient = () => {
    return clients.find((client: any) => client.id === formData.clientId);
  };

  // Resource Plan Management
  const addResourceRole = () => {
    const newRole = {
      id: Date.now().toString(),
      role: '',
      skillId: '',
      requiredCount: 1,
      allocationPercent: 100,
      minExperience: 0,
      maxBudget: 0,
      description: '',
    };
    setResourcePlan([...resourcePlan, newRole]);
  };

  const updateResourceRole = (id: string, field: string, value: any) => {
    setResourcePlan(resourcePlan.map((role: any) =>
      role.id === id ? { ...role, [field]: value } : role
    ));
  };

  const removeResourceRole = (id: string) => {
    setResourcePlan(resourcePlan.filter((role: any) => role.id !== id));
  };

  // Skill Management
  const addSkillRequirement = (skillId: string) => {
    if (!selectedSkills.find((s: any) => s.skillId === skillId)) {
      const skill = skills.find((s: any) => s.id === skillId);
      if (skill) {
        setSelectedSkills([...selectedSkills, {
          skillId: skill.id,
          skillName: skill.name,
          level: 'INTERMEDIATE',
          mandatory: true,
        }]);
      }
    }
  };

  const updateSkillRequirement = (skillId: string, field: string, value: any) => {
    setSelectedSkills(selectedSkills.map((skill: any) =>
      skill.skillId === skillId ? { ...skill, [field]: value } : skill
    ));
  };

  const removeSkillRequirement = (skillId: string) => {
    setSelectedSkills(selectedSkills.filter((skill: any) => skill.skillId !== skillId));
  };

  // Array field helpers
  const addArrayItem = (field: string, item: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: [...prev[field], item]
    }));
  };

  const updateArrayItem = (field: string, index: number, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field].map((item: any, i: number) => i === index ? value : item)
    }));
  };

  const removeArrayItem = (field: string, index: number) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field].filter((_: any, i: number) => i !== index)
    }));
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[95vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <FileText className="h-5 w-5" />
            <span>{getTitle()}</span>
            {formData.autoGenerateProject && (
              <Badge className="bg-green-100 text-green-800">
                <Zap className="h-3 w-3 mr-1" />
                Auto-Generate Project
              </Badge>
            )}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-6">
              <TabsTrigger value="basic">
                <FileText className="h-4 w-4 mr-2" />
                Basic Info
              </TabsTrigger>
              <TabsTrigger value="technical">
                <Code className="h-4 w-4 mr-2" />
                Technical
              </TabsTrigger>
              <TabsTrigger value="resources">
                <Users className="h-4 w-4 mr-2" />
                Resources
              </TabsTrigger>
              <TabsTrigger value="performance">
                <Target className="h-4 w-4 mr-2" />
                Performance
              </TabsTrigger>
              <TabsTrigger value="legal">
                <Scale className="h-4 w-4 mr-2" />
                Legal
              </TabsTrigger>
              <TabsTrigger value="management">
                <Settings className="h-4 w-4 mr-2" />
                Management
              </TabsTrigger>
            </TabsList>

            {/* Basic Information Tab */}
            <TabsContent value="basic" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Contract Information */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <FileText className="h-5 w-5" />
                      <span>Contract Information</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="title">Contract Title *</Label>
                      <Input
                        id="title"
                        value={formData.title}
                        onChange={(e) => handleInputChange('title', e.target.value)}
                        placeholder="Enter contract title"
                        required
                        readOnly={isReadOnly}
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="type">Contract Type *</Label>
                        <select
                          id="type"
                          value={formData.type}
                          onChange={(e) => handleInputChange('type', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                          required
                          disabled={isReadOnly}
                        >
                          <option value="FIXED_PRICE">Fixed Price</option>
                          <option value="TIME_AND_MATERIAL">Time & Material</option>
                          <option value="RETAINER">Retainer</option>
                        </select>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="contractCategory">Category *</Label>
                        <select
                          id="contractCategory"
                          value={formData.contractCategory}
                          onChange={(e) => handleInputChange('contractCategory', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                          required
                          disabled={isReadOnly}
                        >
                          <option value="DEVELOPMENT">Development</option>
                          <option value="MAINTENANCE">Maintenance</option>
                          <option value="CONSULTING">Consulting</option>
                          <option value="SUPPORT">Support</option>
                          <option value="TRAINING">Training</option>
                          <option value="INTEGRATION">Integration</option>
                          <option value="MIGRATION">Migration</option>
                          <option value="TESTING">Testing</option>
                          <option value="INFRASTRUCTURE">Infrastructure</option>
                        </select>
                      </div>
                    </div>

                    <div className="grid grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="priority">Priority *</Label>
                        <select
                          id="priority"
                          value={formData.priority}
                          onChange={(e) => handleInputChange('priority', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                          required
                          disabled={isReadOnly}
                        >
                          <option value="LOW">Low</option>
                          <option value="MEDIUM">Medium</option>
                          <option value="HIGH">High</option>
                          <option value="CRITICAL">Critical</option>
                        </select>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="riskLevel">Risk Level *</Label>
                        <select
                          id="riskLevel"
                          value={formData.riskLevel}
                          onChange={(e) => handleInputChange('riskLevel', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                          required
                          disabled={isReadOnly}
                        >
                          <option value="LOW">Low</option>
                          <option value="MEDIUM">Medium</option>
                          <option value="HIGH">High</option>
                          <option value="CRITICAL">Critical</option>
                        </select>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="complexityLevel">Complexity *</Label>
                        <select
                          id="complexityLevel"
                          value={formData.complexityLevel}
                          onChange={(e) => handleInputChange('complexityLevel', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                          required
                          disabled={isReadOnly}
                        >
                          <option value="LOW">Low</option>
                          <option value="MEDIUM">Medium</option>
                          <option value="HIGH">High</option>
                          <option value="VERY_HIGH">Very High</option>
                        </select>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="description">Description *</Label>
                      <Textarea
                        id="description"
                        value={formData.description}
                        onChange={(e) => handleInputChange('description', e.target.value)}
                        placeholder="Enter detailed contract description"
                        rows={4}
                        required
                        readOnly={isReadOnly}
                      />
                    </div>
                  </CardContent>
                </Card>

                {/* Client & Financial Information */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <User className="h-5 w-5" />
                      <span>Client & Financial</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="clientId">Client *</Label>
                      <select
                        id="clientId"
                        value={formData.clientId}
                        onChange={(e) => handleInputChange('clientId', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                        required
                        disabled={isReadOnly}
                      >
                        <option value="">Select a client</option>
                        {clients.map((client: any) => (
                          <option key={client.id} value={client.id}>
                            {client.firstName} {client.lastName} ({client.email})
                          </option>
                        ))}
                      </select>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="value">Contract Value *</Label>
                        <Input
                          id="value"
                          type="number"
                          step="0.01"
                          value={formData.value}
                          onChange={(e) => handleInputChange('value', parseFloat(e.target.value) || 0)}
                          placeholder="0.00"
                          required
                          readOnly={isReadOnly}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="currency">Currency *</Label>
                        <select
                          id="currency"
                          value={formData.currency}
                          onChange={(e) => handleInputChange('currency', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                          required
                          disabled={isReadOnly}
                        >
                          <option value="USD">USD - US Dollar</option>
                          <option value="EUR">EUR - Euro</option>
                          <option value="GBP">GBP - British Pound</option>
                          <option value="INR">INR - Indian Rupee</option>
                          <option value="CAD">CAD - Canadian Dollar</option>
                          <option value="AUD">AUD - Australian Dollar</option>
                        </select>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="paymentTerms">Payment Terms *</Label>
                        <select
                          id="paymentTerms"
                          value={formData.paymentTerms}
                          onChange={(e) => handleInputChange('paymentTerms', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                          required
                          disabled={isReadOnly}
                        >
                          <option value="NET15">Net 15 Days</option>
                          <option value="NET30">Net 30 Days</option>
                          <option value="NET45">Net 45 Days</option>
                          <option value="NET60">Net 60 Days</option>
                          <option value="ADVANCE">Advance Payment</option>
                          <option value="COD">Cash on Delivery</option>
                        </select>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="estimatedEffort">Estimated Effort (Person-Months)</Label>
                        <Input
                          id="estimatedEffort"
                          type="number"
                          step="0.1"
                          value={formData.estimatedEffort}
                          onChange={(e) => handleInputChange('estimatedEffort', parseFloat(e.target.value) || 0)}
                          placeholder="0.0"
                          readOnly={isReadOnly}
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="startDate">Start Date *</Label>
                        <Input
                          id="startDate"
                          type="date"
                          value={formData.startDate}
                          onChange={(e) => handleInputChange('startDate', e.target.value)}
                          required
                          readOnly={isReadOnly}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="endDate">End Date *</Label>
                        <Input
                          id="endDate"
                          type="date"
                          value={formData.endDate}
                          onChange={(e) => handleInputChange('endDate', e.target.value)}
                          required
                          readOnly={isReadOnly}
                        />
                      </div>
                    </div>

                    {formData.startDate && formData.endDate && (
                      <div className="p-3 bg-blue-50 rounded-lg">
                        <p className="text-sm text-blue-800">
                          <strong>Duration:</strong> {
                            Math.ceil((new Date(formData.endDate).getTime() - new Date(formData.startDate).getTime()) / (1000 * 60 * 60 * 24))
                          } days
                        </p>
                        {mode === 'view' && (
                          <p className="text-lg font-semibold text-blue-900 mt-2">
                            Total Value: {formatCurrency(formData.value, formData.currency)}
                          </p>
                        )}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* Resources Tab - Key Feature for Auto-Generation */}
            <TabsContent value="resources" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Resource Planning */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Users className="h-5 w-5" />
                        <span>Resource Planning</span>
                      </div>
                      {!isReadOnly && (
                        <Button
                          type="button"
                          size="sm"
                          onClick={addResourceRole}
                          className="bg-green-600 hover:bg-green-700"
                        >
                          <Plus className="h-4 w-4 mr-1" />
                          Add Role
                        </Button>
                      )}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {resourcePlan.length === 0 ? (
                      <div className="text-center py-8 text-gray-500">
                        <Users className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                        <p>No resource roles defined</p>
                        <p className="text-sm">Add roles to enable automatic project generation</p>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {resourcePlan.map((role: any) => (
                          <div key={role.id} className="border rounded-lg p-4">
                            <div className="grid grid-cols-2 gap-4 mb-3">
                              <div className="space-y-2">
                                <Label>Role Title *</Label>
                                <Input
                                  value={role.role}
                                  onChange={(e) => updateResourceRole(role.id, 'role', e.target.value)}
                                  placeholder="e.g., Senior Developer"
                                  readOnly={isReadOnly}
                                />
                              </div>
                              <div className="space-y-2">
                                <Label>Required Count *</Label>
                                <Input
                                  type="number"
                                  value={role.requiredCount}
                                  onChange={(e) => updateResourceRole(role.id, 'requiredCount', parseInt(e.target.value) || 1)}
                                  min="1"
                                  readOnly={isReadOnly}
                                />
                              </div>
                            </div>
                            <div className="grid grid-cols-3 gap-4 mb-3">
                              <div className="space-y-2">
                                <Label>Allocation %</Label>
                                <Input
                                  type="number"
                                  value={role.allocationPercent}
                                  onChange={(e) => updateResourceRole(role.id, 'allocationPercent', parseInt(e.target.value) || 100)}
                                  min="1"
                                  max="100"
                                  readOnly={isReadOnly}
                                />
                              </div>
                              <div className="space-y-2">
                                <Label>Min Experience (Years)</Label>
                                <Input
                                  type="number"
                                  value={role.minExperience}
                                  onChange={(e) => updateResourceRole(role.id, 'minExperience', parseInt(e.target.value) || 0)}
                                  min="0"
                                  readOnly={isReadOnly}
                                />
                              </div>
                              <div className="space-y-2">
                                <Label>Max Budget ({formData.currency})</Label>
                                <Input
                                  type="number"
                                  value={role.maxBudget}
                                  onChange={(e) => updateResourceRole(role.id, 'maxBudget', parseFloat(e.target.value) || 0)}
                                  min="0"
                                  step="0.01"
                                  readOnly={isReadOnly}
                                />
                              </div>
                            </div>
                            <div className="space-y-2 mb-3">
                              <Label>Role Description</Label>
                              <Textarea
                                value={role.description}
                                onChange={(e) => updateResourceRole(role.id, 'description', e.target.value)}
                                placeholder="Describe the role responsibilities and requirements"
                                rows={2}
                                readOnly={isReadOnly}
                              />
                            </div>
                            {!isReadOnly && (
                              <Button
                                type="button"
                                size="sm"
                                variant="outline"
                                onClick={() => removeResourceRole(role.id)}
                                className="text-red-600 hover:text-red-700"
                              >
                                <Trash2 className="h-4 w-4 mr-1" />
                                Remove
                              </Button>
                            )}
                          </div>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Skill Requirements */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Award className="h-5 w-5" />
                      <span>Skill Requirements</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {!isReadOnly && (
                      <div className="space-y-2">
                        <Label>Add Skill</Label>
                        <select
                          onChange={(e) => {
                            if (e.target.value) {
                              addSkillRequirement(e.target.value);
                              e.target.value = '';
                            }
                          }}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                        >
                          <option value="">Select a skill to add</option>
                          {skills.filter((skill: any) =>
                            !selectedSkills.find((s: any) => s.skillId === skill.id)
                          ).map((skill: any) => (
                            <option key={skill.id} value={skill.id}>
                              {skill.name}
                            </option>
                          ))}
                        </select>
                      </div>
                    )}

                    {selectedSkills.length === 0 ? (
                      <div className="text-center py-8 text-gray-500">
                        <Award className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                        <p>No skills defined</p>
                        <p className="text-sm">Add skills for resource matching</p>
                      </div>
                    ) : (
                      <div className="space-y-3">
                        {selectedSkills.map((skill: any) => (
                          <div key={skill.skillId} className="flex items-center justify-between p-3 border rounded-lg">
                            <div className="flex-1">
                              <p className="font-medium">{skill.skillName}</p>
                              <div className="grid grid-cols-2 gap-4 mt-2">
                                <div className="space-y-1">
                                  <Label className="text-xs">Level</Label>
                                  <select
                                    value={skill.level}
                                    onChange={(e) => updateSkillRequirement(skill.skillId, 'level', e.target.value)}
                                    className="w-full px-2 py-1 text-sm border border-gray-300 rounded"
                                    disabled={isReadOnly}
                                  >
                                    <option value="BEGINNER">Beginner</option>
                                    <option value="INTERMEDIATE">Intermediate</option>
                                    <option value="ADVANCED">Advanced</option>
                                    <option value="EXPERT">Expert</option>
                                  </select>
                                </div>
                                <div className="space-y-1">
                                  <Label className="text-xs">Mandatory</Label>
                                  <select
                                    value={skill.mandatory ? 'true' : 'false'}
                                    onChange={(e) => updateSkillRequirement(skill.skillId, 'mandatory', e.target.value === 'true')}
                                    className="w-full px-2 py-1 text-sm border border-gray-300 rounded"
                                    disabled={isReadOnly}
                                  >
                                    <option value="true">Yes</option>
                                    <option value="false">No</option>
                                  </select>
                                </div>
                              </div>
                            </div>
                            {!isReadOnly && (
                              <Button
                                type="button"
                                size="sm"
                                variant="outline"
                                onClick={() => removeSkillRequirement(skill.skillId)}
                                className="ml-3 text-red-600 hover:text-red-700"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>

              {/* Auto-Generate Project Option */}
              {!isReadOnly && (
                <Card className="border-green-200 bg-green-50">
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2 text-green-800">
                      <Zap className="h-5 w-5" />
                      <span>Automatic Project Generation</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center space-x-3">
                      <input
                        type="checkbox"
                        id="autoGenerateProject"
                        checked={formData.autoGenerateProject}
                        onChange={(e) => handleInputChange('autoGenerateProject', e.target.checked)}
                        className="w-4 h-4 text-green-600 border-gray-300 rounded focus:ring-green-500"
                      />
                      <Label htmlFor="autoGenerateProject" className="text-green-800">
                        Automatically generate project and resource loading plans when contract is created
                      </Label>
                    </div>
                    {formData.autoGenerateProject && (
                      <div className="mt-4 p-4 bg-green-100 rounded-lg">
                        <h4 className="font-medium text-green-900 mb-2">What will be generated:</h4>
                        <ul className="text-sm text-green-800 space-y-1">
                          <li>• Project with contract timeline and milestones</li>
                          <li>• Resource loading plans for each defined role</li>
                          <li>• Skill requirements mapping for resource matching</li>
                          <li>• Budget allocation based on resource roles</li>
                          <li>• Task structure based on deliverables</li>
                        </ul>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            {/* Technical Requirements Tab */}
            <TabsContent value="technical" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Technology Stack */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Code className="h-5 w-5" />
                      <span>Technology Stack</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {!isReadOnly && (
                      <div className="space-y-2">
                        <Label>Add Technology</Label>
                        <div className="flex space-x-2">
                          <Input
                            placeholder="Technology name (e.g., React)"
                            onKeyPress={(e) => {
                              if (e.key === 'Enter') {
                                const value = e.currentTarget.value.trim();
                                if (value) {
                                  addArrayItem('technologyStack', { name: value, version: '' });
                                  e.currentTarget.value = '';
                                }
                              }
                            }}
                          />
                          <Button
                            type="button"
                            size="sm"
                            onClick={() => {
                              const input = document.querySelector('input[placeholder*="Technology name"]') as HTMLInputElement;
                              const value = input?.value.trim();
                              if (value) {
                                addArrayItem('technologyStack', { name: value, version: '' });
                                input.value = '';
                              }
                            }}
                          >
                            <Plus className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    )}

                    {formData.technologyStack.length === 0 ? (
                      <div className="text-center py-8 text-gray-500">
                        <Code className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                        <p>No technologies defined</p>
                      </div>
                    ) : (
                      <div className="space-y-3">
                        {formData.technologyStack.map((tech: any, index: number) => (
                          <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                            <div className="flex-1 grid grid-cols-2 gap-4">
                              <div className="space-y-1">
                                <Label className="text-xs">Technology</Label>
                                <Input
                                  value={tech.name}
                                  onChange={(e) => updateArrayItem('technologyStack', index, { ...tech, name: e.target.value })}
                                  placeholder="Technology name"
                                  readOnly={isReadOnly}
                                />
                              </div>
                              <div className="space-y-1">
                                <Label className="text-xs">Version</Label>
                                <Input
                                  value={tech.version}
                                  onChange={(e) => updateArrayItem('technologyStack', index, { ...tech, version: e.target.value })}
                                  placeholder="Version"
                                  readOnly={isReadOnly}
                                />
                              </div>
                            </div>
                            {!isReadOnly && (
                              <Button
                                type="button"
                                size="sm"
                                variant="outline"
                                onClick={() => removeArrayItem('technologyStack', index)}
                                className="ml-3 text-red-600 hover:text-red-700"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Deliverables */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Target className="h-5 w-5" />
                      <span>Deliverables</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {!isReadOnly && (
                      <Button
                        type="button"
                        size="sm"
                        onClick={() => addArrayItem('deliverables', { title: '', description: '', dueDate: '' })}
                        className="bg-green-600 hover:bg-green-700"
                      >
                        <Plus className="h-4 w-4 mr-1" />
                        Add Deliverable
                      </Button>
                    )}

                    {formData.deliverables.length === 0 ? (
                      <div className="text-center py-8 text-gray-500">
                        <Target className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                        <p>No deliverables defined</p>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {formData.deliverables.map((deliverable: any, index: number) => (
                          <div key={index} className="border rounded-lg p-4">
                            <div className="grid grid-cols-1 gap-3">
                              <div className="space-y-2">
                                <Label>Title *</Label>
                                <Input
                                  value={deliverable.title}
                                  onChange={(e) => updateArrayItem('deliverables', index, { ...deliverable, title: e.target.value })}
                                  placeholder="Deliverable title"
                                  readOnly={isReadOnly}
                                />
                              </div>
                              <div className="space-y-2">
                                <Label>Description</Label>
                                <Textarea
                                  value={deliverable.description}
                                  onChange={(e) => updateArrayItem('deliverables', index, { ...deliverable, description: e.target.value })}
                                  placeholder="Deliverable description"
                                  rows={2}
                                  readOnly={isReadOnly}
                                />
                              </div>
                              <div className="grid grid-cols-2 gap-4">
                                <div className="space-y-2">
                                  <Label>Due Date</Label>
                                  <Input
                                    type="date"
                                    value={deliverable.dueDate}
                                    onChange={(e) => updateArrayItem('deliverables', index, { ...deliverable, dueDate: e.target.value })}
                                    readOnly={isReadOnly}
                                  />
                                </div>
                                {!isReadOnly && (
                                  <div className="flex items-end">
                                    <Button
                                      type="button"
                                      size="sm"
                                      variant="outline"
                                      onClick={() => removeArrayItem('deliverables', index)}
                                      className="text-red-600 hover:text-red-700"
                                    >
                                      <Trash2 className="h-4 w-4 mr-1" />
                                      Remove
                                    </Button>
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>

              {/* Milestones */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="h-5 w-5" />
                      <span>Project Milestones</span>
                    </div>
                    {!isReadOnly && (
                      <Button
                        type="button"
                        size="sm"
                        onClick={() => addArrayItem('milestones', { title: '', description: '', dueDate: '', percentage: 0 })}
                        className="bg-green-600 hover:bg-green-700"
                      >
                        <Plus className="h-4 w-4 mr-1" />
                        Add Milestone
                      </Button>
                    )}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {formData.milestones.length === 0 ? (
                    <div className="text-center py-8 text-gray-500">
                      <CheckCircle className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                      <p>No milestones defined</p>
                      <p className="text-sm">Add milestones to track project progress</p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {formData.milestones.map((milestone: any, index: number) => (
                        <div key={index} className="border rounded-lg p-4">
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <Label>Milestone Title *</Label>
                              <Input
                                value={milestone.title}
                                onChange={(e) => updateArrayItem('milestones', index, { ...milestone, title: e.target.value })}
                                placeholder="Milestone title"
                                readOnly={isReadOnly}
                              />
                            </div>
                            <div className="space-y-2">
                              <Label>Due Date</Label>
                              <Input
                                type="date"
                                value={milestone.dueDate}
                                onChange={(e) => updateArrayItem('milestones', index, { ...milestone, dueDate: e.target.value })}
                                readOnly={isReadOnly}
                              />
                            </div>
                          </div>
                          <div className="mt-3 space-y-2">
                            <Label>Description</Label>
                            <Textarea
                              value={milestone.description}
                              onChange={(e) => updateArrayItem('milestones', index, { ...milestone, description: e.target.value })}
                              placeholder="Milestone description"
                              rows={2}
                              readOnly={isReadOnly}
                            />
                          </div>
                          <div className="mt-3 grid grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <Label>Completion Percentage</Label>
                              <Input
                                type="number"
                                value={milestone.percentage}
                                onChange={(e) => updateArrayItem('milestones', index, { ...milestone, percentage: parseInt(e.target.value) || 0 })}
                                placeholder="0"
                                min="0"
                                max="100"
                                readOnly={isReadOnly}
                              />
                            </div>
                            {!isReadOnly && (
                              <div className="flex items-end">
                                <Button
                                  type="button"
                                  size="sm"
                                  variant="outline"
                                  onClick={() => removeArrayItem('milestones', index)}
                                  className="text-red-600 hover:text-red-700"
                                >
                                  <Trash2 className="h-4 w-4 mr-1" />
                                  Remove
                                </Button>
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Quality Standards & Testing */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Award className="h-5 w-5" />
                      <span>Quality Standards</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {!isReadOnly && (
                      <div className="space-y-2">
                        <Label>Add Quality Standard</Label>
                        <div className="flex space-x-2">
                          <Input
                            placeholder="Quality standard (e.g., ISO 9001)"
                            onKeyPress={(e) => {
                              if (e.key === 'Enter') {
                                const value = e.currentTarget.value.trim();
                                if (value) {
                                  addArrayItem('qualityStandards', { name: value, description: '' });
                                  e.currentTarget.value = '';
                                }
                              }
                            }}
                          />
                          <Button
                            type="button"
                            size="sm"
                            onClick={() => {
                              const input = document.querySelector('input[placeholder*="Quality standard"]') as HTMLInputElement;
                              const value = input?.value.trim();
                              if (value) {
                                addArrayItem('qualityStandards', { name: value, description: '' });
                                input.value = '';
                              }
                            }}
                          >
                            <Plus className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    )}

                    {formData.qualityStandards.length === 0 ? (
                      <div className="text-center py-6 text-gray-500">
                        <Award className="h-10 w-10 mx-auto mb-2 text-gray-300" />
                        <p className="text-sm">No quality standards defined</p>
                      </div>
                    ) : (
                      <div className="space-y-3">
                        {formData.qualityStandards.map((standard: any, index: number) => (
                          <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                            <div className="flex-1">
                              <p className="font-medium">{standard.name}</p>
                              {standard.description && (
                                <p className="text-sm text-gray-600">{standard.description}</p>
                              )}
                            </div>
                            {!isReadOnly && (
                              <Button
                                type="button"
                                size="sm"
                                variant="outline"
                                onClick={() => removeArrayItem('qualityStandards', index)}
                                className="ml-3 text-red-600 hover:text-red-700"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <CheckCircle className="h-5 w-5" />
                      <span>Testing Requirements</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {!isReadOnly && (
                      <div className="space-y-2">
                        <Label>Add Testing Requirement</Label>
                        <div className="flex space-x-2">
                          <Input
                            placeholder="Testing type (e.g., Unit Testing)"
                            onKeyPress={(e) => {
                              if (e.key === 'Enter') {
                                const value = e.currentTarget.value.trim();
                                if (value) {
                                  addArrayItem('testingRequirements', { type: value, coverage: '', description: '' });
                                  e.currentTarget.value = '';
                                }
                              }
                            }}
                          />
                          <Button
                            type="button"
                            size="sm"
                            onClick={() => {
                              const input = document.querySelector('input[placeholder*="Testing type"]') as HTMLInputElement;
                              const value = input?.value.trim();
                              if (value) {
                                addArrayItem('testingRequirements', { type: value, coverage: '', description: '' });
                                input.value = '';
                              }
                            }}
                          >
                            <Plus className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    )}

                    {formData.testingRequirements.length === 0 ? (
                      <div className="text-center py-6 text-gray-500">
                        <CheckCircle className="h-10 w-10 mx-auto mb-2 text-gray-300" />
                        <p className="text-sm">No testing requirements defined</p>
                      </div>
                    ) : (
                      <div className="space-y-3">
                        {formData.testingRequirements.map((test: any, index: number) => (
                          <div key={index} className="border rounded-lg p-3">
                            <div className="flex items-center justify-between mb-2">
                              <p className="font-medium">{test.type}</p>
                              {!isReadOnly && (
                                <Button
                                  type="button"
                                  size="sm"
                                  variant="outline"
                                  onClick={() => removeArrayItem('testingRequirements', index)}
                                  className="text-red-600 hover:text-red-700"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              )}
                            </div>
                            <div className="space-y-2">
                              <Input
                                value={test.coverage}
                                onChange={(e) => updateArrayItem('testingRequirements', index, { ...test, coverage: e.target.value })}
                                placeholder="Coverage requirement (e.g., 90%)"
                                readOnly={isReadOnly}
                              />
                              <Textarea
                                value={test.description}
                                onChange={(e) => updateArrayItem('testingRequirements', index, { ...test, description: e.target.value })}
                                placeholder="Testing description"
                                rows={2}
                                readOnly={isReadOnly}
                              />
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="performance" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* SLA Requirements */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Clock className="h-5 w-5" />
                        <span>SLA Requirements</span>
                      </div>
                      {!isReadOnly && (
                        <Button
                          type="button"
                          size="sm"
                          onClick={() => addArrayItem('slaRequirements', { metric: '', target: '', penalty: '', description: '' })}
                          className="bg-green-600 hover:bg-green-700"
                        >
                          <Plus className="h-4 w-4 mr-1" />
                          Add SLA
                        </Button>
                      )}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {formData.slaRequirements.length === 0 ? (
                      <div className="text-center py-8 text-gray-500">
                        <Clock className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                        <p>No SLA requirements defined</p>
                        <p className="text-sm">Add SLA metrics to ensure service quality</p>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {formData.slaRequirements.map((sla: any, index: number) => (
                          <div key={index} className="border rounded-lg p-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div className="space-y-2">
                                <Label>SLA Metric *</Label>
                                <Input
                                  value={sla.metric}
                                  onChange={(e) => updateArrayItem('slaRequirements', index, { ...sla, metric: e.target.value })}
                                  placeholder="e.g., Response Time"
                                  readOnly={isReadOnly}
                                />
                              </div>
                              <div className="space-y-2">
                                <Label>Target *</Label>
                                <Input
                                  value={sla.target}
                                  onChange={(e) => updateArrayItem('slaRequirements', index, { ...sla, target: e.target.value })}
                                  placeholder="e.g., < 2 seconds"
                                  readOnly={isReadOnly}
                                />
                              </div>
                            </div>
                            <div className="mt-3 grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div className="space-y-2">
                                <Label>Penalty for Breach</Label>
                                <Input
                                  value={sla.penalty}
                                  onChange={(e) => updateArrayItem('slaRequirements', index, { ...sla, penalty: e.target.value })}
                                  placeholder="e.g., 5% of monthly fee"
                                  readOnly={isReadOnly}
                                />
                              </div>
                              {!isReadOnly && (
                                <div className="flex items-end">
                                  <Button
                                    type="button"
                                    size="sm"
                                    variant="outline"
                                    onClick={() => removeArrayItem('slaRequirements', index)}
                                    className="text-red-600 hover:text-red-700"
                                  >
                                    <Trash2 className="h-4 w-4 mr-1" />
                                    Remove
                                  </Button>
                                </div>
                              )}
                            </div>
                            <div className="mt-3 space-y-2">
                              <Label>Description</Label>
                              <Textarea
                                value={sla.description}
                                onChange={(e) => updateArrayItem('slaRequirements', index, { ...sla, description: e.target.value })}
                                placeholder="Detailed SLA description"
                                rows={2}
                                readOnly={isReadOnly}
                              />
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Performance Metrics */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Target className="h-5 w-5" />
                        <span>Performance Metrics</span>
                      </div>
                      {!isReadOnly && (
                        <Button
                          type="button"
                          size="sm"
                          onClick={() => addArrayItem('performanceMetrics', { name: '', target: '', measurement: '', frequency: '' })}
                          className="bg-green-600 hover:bg-green-700"
                        >
                          <Plus className="h-4 w-4 mr-1" />
                          Add Metric
                        </Button>
                      )}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {formData.performanceMetrics.length === 0 ? (
                      <div className="text-center py-8 text-gray-500">
                        <Target className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                        <p>No performance metrics defined</p>
                        <p className="text-sm">Add metrics to track project performance</p>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {formData.performanceMetrics.map((metric: any, index: number) => (
                          <div key={index} className="border rounded-lg p-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div className="space-y-2">
                                <Label>Metric Name *</Label>
                                <Input
                                  value={metric.name}
                                  onChange={(e) => updateArrayItem('performanceMetrics', index, { ...metric, name: e.target.value })}
                                  placeholder="e.g., Code Quality Score"
                                  readOnly={isReadOnly}
                                />
                              </div>
                              <div className="space-y-2">
                                <Label>Target Value *</Label>
                                <Input
                                  value={metric.target}
                                  onChange={(e) => updateArrayItem('performanceMetrics', index, { ...metric, target: e.target.value })}
                                  placeholder="e.g., > 8.5/10"
                                  readOnly={isReadOnly}
                                />
                              </div>
                            </div>
                            <div className="mt-3 grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div className="space-y-2">
                                <Label>Measurement Method</Label>
                                <Input
                                  value={metric.measurement}
                                  onChange={(e) => updateArrayItem('performanceMetrics', index, { ...metric, measurement: e.target.value })}
                                  placeholder="e.g., SonarQube Analysis"
                                  readOnly={isReadOnly}
                                />
                              </div>
                              <div className="space-y-2">
                                <Label>Frequency</Label>
                                <select
                                  value={metric.frequency}
                                  onChange={(e) => updateArrayItem('performanceMetrics', index, { ...metric, frequency: e.target.value })}
                                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                                  disabled={isReadOnly}
                                >
                                  <option value="">Select frequency</option>
                                  <option value="DAILY">Daily</option>
                                  <option value="WEEKLY">Weekly</option>
                                  <option value="MONTHLY">Monthly</option>
                                  <option value="QUARTERLY">Quarterly</option>
                                  <option value="ON_DELIVERY">On Delivery</option>
                                </select>
                              </div>
                            </div>
                            {!isReadOnly && (
                              <div className="mt-3 flex justify-end">
                                <Button
                                  type="button"
                                  size="sm"
                                  variant="outline"
                                  onClick={() => removeArrayItem('performanceMetrics', index)}
                                  className="text-red-600 hover:text-red-700"
                                >
                                  <Trash2 className="h-4 w-4 mr-1" />
                                  Remove
                                </Button>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>

              {/* Success Criteria & KPIs */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <CheckCircle className="h-5 w-5" />
                        <span>Success Criteria</span>
                      </div>
                      {!isReadOnly && (
                        <Button
                          type="button"
                          size="sm"
                          onClick={() => addArrayItem('successCriteria', { criteria: '', measurement: '', target: '' })}
                          className="bg-green-600 hover:bg-green-700"
                        >
                          <Plus className="h-4 w-4 mr-1" />
                          Add Criteria
                        </Button>
                      )}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {formData.successCriteria.length === 0 ? (
                      <div className="text-center py-6 text-gray-500">
                        <CheckCircle className="h-10 w-10 mx-auto mb-2 text-gray-300" />
                        <p className="text-sm">No success criteria defined</p>
                      </div>
                    ) : (
                      <div className="space-y-3">
                        {formData.successCriteria.map((criteria: any, index: number) => (
                          <div key={index} className="border rounded-lg p-3">
                            <div className="space-y-3">
                              <div className="space-y-2">
                                <Label>Success Criteria *</Label>
                                <Input
                                  value={criteria.criteria}
                                  onChange={(e) => updateArrayItem('successCriteria', index, { ...criteria, criteria: e.target.value })}
                                  placeholder="e.g., User Adoption Rate"
                                  readOnly={isReadOnly}
                                />
                              </div>
                              <div className="grid grid-cols-2 gap-3">
                                <div className="space-y-2">
                                  <Label>Target</Label>
                                  <Input
                                    value={criteria.target}
                                    onChange={(e) => updateArrayItem('successCriteria', index, { ...criteria, target: e.target.value })}
                                    placeholder="e.g., > 80%"
                                    readOnly={isReadOnly}
                                  />
                                </div>
                                <div className="space-y-2">
                                  <Label>Measurement</Label>
                                  <Input
                                    value={criteria.measurement}
                                    onChange={(e) => updateArrayItem('successCriteria', index, { ...criteria, measurement: e.target.value })}
                                    placeholder="e.g., Analytics"
                                    readOnly={isReadOnly}
                                  />
                                </div>
                              </div>
                              {!isReadOnly && (
                                <div className="flex justify-end">
                                  <Button
                                    type="button"
                                    size="sm"
                                    variant="outline"
                                    onClick={() => removeArrayItem('successCriteria', index)}
                                    className="text-red-600 hover:text-red-700"
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </div>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Award className="h-5 w-5" />
                        <span>Key Performance Indicators</span>
                      </div>
                      {!isReadOnly && (
                        <Button
                          type="button"
                          size="sm"
                          onClick={() => addArrayItem('kpis', { name: '', target: '', unit: '', priority: 'MEDIUM' })}
                          className="bg-green-600 hover:bg-green-700"
                        >
                          <Plus className="h-4 w-4 mr-1" />
                          Add KPI
                        </Button>
                      )}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {formData.kpis.length === 0 ? (
                      <div className="text-center py-6 text-gray-500">
                        <Award className="h-10 w-10 mx-auto mb-2 text-gray-300" />
                        <p className="text-sm">No KPIs defined</p>
                      </div>
                    ) : (
                      <div className="space-y-3">
                        {formData.kpis.map((kpi: any, index: number) => (
                          <div key={index} className="border rounded-lg p-3">
                            <div className="space-y-3">
                              <div className="space-y-2">
                                <Label>KPI Name *</Label>
                                <Input
                                  value={kpi.name}
                                  onChange={(e) => updateArrayItem('kpis', index, { ...kpi, name: e.target.value })}
                                  placeholder="e.g., Customer Satisfaction"
                                  readOnly={isReadOnly}
                                />
                              </div>
                              <div className="grid grid-cols-3 gap-3">
                                <div className="space-y-2">
                                  <Label>Target</Label>
                                  <Input
                                    value={kpi.target}
                                    onChange={(e) => updateArrayItem('kpis', index, { ...kpi, target: e.target.value })}
                                    placeholder="e.g., 4.5"
                                    readOnly={isReadOnly}
                                  />
                                </div>
                                <div className="space-y-2">
                                  <Label>Unit</Label>
                                  <Input
                                    value={kpi.unit}
                                    onChange={(e) => updateArrayItem('kpis', index, { ...kpi, unit: e.target.value })}
                                    placeholder="e.g., /5"
                                    readOnly={isReadOnly}
                                  />
                                </div>
                                <div className="space-y-2">
                                  <Label>Priority</Label>
                                  <select
                                    value={kpi.priority}
                                    onChange={(e) => updateArrayItem('kpis', index, { ...kpi, priority: e.target.value })}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                                    disabled={isReadOnly}
                                  >
                                    <option value="LOW">Low</option>
                                    <option value="MEDIUM">Medium</option>
                                    <option value="HIGH">High</option>
                                    <option value="CRITICAL">Critical</option>
                                  </select>
                                </div>
                              </div>
                              {!isReadOnly && (
                                <div className="flex justify-end">
                                  <Button
                                    type="button"
                                    size="sm"
                                    variant="outline"
                                    onClick={() => removeArrayItem('kpis', index)}
                                    className="text-red-600 hover:text-red-700"
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </div>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="legal" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Legal Framework */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Scale className="h-5 w-5" />
                      <span>Legal Framework</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="governingLaw">Governing Law</Label>
                        <Input
                          id="governingLaw"
                          value={formData.governingLaw}
                          onChange={(e) => handleInputChange('governingLaw', e.target.value)}
                          placeholder="e.g., Laws of California, USA"
                          readOnly={isReadOnly}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="disputeResolution">Dispute Resolution</Label>
                        <select
                          id="disputeResolution"
                          value={formData.disputeResolution}
                          onChange={(e) => handleInputChange('disputeResolution', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                          disabled={isReadOnly}
                        >
                          <option value="">Select dispute resolution method</option>
                          <option value="ARBITRATION">Arbitration</option>
                          <option value="MEDIATION">Mediation</option>
                          <option value="LITIGATION">Litigation</option>
                          <option value="NEGOTIATION">Negotiation</option>
                        </select>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="securityLevel">Security Level *</Label>
                        <select
                          id="securityLevel"
                          value={formData.securityLevel}
                          onChange={(e) => handleInputChange('securityLevel', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                          required
                          disabled={isReadOnly}
                        >
                          <option value="PUBLIC">Public</option>
                          <option value="INTERNAL">Internal</option>
                          <option value="CONFIDENTIAL">Confidential</option>
                          <option value="RESTRICTED">Restricted</option>
                          <option value="TOP_SECRET">Top Secret</option>
                        </select>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="confidentialityLevel">Confidentiality Level *</Label>
                        <select
                          id="confidentialityLevel"
                          value={formData.confidentialityLevel}
                          onChange={(e) => handleInputChange('confidentialityLevel', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                          required
                          disabled={isReadOnly}
                        >
                          <option value="STANDARD">Standard</option>
                          <option value="CONFIDENTIAL">Confidential</option>
                          <option value="HIGHLY_CONFIDENTIAL">Highly Confidential</option>
                          <option value="TOP_SECRET">Top Secret</option>
                        </select>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Intellectual Property & Rights */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Shield className="h-5 w-5" />
                      <span>Intellectual Property</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="ipRights">IP Rights & Ownership</Label>
                      <Textarea
                        id="ipRights"
                        value={formData.ipRights}
                        onChange={(e) => handleInputChange('ipRights', e.target.value)}
                        placeholder="Define intellectual property rights and ownership..."
                        rows={4}
                        readOnly={isReadOnly}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="penaltyClause">Penalty Clause</Label>
                      <Textarea
                        id="penaltyClause"
                        value={formData.penaltyClause}
                        onChange={(e) => handleInputChange('penaltyClause', e.target.value)}
                        placeholder="Define penalties for contract breaches..."
                        rows={3}
                        readOnly={isReadOnly}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="bonusClause">Bonus Clause</Label>
                      <Textarea
                        id="bonusClause"
                        value={formData.bonusClause}
                        onChange={(e) => handleInputChange('bonusClause', e.target.value)}
                        placeholder="Define bonus conditions for exceptional performance..."
                        rows={3}
                        readOnly={isReadOnly}
                      />
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Compliance Requirements */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <AlertTriangle className="h-5 w-5" />
                      <span>Compliance Requirements</span>
                    </div>
                    {!isReadOnly && (
                      <Button
                        type="button"
                        size="sm"
                        onClick={() => addArrayItem('complianceRequirements', { standard: '', description: '', mandatory: true })}
                        className="bg-green-600 hover:bg-green-700"
                      >
                        <Plus className="h-4 w-4 mr-1" />
                        Add Compliance
                      </Button>
                    )}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {formData.complianceRequirements.length === 0 ? (
                    <div className="text-center py-8 text-gray-500">
                      <AlertTriangle className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                      <p>No compliance requirements defined</p>
                      <p className="text-sm">Add regulatory and compliance standards</p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {formData.complianceRequirements.map((compliance: any, index: number) => (
                        <div key={index} className="border rounded-lg p-4">
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <Label>Compliance Standard *</Label>
                              <Input
                                value={compliance.standard}
                                onChange={(e) => updateArrayItem('complianceRequirements', index, { ...compliance, standard: e.target.value })}
                                placeholder="e.g., GDPR, HIPAA, SOX"
                                readOnly={isReadOnly}
                              />
                            </div>
                            <div className="space-y-2">
                              <Label>Mandatory</Label>
                              <select
                                value={compliance.mandatory ? 'true' : 'false'}
                                onChange={(e) => updateArrayItem('complianceRequirements', index, { ...compliance, mandatory: e.target.value === 'true' })}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                                disabled={isReadOnly}
                              >
                                <option value="true">Yes</option>
                                <option value="false">No</option>
                              </select>
                            </div>
                          </div>
                          <div className="mt-3 space-y-2">
                            <Label>Description</Label>
                            <Textarea
                              value={compliance.description}
                              onChange={(e) => updateArrayItem('complianceRequirements', index, { ...compliance, description: e.target.value })}
                              placeholder="Describe compliance requirements and implementation details"
                              rows={2}
                              readOnly={isReadOnly}
                            />
                          </div>
                          {!isReadOnly && (
                            <div className="mt-3 flex justify-end">
                              <Button
                                type="button"
                                size="sm"
                                variant="outline"
                                onClick={() => removeArrayItem('complianceRequirements', index)}
                                className="text-red-600 hover:text-red-700"
                              >
                                <Trash2 className="h-4 w-4 mr-1" />
                                Remove
                              </Button>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Contract Terms & Conditions */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <FileText className="h-5 w-5" />
                    <span>Terms & Conditions</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="terms">Contract Terms & Conditions *</Label>
                    <Textarea
                      id="terms"
                      value={formData.terms}
                      onChange={(e) => handleInputChange('terms', e.target.value)}
                      placeholder="Enter detailed contract terms and conditions..."
                      rows={8}
                      required
                      readOnly={isReadOnly}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="terminationClause">Termination Clause</Label>
                      <Textarea
                        id="terminationClause"
                        value={formData.terminationClause}
                        onChange={(e) => handleInputChange('terminationClause', e.target.value)}
                        placeholder="Define contract termination conditions..."
                        rows={4}
                        readOnly={isReadOnly}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="renewalTerms">Renewal Terms</Label>
                      <Textarea
                        id="renewalTerms"
                        value={formData.renewalTerms}
                        onChange={(e) => handleInputChange('renewalTerms', e.target.value)}
                        placeholder="Define contract renewal conditions..."
                        rows={4}
                        readOnly={isReadOnly}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="management" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Stakeholder Management */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Users className="h-5 w-5" />
                        <span>Stakeholders</span>
                      </div>
                      {!isReadOnly && (
                        <Button
                          type="button"
                          size="sm"
                          onClick={() => addArrayItem('stakeholders', { name: '', role: '', email: '', phone: '', responsibility: '' })}
                          className="bg-green-600 hover:bg-green-700"
                        >
                          <Plus className="h-4 w-4 mr-1" />
                          Add Stakeholder
                        </Button>
                      )}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {formData.stakeholders.length === 0 ? (
                      <div className="text-center py-6 text-gray-500">
                        <Users className="h-10 w-10 mx-auto mb-2 text-gray-300" />
                        <p className="text-sm">No stakeholders defined</p>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {formData.stakeholders.map((stakeholder: any, index: number) => (
                          <div key={index} className="border rounded-lg p-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                              <div className="space-y-2">
                                <Label>Name *</Label>
                                <Input
                                  value={stakeholder.name}
                                  onChange={(e) => updateArrayItem('stakeholders', index, { ...stakeholder, name: e.target.value })}
                                  placeholder="Stakeholder name"
                                  readOnly={isReadOnly}
                                />
                              </div>
                              <div className="space-y-2">
                                <Label>Role</Label>
                                <Input
                                  value={stakeholder.role}
                                  onChange={(e) => updateArrayItem('stakeholders', index, { ...stakeholder, role: e.target.value })}
                                  placeholder="e.g., Project Sponsor"
                                  readOnly={isReadOnly}
                                />
                              </div>
                              <div className="space-y-2">
                                <Label>Email</Label>
                                <Input
                                  value={stakeholder.email}
                                  onChange={(e) => updateArrayItem('stakeholders', index, { ...stakeholder, email: e.target.value })}
                                  placeholder="<EMAIL>"
                                  readOnly={isReadOnly}
                                />
                              </div>
                              <div className="space-y-2">
                                <Label>Phone</Label>
                                <Input
                                  value={stakeholder.phone}
                                  onChange={(e) => updateArrayItem('stakeholders', index, { ...stakeholder, phone: e.target.value })}
                                  placeholder="+1234567890"
                                  readOnly={isReadOnly}
                                />
                              </div>
                            </div>
                            <div className="mt-3 space-y-2">
                              <Label>Responsibility</Label>
                              <Textarea
                                value={stakeholder.responsibility}
                                onChange={(e) => updateArrayItem('stakeholders', index, { ...stakeholder, responsibility: e.target.value })}
                                placeholder="Define stakeholder responsibilities"
                                rows={2}
                                readOnly={isReadOnly}
                              />
                            </div>
                            {!isReadOnly && (
                              <div className="mt-3 flex justify-end">
                                <Button
                                  type="button"
                                  size="sm"
                                  variant="outline"
                                  onClick={() => removeArrayItem('stakeholders', index)}
                                  className="text-red-600 hover:text-red-700"
                                >
                                  <Trash2 className="h-4 w-4 mr-1" />
                                  Remove
                                </Button>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Escalation Matrix */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <AlertTriangle className="h-5 w-5" />
                        <span>Escalation Matrix</span>
                      </div>
                      {!isReadOnly && (
                        <Button
                          type="button"
                          size="sm"
                          onClick={() => addArrayItem('escalationMatrix', { level: '', contact: '', timeframe: '', criteria: '' })}
                          className="bg-green-600 hover:bg-green-700"
                        >
                          <Plus className="h-4 w-4 mr-1" />
                          Add Level
                        </Button>
                      )}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {formData.escalationMatrix.length === 0 ? (
                      <div className="text-center py-6 text-gray-500">
                        <AlertTriangle className="h-10 w-10 mx-auto mb-2 text-gray-300" />
                        <p className="text-sm">No escalation levels defined</p>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {formData.escalationMatrix.map((escalation: any, index: number) => (
                          <div key={index} className="border rounded-lg p-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                              <div className="space-y-2">
                                <Label>Escalation Level *</Label>
                                <select
                                  value={escalation.level}
                                  onChange={(e) => updateArrayItem('escalationMatrix', index, { ...escalation, level: e.target.value })}
                                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                                  disabled={isReadOnly}
                                >
                                  <option value="">Select level</option>
                                  <option value="L1">Level 1 - Team Lead</option>
                                  <option value="L2">Level 2 - Project Manager</option>
                                  <option value="L3">Level 3 - Department Head</option>
                                  <option value="L4">Level 4 - Executive</option>
                                </select>
                              </div>
                              <div className="space-y-2">
                                <Label>Contact Person</Label>
                                <Input
                                  value={escalation.contact}
                                  onChange={(e) => updateArrayItem('escalationMatrix', index, { ...escalation, contact: e.target.value })}
                                  placeholder="Contact name and details"
                                  readOnly={isReadOnly}
                                />
                              </div>
                              <div className="space-y-2">
                                <Label>Response Timeframe</Label>
                                <Input
                                  value={escalation.timeframe}
                                  onChange={(e) => updateArrayItem('escalationMatrix', index, { ...escalation, timeframe: e.target.value })}
                                  placeholder="e.g., 4 hours"
                                  readOnly={isReadOnly}
                                />
                              </div>
                              <div className="space-y-2">
                                <Label>Escalation Criteria</Label>
                                <Input
                                  value={escalation.criteria}
                                  onChange={(e) => updateArrayItem('escalationMatrix', index, { ...escalation, criteria: e.target.value })}
                                  placeholder="When to escalate"
                                  readOnly={isReadOnly}
                                />
                              </div>
                            </div>
                            {!isReadOnly && (
                              <div className="mt-3 flex justify-end">
                                <Button
                                  type="button"
                                  size="sm"
                                  variant="outline"
                                  onClick={() => removeArrayItem('escalationMatrix', index)}
                                  className="text-red-600 hover:text-red-700"
                                >
                                  <Trash2 className="h-4 w-4 mr-1" />
                                  Remove
                                </Button>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>

              {/* Risk Assessment & Dependencies */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <AlertTriangle className="h-5 w-5" />
                        <span>Risk Assessment</span>
                      </div>
                      {!isReadOnly && (
                        <Button
                          type="button"
                          size="sm"
                          onClick={() => addArrayItem('riskAssessment', { risk: '', impact: '', probability: '', mitigation: '' })}
                          className="bg-green-600 hover:bg-green-700"
                        >
                          <Plus className="h-4 w-4 mr-1" />
                          Add Risk
                        </Button>
                      )}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {formData.riskAssessment.length === 0 ? (
                      <div className="text-center py-6 text-gray-500">
                        <AlertTriangle className="h-10 w-10 mx-auto mb-2 text-gray-300" />
                        <p className="text-sm">No risks identified</p>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {formData.riskAssessment.map((risk: any, index: number) => (
                          <div key={index} className="border rounded-lg p-4">
                            <div className="space-y-3">
                              <div className="space-y-2">
                                <Label>Risk Description *</Label>
                                <Input
                                  value={risk.risk}
                                  onChange={(e) => updateArrayItem('riskAssessment', index, { ...risk, risk: e.target.value })}
                                  placeholder="Describe the risk"
                                  readOnly={isReadOnly}
                                />
                              </div>
                              <div className="grid grid-cols-2 gap-3">
                                <div className="space-y-2">
                                  <Label>Impact</Label>
                                  <select
                                    value={risk.impact}
                                    onChange={(e) => updateArrayItem('riskAssessment', index, { ...risk, impact: e.target.value })}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                                    disabled={isReadOnly}
                                  >
                                    <option value="">Select impact</option>
                                    <option value="LOW">Low</option>
                                    <option value="MEDIUM">Medium</option>
                                    <option value="HIGH">High</option>
                                    <option value="CRITICAL">Critical</option>
                                  </select>
                                </div>
                                <div className="space-y-2">
                                  <Label>Probability</Label>
                                  <select
                                    value={risk.probability}
                                    onChange={(e) => updateArrayItem('riskAssessment', index, { ...risk, probability: e.target.value })}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                                    disabled={isReadOnly}
                                  >
                                    <option value="">Select probability</option>
                                    <option value="LOW">Low</option>
                                    <option value="MEDIUM">Medium</option>
                                    <option value="HIGH">High</option>
                                  </select>
                                </div>
                              </div>
                              <div className="space-y-2">
                                <Label>Mitigation Strategy</Label>
                                <Textarea
                                  value={risk.mitigation}
                                  onChange={(e) => updateArrayItem('riskAssessment', index, { ...risk, mitigation: e.target.value })}
                                  placeholder="How to mitigate this risk"
                                  rows={2}
                                  readOnly={isReadOnly}
                                />
                              </div>
                              {!isReadOnly && (
                                <div className="flex justify-end">
                                  <Button
                                    type="button"
                                    size="sm"
                                    variant="outline"
                                    onClick={() => removeArrayItem('riskAssessment', index)}
                                    className="text-red-600 hover:text-red-700"
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </div>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Building className="h-5 w-5" />
                        <span>Dependencies & Constraints</span>
                      </div>
                      {!isReadOnly && (
                        <Button
                          type="button"
                          size="sm"
                          onClick={() => addArrayItem('dependencies', { type: 'DEPENDENCY', description: '', impact: '', owner: '' })}
                          className="bg-green-600 hover:bg-green-700"
                        >
                          <Plus className="h-4 w-4 mr-1" />
                          Add Item
                        </Button>
                      )}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {formData.dependencies.length === 0 ? (
                      <div className="text-center py-6 text-gray-500">
                        <Building className="h-10 w-10 mx-auto mb-2 text-gray-300" />
                        <p className="text-sm">No dependencies or constraints defined</p>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {formData.dependencies.map((item: any, index: number) => (
                          <div key={index} className="border rounded-lg p-4">
                            <div className="space-y-3">
                              <div className="grid grid-cols-2 gap-3">
                                <div className="space-y-2">
                                  <Label>Type</Label>
                                  <select
                                    value={item.type}
                                    onChange={(e) => updateArrayItem('dependencies', index, { ...item, type: e.target.value })}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                                    disabled={isReadOnly}
                                  >
                                    <option value="DEPENDENCY">Dependency</option>
                                    <option value="CONSTRAINT">Constraint</option>
                                    <option value="ASSUMPTION">Assumption</option>
                                    <option value="EXCLUSION">Exclusion</option>
                                  </select>
                                </div>
                                <div className="space-y-2">
                                  <Label>Impact</Label>
                                  <select
                                    value={item.impact}
                                    onChange={(e) => updateArrayItem('dependencies', index, { ...item, impact: e.target.value })}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                                    disabled={isReadOnly}
                                  >
                                    <option value="">Select impact</option>
                                    <option value="LOW">Low</option>
                                    <option value="MEDIUM">Medium</option>
                                    <option value="HIGH">High</option>
                                    <option value="CRITICAL">Critical</option>
                                  </select>
                                </div>
                              </div>
                              <div className="space-y-2">
                                <Label>Description *</Label>
                                <Textarea
                                  value={item.description}
                                  onChange={(e) => updateArrayItem('dependencies', index, { ...item, description: e.target.value })}
                                  placeholder="Describe the dependency, constraint, assumption, or exclusion"
                                  rows={2}
                                  readOnly={isReadOnly}
                                />
                              </div>
                              <div className="space-y-2">
                                <Label>Owner/Responsible Party</Label>
                                <Input
                                  value={item.owner}
                                  onChange={(e) => updateArrayItem('dependencies', index, { ...item, owner: e.target.value })}
                                  placeholder="Who is responsible for managing this"
                                  readOnly={isReadOnly}
                                />
                              </div>
                              {!isReadOnly && (
                                <div className="flex justify-end">
                                  <Button
                                    type="button"
                                    size="sm"
                                    variant="outline"
                                    onClick={() => removeArrayItem('dependencies', index)}
                                    className="text-red-600 hover:text-red-700"
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </div>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>

              {/* Contingency Plan */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Shield className="h-5 w-5" />
                    <span>Contingency Plan</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <Label htmlFor="contingencyPlan">Contingency Plan</Label>
                    <Textarea
                      id="contingencyPlan"
                      value={formData.contingencyPlan}
                      onChange={(e) => handleInputChange('contingencyPlan', e.target.value)}
                      placeholder="Define contingency plans for major risks and issues..."
                      rows={6}
                      readOnly={isReadOnly}
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          {/* Form Actions */}
          <div className="flex justify-between items-center pt-6 border-t">
            <div className="flex items-center space-x-4">
              {resourcePlan.length > 0 && selectedSkills.length > 0 && (
                <Badge className="bg-green-100 text-green-800">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Ready for Auto-Generation
                </Badge>
              )}
              {formData.autoGenerateProject && (
                <Badge className="bg-blue-100 text-blue-800">
                  <Zap className="h-3 w-3 mr-1" />
                  Will Generate Project
                </Badge>
              )}
            </div>

            <div className="flex space-x-3">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={isLoading}
              >
                {mode === 'view' ? 'Close' : 'Cancel'}
              </Button>
              {mode !== 'view' && (
                <Button
                  type="submit"
                  className="bg-red-600 hover:bg-red-700"
                  disabled={isLoading}
                >
                  {isLoading
                    ? (mode === 'create' ? 'Creating...' : 'Updating...')
                    : (mode === 'create' ? 'Create Contract' : 'Update Contract')
                  }
                  {formData.autoGenerateProject && mode === 'create' && (
                    <span className="ml-2">& Generate Project</span>
                  )}
                </Button>
              )}
            </div>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
