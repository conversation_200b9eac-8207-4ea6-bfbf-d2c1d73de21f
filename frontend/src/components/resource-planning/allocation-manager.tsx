'use client';

import { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { resourcePlansApi } from '@/lib/api';
import { Edit, Trash2, Calendar, Percent, User } from 'lucide-react';
import toast from 'react-hot-toast';

interface AllocationManagerProps {
  isOpen: boolean;
  onClose: () => void;
  allocation: any;
  resourcePlan: any;
}

export function AllocationManager({
  isOpen,
  onClose,
  allocation,
  resourcePlan,
}: AllocationManagerProps) {
  const queryClient = useQueryClient();
  const [mode, setMode] = useState<'view' | 'edit'>('view');
  const [formData, setFormData] = useState({
    allocationPercent: allocation?.allocationPercent || 100,
    startDate: allocation?.startDate ? new Date(allocation.startDate).toISOString().split('T')[0] : '',
    endDate: allocation?.endDate ? new Date(allocation.endDate).toISOString().split('T')[0] : '',
  });

  const updateMutation = useMutation({
    mutationFn: (data: any) => 
      resourcePlansApi.updateAllocation(resourcePlan.id, allocation.id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['resource-plans'] });
      queryClient.invalidateQueries({ queryKey: ['project', resourcePlan.projectId] });
      toast.success('Allocation updated successfully');
      setMode('view');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to update allocation');
    },
  });

  const removeMutation = useMutation({
    mutationFn: () => 
      resourcePlansApi.removeAllocation(resourcePlan.id, allocation.id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['resource-plans'] });
      queryClient.invalidateQueries({ queryKey: ['project', resourcePlan.projectId] });
      toast.success('Resource deallocated successfully');
      onClose();
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to deallocate resource');
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const updateData: any = {};
    
    if (formData.allocationPercent !== allocation.allocationPercent) {
      updateData.allocationPercent = Number(formData.allocationPercent);
    }
    
    if (formData.startDate && formData.startDate !== new Date(allocation.startDate).toISOString().split('T')[0]) {
      updateData.startDate = new Date(formData.startDate).toISOString();
    }
    
    if (formData.endDate && formData.endDate !== (allocation.endDate ? new Date(allocation.endDate).toISOString().split('T')[0] : '')) {
      updateData.endDate = new Date(formData.endDate).toISOString();
    }

    if (Object.keys(updateData).length === 0) {
      toast.error('No changes to save');
      return;
    }

    updateMutation.mutate(updateData);
  };

  const handleRemove = () => {
    if (confirm('Are you sure you want to deallocate this resource? This action cannot be undone.')) {
      removeMutation.mutate();
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  if (!allocation) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <User className="h-5 w-5" />
            <span>Manage Resource Allocation</span>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Resource Info */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-medium text-lg">
                {allocation.resource.user.firstName} {allocation.resource.user.lastName}
              </h3>
              <Badge variant="outline">
                {allocation.resource.designation}
              </Badge>
            </div>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Email:</span>
                <p className="font-medium">{allocation.resource.user.email}</p>
              </div>
              <div>
                <span className="text-gray-600">Department:</span>
                <p className="font-medium">{allocation.resource.department}</p>
              </div>
            </div>
          </div>

          {/* Allocation Details */}
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Allocation Percentage */}
              <div className="space-y-2">
                <Label htmlFor="allocationPercent" className="flex items-center space-x-1">
                  <Percent className="h-4 w-4" />
                  <span>Allocation %</span>
                </Label>
                {mode === 'edit' ? (
                  <Input
                    id="allocationPercent"
                    type="number"
                    min="0"
                    max="100"
                    value={formData.allocationPercent}
                    onChange={(e) => handleInputChange('allocationPercent', e.target.value)}
                    required
                  />
                ) : (
                  <div className="p-2 bg-gray-100 rounded border">
                    {allocation.allocationPercent}%
                  </div>
                )}
              </div>

              {/* Start Date */}
              <div className="space-y-2">
                <Label htmlFor="startDate" className="flex items-center space-x-1">
                  <Calendar className="h-4 w-4" />
                  <span>Start Date</span>
                </Label>
                {mode === 'edit' ? (
                  <Input
                    id="startDate"
                    type="date"
                    value={formData.startDate}
                    onChange={(e) => handleInputChange('startDate', e.target.value)}
                    required
                  />
                ) : (
                  <div className="p-2 bg-gray-100 rounded border">
                    {new Date(allocation.startDate).toLocaleDateString()}
                  </div>
                )}
              </div>

              {/* End Date */}
              <div className="space-y-2">
                <Label htmlFor="endDate" className="flex items-center space-x-1">
                  <Calendar className="h-4 w-4" />
                  <span>End Date</span>
                </Label>
                {mode === 'edit' ? (
                  <Input
                    id="endDate"
                    type="date"
                    value={formData.endDate}
                    onChange={(e) => handleInputChange('endDate', e.target.value)}
                  />
                ) : (
                  <div className="p-2 bg-gray-100 rounded border">
                    {allocation.endDate 
                      ? new Date(allocation.endDate).toLocaleDateString()
                      : 'Not specified'
                    }
                  </div>
                )}
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center justify-between pt-4 border-t">
              <div className="flex space-x-2">
                {mode === 'view' ? (
                  <Button
                    type="button"
                    onClick={() => setMode('edit')}
                    variant="outline"
                    className="flex items-center space-x-2"
                  >
                    <Edit className="h-4 w-4" />
                    <span>Edit Allocation</span>
                  </Button>
                ) : (
                  <div className="flex space-x-2">
                    <Button
                      type="submit"
                      disabled={updateMutation.isPending}
                      className="bg-red-600 hover:bg-red-700"
                    >
                      {updateMutation.isPending ? 'Saving...' : 'Save Changes'}
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setMode('view')}
                    >
                      Cancel
                    </Button>
                  </div>
                )}
              </div>

              <Button
                type="button"
                variant="destructive"
                onClick={handleRemove}
                disabled={removeMutation.isPending}
                className="flex items-center space-x-2"
              >
                <Trash2 className="h-4 w-4" />
                <span>{removeMutation.isPending ? 'Removing...' : 'Deallocate'}</span>
              </Button>
            </div>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  );
}
