'use client';

import { useState, useEffect } from 'react';
import { useMutation, useQueryClient, useQuery } from '@tanstack/react-query';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { tasksApi, timesheetsApi } from '@/lib/api';
import { toast } from 'sonner';
import { useAuth } from '@/hooks/useAuth';

interface QuickTaskModalProps {
  isOpen: boolean;
  onClose: () => void;
  projectId: string;
  resourceId: string;
  onTaskCreated?: () => void;
}

export function QuickTaskModal({
  isOpen,
  onClose,
  projectId,
  resourceId,
  onTaskCreated
}: QuickTaskModalProps) {
  const { user, hasRole } = useAuth();
  const queryClient = useQueryClient();
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    priority: 'MEDIUM',
    estimatedHours: '8',
  });

  // Get all resources to find the user ID for the selected resource
  const { data: resourcesData } = useQuery({
    queryKey: ['all-resources'],
    queryFn: () => timesheetsApi.getAllResources(),
    enabled: !!resourceId && hasRole(['ADMIN']),
  });

  // Find the user ID for the resource
  const targetUserId = hasRole(['ADMIN']) && resourceId
    ? resourcesData?.data?.find((r: any) => r.id === resourceId)?.userId
    : user?.id;

  const createTaskMutation = useMutation({
    mutationFn: (data: any) => {
      console.log('Creating task with data:', data);
      console.log('User role:', user?.role);
      return tasksApi.create(data);
    },
    onSuccess: () => {
      toast.success('Task created successfully');
      queryClient.invalidateQueries({ queryKey: ['my-tasks'] });
      queryClient.invalidateQueries({ queryKey: ['resource-tasks'] });
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
      onTaskCreated?.();
      handleClose();
    },
    onError: (error: any) => {
      console.error('Task creation error:', error);
      toast.error(error.response?.data?.message || 'Failed to create task');
    },
  });

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleClose = () => {
    setFormData({
      title: '',
      description: '',
      priority: 'MEDIUM',
      estimatedHours: '8',
    });
    onClose();
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.title.trim() || !formData.description.trim()) {
      toast.error('Title and description are required');
      return;
    }

    if (!targetUserId) {
      toast.error('Unable to determine target user for task assignment');
      return;
    }

    const submitData = {
      title: formData.title.trim(),
      description: formData.description.trim(),
      projectId,
      assignedToId: targetUserId,
      priority: formData.priority,
      estimatedHours: parseFloat(formData.estimatedHours),
      startDate: new Date().toISOString(),
      dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 1 week from now
    };

    createTaskMutation.mutate(submitData);
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  // Check if user can create tasks
  const canCreateTasks = hasRole(['ADMIN', 'PROJECT_MANAGER']);

  if (!canCreateTasks) {
    return (
      <Dialog open={isOpen} onOpenChange={handleClose}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Create Quick Task</DialogTitle>
          </DialogHeader>
          <div className="p-4 text-center">
            <p className="text-gray-600 mb-4">
              Only administrators and project managers can create tasks.
            </p>
            <Button onClick={handleClose} variant="outline">
              Close
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Create Quick Task</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Task Title *
            </label>
            <Input
              value={formData.title}
              onChange={(e) => handleInputChange('title', e.target.value)}
              placeholder="Enter task title"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Description *
            </label>
            <Textarea
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Enter task description"
              rows={3}
              required
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Priority
              </label>
              <select
                value={formData.priority}
                onChange={(e) => handleInputChange('priority', e.target.value)}
                className="redwood-input w-full"
              >
                <option value="LOW">Low</option>
                <option value="MEDIUM">Medium</option>
                <option value="HIGH">High</option>
                <option value="CRITICAL">Critical</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Estimated Hours
              </label>
              <Input
                type="number"
                step="0.5"
                min="0.5"
                max="40"
                value={formData.estimatedHours}
                onChange={(e) => handleInputChange('estimatedHours', e.target.value)}
                placeholder="8"
              />
            </div>
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={createTaskMutation.isPending}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="redwood-button-primary"
              disabled={createTaskMutation.isPending}
            >
              {createTaskMutation.isPending ? 'Creating...' : 'Create Task'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
