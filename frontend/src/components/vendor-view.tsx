'use client';

import { <PERSON><PERSON>, <PERSON>alogContent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { formatDate } from '@/lib/utils';
import { 
  Building, 
  Mail, 
  Phone, 
  MapPin, 
  CreditCard, 
  FileText, 
  Award, 
  Star,
  Calendar,
  Hash,
  Banknote,
  Shield,
  X
} from 'lucide-react';

interface VendorViewProps {
  isOpen: boolean;
  onClose: () => void;
  vendor: any;
  onEdit?: () => void;
}

export function VendorView({ 
  isOpen, 
  onClose, 
  vendor,
  onEdit 
}: VendorViewProps) {
  if (!vendor) return null;

  const bankDetails = vendor.bankDetails ? JSON.parse(vendor.bankDetails) : {};

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE': return 'bg-green-100 text-green-800';
      case 'INACTIVE': return 'bg-yellow-100 text-yellow-800';
      case 'BLACKLISTED': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${i < Math.floor(rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
      />
    ));
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[95vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="flex items-center space-x-2">
              <Building className="h-5 w-5" />
              <span>{vendor.name}</span>
            </DialogTitle>
            <div className="flex items-center space-x-2">
              {onEdit && (
                <Button onClick={onEdit} variant="outline" size="sm">
                  Edit Vendor
                </Button>
              )}
              <Button onClick={onClose} variant="ghost" size="sm">
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </DialogHeader>

        <div className="space-y-6">
          {/* Header Info */}
          <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center space-x-4">
              <div className="w-16 h-16 bg-red-100 rounded-lg flex items-center justify-center">
                <Building className="h-8 w-8 text-red-600" />
              </div>
              <div>
                <h2 className="text-xl font-semibold text-gray-900">{vendor.name}</h2>
                <p className="text-gray-600">{vendor.contactPerson}</p>
                <div className="flex items-center space-x-2 mt-1">
                  <Badge className={getStatusColor(vendor.status)}>
                    {vendor.status}
                  </Badge>
                  {vendor.rating && (
                    <div className="flex items-center space-x-1">
                      {renderStars(vendor.rating)}
                      <span className="text-sm text-gray-600">({vendor.rating})</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
            <div className="text-right">
              <p className="text-sm text-gray-600">Onboarded</p>
              <p className="font-medium">{vendor.onboardedAt ? formatDate(vendor.onboardedAt) : 'N/A'}</p>
            </div>
          </div>

          <Tabs defaultValue="basic" className="w-full">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="basic">Basic Info</TabsTrigger>
              <TabsTrigger value="company">Company</TabsTrigger>
              <TabsTrigger value="banking">Banking</TabsTrigger>
              <TabsTrigger value="compliance">Compliance</TabsTrigger>
              <TabsTrigger value="performance">Performance</TabsTrigger>
            </TabsList>

            {/* Basic Information */}
            <TabsContent value="basic" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Mail className="h-5 w-5" />
                      <span>Contact Information</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center space-x-3">
                      <Mail className="h-4 w-4 text-gray-400" />
                      <div>
                        <p className="text-sm text-gray-600">Email</p>
                        <p className="font-medium">{vendor.email}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Phone className="h-4 w-4 text-gray-400" />
                      <div>
                        <p className="text-sm text-gray-600">Primary Phone</p>
                        <p className="font-medium">{vendor.phone}</p>
                      </div>
                    </div>
                    {vendor.alternatePhone && (
                      <div className="flex items-center space-x-3">
                        <Phone className="h-4 w-4 text-gray-400" />
                        <div>
                          <p className="text-sm text-gray-600">Alternate Phone</p>
                          <p className="font-medium">{vendor.alternatePhone}</p>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <MapPin className="h-5 w-5" />
                      <span>Address</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <p className="font-medium">{vendor.address}</p>
                      <div className="text-sm text-gray-600">
                        {vendor.city && <span>{vendor.city}, </span>}
                        {vendor.state && <span>{vendor.state} </span>}
                        {vendor.pincode && <span>- {vendor.pincode}</span>}
                      </div>
                      {vendor.country && (
                        <p className="text-sm text-gray-600">{vendor.country}</p>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* Company Information */}
            <TabsContent value="company" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <FileText className="h-5 w-5" />
                      <span>Company Details</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {vendor.companyType && (
                      <div>
                        <p className="text-sm text-gray-600">Company Type</p>
                        <p className="font-medium">{vendor.companyType.replace('_', ' ')}</p>
                      </div>
                    )}
                    {vendor.incorporationDate && (
                      <div>
                        <p className="text-sm text-gray-600">Incorporation Date</p>
                        <p className="font-medium">{formatDate(vendor.incorporationDate)}</p>
                      </div>
                    )}
                    {vendor.registrationNumber && (
                      <div>
                        <p className="text-sm text-gray-600">Registration Number</p>
                        <p className="font-medium">{vendor.registrationNumber}</p>
                      </div>
                    )}
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Hash className="h-5 w-5" />
                      <span>Tax Information</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <p className="text-sm text-gray-600">PAN Number</p>
                      <p className="font-medium font-mono">{vendor.panNumber}</p>
                    </div>
                    {vendor.gstNumber && (
                      <div>
                        <p className="text-sm text-gray-600">GST Number</p>
                        <p className="font-medium font-mono">{vendor.gstNumber}</p>
                      </div>
                    )}
                    {vendor.tanNumber && (
                      <div>
                        <p className="text-sm text-gray-600">TAN Number</p>
                        <p className="font-medium font-mono">{vendor.tanNumber}</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* Banking Information */}
            <TabsContent value="banking" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <CreditCard className="h-5 w-5" />
                    <span>Banking Details</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <div>
                        <p className="text-sm text-gray-600">Bank Name</p>
                        <p className="font-medium">{bankDetails.bankName || 'Not specified'}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Account Number</p>
                        <p className="font-medium font-mono">
                          {bankDetails.accountNumber ? 
                            `****${bankDetails.accountNumber.slice(-4)}` : 
                            'Not specified'
                          }
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">IFSC Code</p>
                        <p className="font-medium font-mono">{bankDetails.ifscCode || 'Not specified'}</p>
                      </div>
                    </div>
                    <div className="space-y-4">
                      <div>
                        <p className="text-sm text-gray-600">Account Type</p>
                        <p className="font-medium">{bankDetails.accountType || 'Not specified'}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Branch</p>
                        <p className="font-medium">{bankDetails.branch || 'Not specified'}</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Compliance */}
            <TabsContent value="compliance" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Award className="h-5 w-5" />
                      <span>Certifications</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span>MSME Certificate</span>
                      <Badge className={vendor.msmeCertificate ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}>
                        {vendor.msmeCertificate ? 'Yes' : 'No'}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>ISO 27001</span>
                      <Badge className={vendor.iso27001 ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}>
                        {vendor.iso27001 ? 'Certified' : 'Not Certified'}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>ISO 9001</span>
                      <Badge className={vendor.iso9001 ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}>
                        {vendor.iso9001 ? 'Certified' : 'Not Certified'}
                      </Badge>
                    </div>
                    {vendor.cmmiLevel && (
                      <div className="flex items-center justify-between">
                        <span>CMMI Level</span>
                        <Badge className="bg-blue-100 text-blue-800">
                          {vendor.cmmiLevel.replace('LEVEL_', 'Level ')}
                        </Badge>
                      </div>
                    )}
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Banknote className="h-5 w-5" />
                      <span>Commercial Terms</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {vendor.contractType && (
                      <div>
                        <p className="text-sm text-gray-600">Contract Type</p>
                        <p className="font-medium">{vendor.contractType}</p>
                      </div>
                    )}
                    <div>
                      <p className="text-sm text-gray-600">Payment Terms</p>
                      <p className="font-medium">{vendor.paymentTerms || 'NET30'}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Currency</p>
                      <p className="font-medium">{vendor.currency || 'INR'}</p>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* Performance */}
            <TabsContent value="performance" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <Card>
                  <CardContent className="p-6 text-center">
                    <div className="text-2xl font-bold text-blue-600">{vendor.totalResources || 0}</div>
                    <p className="text-sm text-gray-600">Total Resources</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-6 text-center">
                    <div className="text-2xl font-bold text-green-600">{vendor.activeContracts || 0}</div>
                    <p className="text-sm text-gray-600">Active Contracts</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-6 text-center">
                    <div className="flex items-center justify-center space-x-1">
                      {vendor.rating ? renderStars(vendor.rating) : <span className="text-gray-400">No rating</span>}
                    </div>
                    <p className="text-sm text-gray-600 mt-1">
                      {vendor.rating ? `${vendor.rating}/5` : 'Not rated'}
                    </p>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </DialogContent>
    </Dialog>
  );
}
