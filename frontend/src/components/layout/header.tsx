'use client';

import { useState, useRef, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Bell, Search, ExternalLink } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useAuth } from '@/hooks/useAuth';
import { searchApi } from '@/lib/api';
import { useQuery } from '@tanstack/react-query';

export function Header() {
  const { user } = useAuth();
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');
  const [showResults, setShowResults] = useState(false);
  const searchRef = useRef<HTMLDivElement>(null);

  // Debounced search
  const { data: searchResults, isLoading } = useQuery({
    queryKey: ['search', searchTerm],
    queryFn: () => searchApi.global(searchTerm, 3),
    enabled: searchTerm.length >= 2,
    staleTime: 30000, // 30 seconds
  });

  // Close search results when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setShowResults(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
    setShowResults(value.length >= 2);
  };

  const handleResultClick = (type: string, id: string) => {
    setShowResults(false);
    setSearchTerm('');

    // Navigate based on type
    switch (type) {
      case 'project':
        router.push(`/projects/${id}`);
        break;
      case 'task':
        router.push(`/tasks`); // Could be enhanced to show specific task
        break;
      case 'resource':
        router.push(`/resources`); // Could be enhanced to show specific resource
        break;
      case 'vendor':
        router.push(`/vendors`); // Could be enhanced to show specific vendor
        break;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'project': return 'bg-blue-100 text-blue-800';
      case 'task': return 'bg-green-100 text-green-800';
      case 'resource': return 'bg-purple-100 text-purple-800';
      case 'vendor': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <header className="h-16 border-b bg-background flex items-center justify-between px-6">
      {/* Search */}
      <div className="flex items-center space-x-4 flex-1 max-w-md">
        <div className="relative" ref={searchRef}>
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search projects, tasks, resources..."
            className="pl-10 w-80"
            value={searchTerm}
            onChange={(e) => handleSearchChange(e.target.value)}
            onFocus={() => searchTerm.length >= 2 && setShowResults(true)}
          />

          {/* Search Results Dropdown */}
          {showResults && searchTerm.length >= 2 && (
            <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-50 max-h-96 overflow-y-auto">
              {isLoading ? (
                <div className="p-4 text-center text-gray-500">Searching...</div>
              ) : searchResults?.data ? (
                <div className="py-2">
                  {/* Projects */}
                  {searchResults.data.projects.length > 0 && (
                    <div>
                      <div className="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide">Projects</div>
                      {searchResults.data.projects.map((item: any) => (
                        <button
                          key={`project-${item.id}`}
                          onClick={() => handleResultClick('project', item.id)}
                          className="w-full px-3 py-2 text-left hover:bg-gray-50 flex items-center justify-between"
                        >
                          <div>
                            <div className="font-medium text-sm">{item.title}</div>
                            <div className="text-xs text-gray-500">{item.subtitle}</div>
                          </div>
                          <span className={`px-2 py-1 text-xs rounded-full ${getTypeColor('project')}`}>
                            Project
                          </span>
                        </button>
                      ))}
                    </div>
                  )}

                  {/* Tasks */}
                  {searchResults.data.tasks.length > 0 && (
                    <div>
                      <div className="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide">Tasks</div>
                      {searchResults.data.tasks.map((item: any) => (
                        <button
                          key={`task-${item.id}`}
                          onClick={() => handleResultClick('task', item.id)}
                          className="w-full px-3 py-2 text-left hover:bg-gray-50 flex items-center justify-between"
                        >
                          <div>
                            <div className="font-medium text-sm">{item.title}</div>
                            <div className="text-xs text-gray-500">{item.subtitle}</div>
                          </div>
                          <span className={`px-2 py-1 text-xs rounded-full ${getTypeColor('task')}`}>
                            Task
                          </span>
                        </button>
                      ))}
                    </div>
                  )}

                  {/* Resources */}
                  {searchResults.data.resources.length > 0 && (
                    <div>
                      <div className="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide">Resources</div>
                      {searchResults.data.resources.map((item: any) => (
                        <button
                          key={`resource-${item.id}`}
                          onClick={() => handleResultClick('resource', item.id)}
                          className="w-full px-3 py-2 text-left hover:bg-gray-50 flex items-center justify-between"
                        >
                          <div>
                            <div className="font-medium text-sm">{item.title}</div>
                            <div className="text-xs text-gray-500">{item.subtitle}</div>
                          </div>
                          <span className={`px-2 py-1 text-xs rounded-full ${getTypeColor('resource')}`}>
                            Resource
                          </span>
                        </button>
                      ))}
                    </div>
                  )}

                  {/* Vendors */}
                  {searchResults.data.vendors.length > 0 && (
                    <div>
                      <div className="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide">Vendors</div>
                      {searchResults.data.vendors.map((item: any) => (
                        <button
                          key={`vendor-${item.id}`}
                          onClick={() => handleResultClick('vendor', item.id)}
                          className="w-full px-3 py-2 text-left hover:bg-gray-50 flex items-center justify-between"
                        >
                          <div>
                            <div className="font-medium text-sm">{item.title}</div>
                            <div className="text-xs text-gray-500">{item.subtitle}</div>
                          </div>
                          <span className={`px-2 py-1 text-xs rounded-full ${getTypeColor('vendor')}`}>
                            Vendor
                          </span>
                        </button>
                      ))}
                    </div>
                  )}

                  {searchResults.data.total === 0 && (
                    <div className="p-4 text-center text-gray-500">No results found</div>
                  )}
                </div>
              ) : (
                <div className="p-4 text-center text-gray-500">Start typing to search...</div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Right side */}
      <div className="flex items-center space-x-4">
        {/* Notifications */}
        <Button variant="ghost" size="icon" className="relative">
          <Bell className="h-5 w-5" />
          <span className="absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full text-xs"></span>
        </Button>

        {/* User info */}
        {user && (
          <div className="flex items-center space-x-3">
            <div className="text-right">
              <p className="text-sm font-medium">
                {user.firstName} {user.lastName}
              </p>
              <p className="text-xs text-muted-foreground">
                {user.role.replace('_', ' ').toLowerCase()}
              </p>
            </div>
            <div className="h-8 w-8 rounded-full bg-primary flex items-center justify-center text-primary-foreground text-sm font-medium">
              {user.firstName[0]}{user.lastName[0]}
            </div>
          </div>
        )}
      </div>
    </header>
  );
}
