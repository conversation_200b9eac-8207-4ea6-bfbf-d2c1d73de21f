'use client';

import { useQuery } from '@tanstack/react-query';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { resourcesApi } from '@/lib/api';
import {
  User,
  Mail,
  Phone,
  MapPin,
  Building,
  Calendar,
  DollarSign,
  Star,
  Briefcase,
  GraduationCap,
  Award,
  Languages,
  Shield
} from 'lucide-react';

interface ResourceViewProps {
  isOpen: boolean;
  onClose: () => void;
  resourceId: string;
}

export function ResourceView({ 
  isOpen, 
  onClose, 
  resourceId 
}: ResourceViewProps) {
  const { data: resourceData, isLoading } = useQuery({
    queryKey: ['resource', resourceId],
    queryFn: () => resourcesApi.getById(resourceId),
    enabled: isOpen && !!resourceId,
  });

  const resource = resourceData?.data;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'AVAILABLE': return 'bg-green-100 text-green-800';
      case 'ALLOCATED': return 'bg-blue-100 text-blue-800';
      case 'ON_LEAVE': return 'bg-yellow-100 text-yellow-800';
      case 'INACTIVE': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getEmploymentTypeColor = (type: string) => {
    switch (type) {
      case 'FULL_TIME': return 'bg-blue-100 text-blue-800';
      case 'PART_TIME': return 'bg-purple-100 text-purple-800';
      case 'CONTRACT': return 'bg-orange-100 text-orange-800';
      case 'FREELANCE': return 'bg-pink-100 text-pink-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  if (isLoading) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-4xl">
          <div className="flex items-center justify-center h-64">
            <div className="spinner"></div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  if (!resource) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-4xl">
          <div className="text-center py-8">
            <p className="text-gray-500">Resource not found</p>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <User className="h-5 w-5" />
            <span>Resource Details</span>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Header Section */}
          <div className="flex items-start space-x-6 p-6 bg-gray-50 rounded-lg">
            <div className="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center">
              <span className="text-2xl font-semibold text-red-600">
                {getInitials(resource.user.firstName, resource.user.lastName)}
              </span>
            </div>
            <div className="flex-1">
              <h2 className="text-2xl font-bold text-gray-900">
                {resource.user.firstName} {resource.user.lastName}
              </h2>
              <p className="text-lg text-gray-600 mb-2">{resource.designation}</p>
              <div className="flex items-center space-x-4">
                <Badge className={getStatusColor(resource.status)}>
                  {resource.status}
                </Badge>
                <Badge className={getEmploymentTypeColor(resource.employmentType)}>
                  {resource.employmentType.replace('_', ' ')}
                </Badge>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Contact Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Mail className="h-5 w-5" />
                  <span>Contact Information</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-3">
                  <Mail className="h-4 w-4 text-gray-400" />
                  <span>{resource.user.email}</span>
                </div>
                {resource.user.phone && (
                  <div className="flex items-center space-x-3">
                    <Phone className="h-4 w-4 text-gray-400" />
                    <span>{resource.user.phone}</span>
                  </div>
                )}
                {resource.location && (
                  <div className="flex items-center space-x-3">
                    <MapPin className="h-4 w-4 text-gray-400" />
                    <span>{resource.location}</span>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Professional Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Briefcase className="h-5 w-5" />
                  <span>Professional Details</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-3">
                  <Building className="h-4 w-4 text-gray-400" />
                  <span>{resource.department}</span>
                </div>
                {resource.employeeId && (
                  <div className="flex items-center space-x-3">
                    <User className="h-4 w-4 text-gray-400" />
                    <span>ID: {resource.employeeId}</span>
                  </div>
                )}
                {resource.hourlyRate && (
                  <div className="flex items-center space-x-3">
                    <DollarSign className="h-4 w-4 text-gray-400" />
                    <span>${resource.hourlyRate}/hour</span>
                  </div>
                )}
                <div className="flex items-center space-x-3">
                  <Calendar className="h-4 w-4 text-gray-400" />
                  <span>Joined: {new Date(resource.createdAt).toLocaleDateString()}</span>
                </div>
              </CardContent>
            </Card>

            {/* Education */}
            {resource.educations && resource.educations.length > 0 && (
              <Card className="md:col-span-2">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <GraduationCap className="h-5 w-5" />
                    <span>Education</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {resource.educations.map((education: any) => (
                      <div key={education.id} className="p-4 bg-gray-50 rounded-lg">
                        <div className="font-medium text-lg">{education.degree}</div>
                        <div className="text-gray-600">{education.fieldOfStudy}</div>
                        <div className="text-sm text-gray-500 mt-2">
                          {education.institution} • {education.university}
                        </div>
                        <div className="text-sm text-gray-500">
                          {education.startYear} - {education.endYear}
                          {education.percentage && ` • ${education.percentage}%`}
                          {education.cgpa && ` • CGPA: ${education.cgpa}`}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Certifications */}
            {resource.certifications && resource.certifications.length > 0 && (
              <Card className="md:col-span-2">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Award className="h-5 w-5" />
                    <span>Certifications</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {resource.certifications.map((cert: any) => (
                      <div key={cert.id} className="p-4 bg-gray-50 rounded-lg">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="font-medium text-lg">{cert.name}</div>
                            <div className="text-gray-600">{cert.issuingOrg}</div>
                            <div className="text-sm text-gray-500 mt-2">
                              Issued: {new Date(cert.issueDate).toLocaleDateString()}
                              {cert.expiryDate && ` • Expires: ${new Date(cert.expiryDate).toLocaleDateString()}`}
                            </div>
                            {cert.credentialId && (
                              <div className="text-sm text-gray-500">
                                Credential ID: {cert.credentialId}
                              </div>
                            )}
                          </div>
                          {cert.verified && (
                            <Badge className="bg-green-100 text-green-800">Verified</Badge>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Languages */}
            {resource.languages && resource.languages.length > 0 && (
              <Card className="md:col-span-2">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Languages className="h-5 w-5" />
                    <span>Language Proficiency</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {resource.languages.map((lang: any) => (
                      <div key={lang.id} className="p-4 bg-gray-50 rounded-lg">
                        <div className="font-medium text-lg mb-2">{lang.language}</div>
                        <div className="space-y-1 text-sm">
                          <div className="flex justify-between">
                            <span>Speaking:</span>
                            <Badge variant="outline">{lang.speaking}</Badge>
                          </div>
                          <div className="flex justify-between">
                            <span>Reading:</span>
                            <Badge variant="outline">{lang.reading}</Badge>
                          </div>
                          <div className="flex justify-between">
                            <span>Writing:</span>
                            <Badge variant="outline">{lang.writing}</Badge>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Skills */}
            {resource.skills && resource.skills.length > 0 && (
              <Card className="md:col-span-2">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Star className="h-5 w-5" />
                    <span>Skills & Expertise</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {resource.skills.map((skill: any) => (
                      <div key={skill.id} className="p-3 bg-gray-50 rounded-lg">
                        <div className="font-medium text-sm">{skill.name}</div>
                        <div className="text-xs text-gray-600">{skill.category}</div>
                        <div className="text-xs text-gray-500 mt-1">
                          Level: {skill.proficiencyLevel}/5 • {skill.yearsOfExperience} years
                          {skill.certified && <span className="text-green-600"> • Certified</span>}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Security & Identity */}
            <Card className="md:col-span-2">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Shield className="h-5 w-5" />
                  <span>Security & Identity Information</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {resource.panNumber && (
                    <div>
                      <div className="text-sm text-gray-600">PAN Number</div>
                      <div className="font-medium">{resource.panNumber}</div>
                    </div>
                  )}
                  {resource.aadharNumber && (
                    <div>
                      <div className="text-sm text-gray-600">Aadhar Number</div>
                      <div className="font-medium">{resource.aadharNumber}</div>
                    </div>
                  )}
                  {resource.passportNumber && (
                    <div>
                      <div className="text-sm text-gray-600">Passport Number</div>
                      <div className="font-medium">{resource.passportNumber}</div>
                    </div>
                  )}
                  {resource.securityClearance && (
                    <div>
                      <div className="text-sm text-gray-600">Security Clearance</div>
                      <div className="font-medium">{resource.securityClearance}</div>
                    </div>
                  )}
                  {resource.entryPass && (
                    <div>
                      <div className="text-sm text-gray-600">Entry Pass</div>
                      <div className="font-medium">{resource.entryPass}</div>
                    </div>
                  )}
                  <div>
                    <div className="text-sm text-gray-600">Background Check</div>
                    <Badge className={resource.backgroundCheck ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                      {resource.backgroundCheck ? 'Completed' : 'Pending'}
                    </Badge>
                  </div>
                  <div>
                    <div className="text-sm text-gray-600">Documents Verified</div>
                    <Badge className={resource.documentsVerified ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                      {resource.documentsVerified ? 'Verified' : 'Pending'}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Vendor Information */}
            {resource.vendor && (
              <Card className="md:col-span-2">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Building className="h-5 w-5" />
                    <span>Vendor Information</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <div className="text-sm text-gray-600">Vendor Name</div>
                      <div className="font-medium">{resource.vendor.name}</div>
                    </div>
                    {resource.vendor.contactPerson && (
                      <div>
                        <div className="text-sm text-gray-600">Contact Person</div>
                        <div className="font-medium">{resource.vendor.contactPerson}</div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
