'use client';

import { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { resourcePlansApi, resourceRequestsApi } from '@/lib/api';
import { useAuth } from '@/hooks/useAuth';
import {
  Search,
  User,
  Star,
  Clock,
  DollarSign,
  CheckCircle,
  AlertCircle,
  UserPlus,
  FileText
} from 'lucide-react';
import toast from 'react-hot-toast';

interface ResourceMatcherProps {
  isOpen: boolean;
  onClose: () => void;
  resourcePlan: any;
}

interface ResourceMatch {
  resourceId: string;
  resource: any;
  matchScore: number;
  availability: number;
  skillMatch: number;
  experienceMatch: number;
  budgetMatch: number;
  availabilityDetails: {
    currentAllocations: number;
    availableCapacity: number;
    conflictingProjects: string[];
  };
}

export function ResourceMatcher({ isOpen, onClose, resourcePlan }: ResourceMatcherProps) {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const [matches, setMatches] = useState<ResourceMatch[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [searchPerformed, setSearchPerformed] = useState(false);
  const [selectedResource, setSelectedResource] = useState<string | null>(null);

  const searchMutation = useMutation({
    mutationFn: (data: any) => resourcePlansApi.matchResources(resourcePlan.id, data),
    onSuccess: (response) => {
      setMatches(response.data.matches || []);
      setSearchPerformed(true);
      toast.success(`Found ${response.data.matches?.length || 0} matching resources`);
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to find matching resources');
    },
    onSettled: () => {
      setIsSearching(false);
    },
  });

  const allocateMutation = useMutation({
    mutationFn: (data: any) => resourcePlansApi.allocateResource(resourcePlan.id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['resource-plans'] });
      queryClient.invalidateQueries({ queryKey: ['project', resourcePlan.projectId] });
      toast.success('Resource allocated successfully');
      onClose();
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to allocate resource');
    },
  });

  const createRequestMutation = useMutation({
    mutationFn: (data: any) => resourceRequestsApi.create(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['resource-requests'] });
      toast.success('Resource request created successfully');
      onClose();
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to create resource request');
    },
  });

  const handleSearch = () => {
    setIsSearching(true);
    searchMutation.mutate({ limit: 20 });
  };

  const handleAllocateResource = (resourceId: string) => {
    setSelectedResource(resourceId);
    allocateMutation.mutate({
      resourceId,
      allocationPercent: resourcePlan.allocationPercent,
      startDate: resourcePlan.startDate,
      endDate: resourcePlan.endDate,
    });
  };

  const handleCreateRequest = () => {
    const requestData = {
      resourcePlanId: resourcePlan.id,
      title: `Hiring Request: ${resourcePlan.role} for ${resourcePlan.project?.name}`,
      description: `Need to hire ${resourcePlan.role} with ${resourcePlan.skill?.name} skills. No suitable internal resources found.`,
      jobDescription: `JOB DESCRIPTION

Position: ${resourcePlan.role}
Project: ${resourcePlan.project?.name}
Department: ${resourcePlan.project?.department || 'Technology'}

REQUIREMENTS:
• Primary Skill: ${resourcePlan.skill?.name}
• Minimum Experience: ${resourcePlan.minExperience} years
• Allocation: ${resourcePlan.allocationPercent}% (${resourcePlan.allocationPercent === 100 ? 'Full-time' : 'Part-time'})
• Duration: ${new Date(resourcePlan.startDate).toLocaleDateString()} - ${new Date(resourcePlan.endDate).toLocaleDateString()}
${resourcePlan.maxBudget ? `• Budget: Up to $${resourcePlan.maxBudget}/hour` : ''}

RESPONSIBILITIES:
• Work on ${resourcePlan.project?.name} project
• Collaborate with project team
• Deliver high-quality solutions using ${resourcePlan.skill?.name}
${resourcePlan.description ? `• ${resourcePlan.description}` : ''}

QUALIFICATIONS:
• ${resourcePlan.minExperience}+ years of experience in ${resourcePlan.skill?.name}
• Strong problem-solving skills
• Excellent communication abilities
• Team collaboration experience

This is an ${resourcePlan.allocationPercent === 100 ? 'immediate full-time' : 'part-time'} requirement for our ${resourcePlan.project?.name} project.`,
      requiredSkills: [
        {
          skillId: resourcePlan.skillId,
          skillName: resourcePlan.skill?.name,
          proficiencyLevel: 3,
          required: true,
        },
      ],
      minExperience: resourcePlan.minExperience,
      maxBudget: resourcePlan.maxBudget,
      priority: resourcePlan.allocationPercent === 100 ? 'HIGH' : 'MEDIUM',
      expectedDate: resourcePlan.startDate, // Need by start date
    };

    createRequestMutation.mutate(requestData);
  };

  const getMatchScoreColor = (score: number) => {
    if (score >= 90) return 'bg-green-100 text-green-800';
    if (score >= 70) return 'bg-yellow-100 text-yellow-800';
    if (score >= 50) return 'bg-orange-100 text-orange-800';
    return 'bg-red-100 text-red-800';
  };

  const getMatchScoreLabel = (score: number) => {
    if (score >= 90) return 'Excellent';
    if (score >= 70) return 'Good';
    if (score >= 50) return 'Fair';
    return 'Poor';
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Search className="h-5 w-5" />
            <span>Resource Matching - {resourcePlan.role}</span>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Resource Plan Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Resource Requirements</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="font-medium">Role:</span>
                  <p>{resourcePlan.role}</p>
                </div>
                <div>
                  <span className="font-medium">Skill:</span>
                  <p>{resourcePlan.skill?.name}</p>
                </div>
                <div>
                  <span className="font-medium">Allocation:</span>
                  <p>{resourcePlan.allocationPercent}%</p>
                </div>
                <div>
                  <span className="font-medium">Duration:</span>
                  <p>{new Date(resourcePlan.startDate).toLocaleDateString()} - {new Date(resourcePlan.endDate).toLocaleDateString()}</p>
                </div>
                <div>
                  <span className="font-medium">Min Experience:</span>
                  <p>{resourcePlan.minExperience} years</p>
                </div>
                <div>
                  <span className="font-medium">Max Budget:</span>
                  <p>{resourcePlan.maxBudget ? `$${resourcePlan.maxBudget}/hour` : 'No limit'}</p>
                </div>
                <div>
                  <span className="font-medium">Required Count:</span>
                  <p>{resourcePlan.requiredCount}</p>
                </div>
                <div>
                  <span className="font-medium">Status:</span>
                  <Badge variant="outline">{resourcePlan.status}</Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Search Actions */}
          <div className="flex justify-between items-center">
            <Button
              onClick={handleSearch}
              disabled={isSearching}
              className="bg-red-600 hover:bg-red-700"
            >
              <Search className="h-4 w-4 mr-2" />
              {isSearching ? 'Searching...' : 'Find Matching Resources'}
            </Button>

            <Button
              onClick={handleCreateRequest}
              variant="outline"
              disabled={createRequestMutation.isPending}
            >
              <FileText className="h-4 w-4 mr-2" />
              {createRequestMutation.isPending ? 'Creating...' : 'Create Resource Request'}
            </Button>
          </div>

          {/* Search Results */}
          {searchPerformed && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">
                  Matching Resources ({matches.length})
                </h3>
                {matches.length === 0 && searchPerformed && (
                  <div className="bg-amber-50 border border-amber-200 rounded-lg p-3">
                    <div className="flex items-start space-x-2">
                      <AlertCircle className="h-4 w-4 text-amber-600 mt-0.5" />
                      <div>
                        <p className="text-sm font-medium text-amber-800">No Internal Resources Available</p>
                        <p className="text-xs text-amber-700 mt-1">
                          Consider creating a resource request to initiate hiring.
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {matches.length > 0 && (
                <div className="grid gap-4">
                  {matches.map((match) => (
                    <Card key={match.resourceId} className="hover:shadow-md transition-shadow">
                      <CardContent className="p-6">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-3 mb-3">
                              <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                                <User className="h-5 w-5 text-red-600" />
                              </div>
                              <div>
                                <h4 className="font-semibold">
                                  {match.resource.user.firstName} {match.resource.user.lastName}
                                </h4>
                                <p className="text-sm text-gray-600">
                                  {match.resource.designation} • {match.resource.department}
                                </p>
                              </div>
                              <Badge className={getMatchScoreColor(match.matchScore)}>
                                {Math.round(match.matchScore)}% {getMatchScoreLabel(match.matchScore)}
                              </Badge>
                            </div>

                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-4">
                              <div className="flex items-center space-x-2">
                                <Star className="h-4 w-4 text-yellow-500" />
                                <span>Skill: {Math.round(match.skillMatch)}%</span>
                              </div>
                              <div className="flex items-center space-x-2">
                                <Clock className="h-4 w-4 text-blue-500" />
                                <span>Experience: {Math.round(match.experienceMatch)}%</span>
                              </div>
                              <div className="flex items-center space-x-2">
                                <DollarSign className="h-4 w-4 text-green-500" />
                                <span>Budget: {Math.round(match.budgetMatch)}%</span>
                              </div>
                              <div className="flex items-center space-x-2">
                                <CheckCircle className="h-4 w-4 text-purple-500" />
                                <span>Available: {match.availability}%</span>
                              </div>
                            </div>

                            <div className="text-sm text-gray-600">
                              <p><strong>Rate:</strong> ${match.resource.hourlyRate}/hour</p>
                              <p><strong>Skills:</strong> {match.resource.skills?.map((s: any) => s.skill.name).join(', ')}</p>
                              {match.availabilityDetails.conflictingProjects.length > 0 && (
                                <p><strong>Current Projects:</strong> {match.availabilityDetails.conflictingProjects.join(', ')}</p>
                              )}
                            </div>
                          </div>

                          <div className="ml-4">
                            <Button
                              onClick={() => handleAllocateResource(match.resourceId)}
                              disabled={
                                allocateMutation.isPending || 
                                match.availability < resourcePlan.allocationPercent
                              }
                              className="bg-red-600 hover:bg-red-700"
                              size="sm"
                            >
                              <UserPlus className="h-4 w-4 mr-2" />
                              {allocateMutation.isPending && selectedResource === match.resourceId
                                ? 'Allocating...'
                                : 'Allocate'
                              }
                            </Button>
                            {match.availability < resourcePlan.allocationPercent && (
                              <p className="text-xs text-red-600 mt-1">
                                Insufficient availability
                              </p>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
