{"name": "@agentic-talent-pro/frontend", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev -p 3002", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@agentic-talent-pro/shared": "file:../shared", "next": "14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "next-auth": "^4.24.5", "@next-auth/prisma-adapter": "^1.0.7", "@tanstack/react-query": "^5.17.9", "@tanstack/react-query-devtools": "^5.17.9", "axios": "^1.6.2", "zustand": "^4.4.7", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "date-fns": "^3.0.6", "lucide-react": "^0.303.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-popover": "^1.0.7", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "tailwind-merge": "^2.2.0", "tailwindcss-animate": "^1.0.7", "@fullcalendar/core": "^6.1.10", "@fullcalendar/react": "^6.1.10", "@fullcalendar/daygrid": "^6.1.10", "@fullcalendar/timegrid": "^6.1.10", "@fullcalendar/interaction": "^6.1.10", "recharts": "^2.8.0", "react-hot-toast": "^2.4.1"}, "devDependencies": {"typescript": "^5.3.3", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "tailwindcss": "^3.4.0", "eslint": "^8.56.0", "eslint-config-next": "14.0.4"}}