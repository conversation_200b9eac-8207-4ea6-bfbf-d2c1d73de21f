import { z } from 'zod';

// User and Authentication Types
export enum UserRole {
  ADMIN = 'ADMIN',
  PROJECT_MANAGER = 'PROJECT_MANAGER',
  RESOURCE = 'RESOURCE',
  CLIENT = 'CLIENT',
  HR_MANAGER = 'HR_MANAGER',
  BILLING_MANAGER = 'BILLING_MANAGER'
}

export enum UserStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  PENDING = 'PENDING'
}

export const UserSchema = z.object({
  id: z.string(),
  email: z.string().email(),
  firstName: z.string(),
  lastName: z.string(),
  role: z.nativeEnum(UserRole),
  status: z.nativeEnum(UserStatus),
  phone: z.string().optional(),
  avatar: z.string().optional(),
  createdAt: z.date(),
  updatedAt: z.date()
});

export type User = z.infer<typeof UserSchema>;

// Contract Types
export enum ContractStatus {
  DRAFT = 'DRAFT',
  ACTIVE = 'ACTIVE',
  COMPLETED = 'COMPLETED',
  TERMINATED = 'TERMINATED'
}

export enum ContractType {
  FIXED_PRICE = 'FIXED_PRICE',
  TIME_AND_MATERIAL = 'TIME_AND_MATERIAL',
  RETAINER = 'RETAINER'
}

export const ContractSchema = z.object({
  id: z.string(),
  title: z.string(),
  description: z.string(),
  clientId: z.string(),
  type: z.nativeEnum(ContractType),
  status: z.nativeEnum(ContractStatus),
  startDate: z.date(),
  endDate: z.date(),
  value: z.number(),
  currency: z.string().default('USD'),
  terms: z.string(),
  createdAt: z.date(),
  updatedAt: z.date()
});

export type Contract = z.infer<typeof ContractSchema>;

// Project Types
export enum ProjectStatus {
  PLANNING = 'PLANNING',
  ACTIVE = 'ACTIVE',
  ON_HOLD = 'ON_HOLD',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED'
}

export const ProjectSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string(),
  contractId: z.string(),
  managerId: z.string(),
  status: z.nativeEnum(ProjectStatus),
  startDate: z.date(),
  endDate: z.date(),
  budget: z.number(),
  createdAt: z.date(),
  updatedAt: z.date()
});

export type Project = z.infer<typeof ProjectSchema>;

// Resource Types
export enum EmploymentType {
  FULL_TIME = 'FULL_TIME',
  CONTRACTOR = 'CONTRACTOR',
  VENDOR = 'VENDOR'
}

export enum ResourceStatus {
  AVAILABLE = 'AVAILABLE',
  ALLOCATED = 'ALLOCATED',
  ON_LEAVE = 'ON_LEAVE',
  TERMINATED = 'TERMINATED'
}

export const SkillSchema = z.object({
  id: z.string(),
  name: z.string(),
  category: z.string(),
  description: z.string().optional()
});

export type Skill = z.infer<typeof SkillSchema>;

export const ResourceSkillSchema = z.object({
  resourceId: z.string(),
  skillId: z.string(),
  proficiencyLevel: z.number().min(1).max(5),
  yearsOfExperience: z.number(),
  certified: z.boolean().default(false)
});

export type ResourceSkill = z.infer<typeof ResourceSkillSchema>;

export const ResourceSchema = z.object({
  id: z.string(),
  userId: z.string(),
  employeeId: z.string(),
  employmentType: z.nativeEnum(EmploymentType),
  status: z.nativeEnum(ResourceStatus),
  designation: z.string(),
  department: z.string(),
  location: z.string(),
  hourlyRate: z.number(),
  joiningDate: z.date(),
  panNumber: z.string().optional(),
  bankDetails: z.string().optional(),
  backgroundCheck: z.boolean().default(false),
  securityClearance: z.string().optional(),
  createdAt: z.date(),
  updatedAt: z.date()
});

export type Resource = z.infer<typeof ResourceSchema>;

// Task Types
export enum TaskStatus {
  TODO = 'TODO',
  IN_PROGRESS = 'IN_PROGRESS',
  REVIEW = 'REVIEW',
  COMPLETED = 'COMPLETED',
  BLOCKED = 'BLOCKED'
}

export enum TaskPriority {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

export const TaskSchema = z.object({
  id: z.string(),
  title: z.string(),
  description: z.string(),
  projectId: z.string(),
  assignedToId: z.string(),
  status: z.nativeEnum(TaskStatus),
  priority: z.nativeEnum(TaskPriority),
  estimatedHours: z.number(),
  actualHours: z.number().default(0),
  startDate: z.date(),
  dueDate: z.date(),
  createdAt: z.date(),
  updatedAt: z.date()
});

export type Task = z.infer<typeof TaskSchema>;

// Timesheet Types
export enum TimesheetStatus {
  DRAFT = 'DRAFT',
  SUBMITTED = 'SUBMITTED',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED'
}

export const TimesheetEntrySchema = z.object({
  id: z.string(),
  timesheetId: z.string(),
  taskId: z.string(),
  date: z.date(),
  hours: z.number(),
  description: z.string(),
  createdAt: z.date(),
  updatedAt: z.date()
});

export type TimesheetEntry = z.infer<typeof TimesheetEntrySchema>;

export const TimesheetSchema = z.object({
  id: z.string(),
  resourceId: z.string(),
  projectId: z.string(),
  weekStarting: z.date(),
  weekEnding: z.date(),
  status: z.nativeEnum(TimesheetStatus),
  totalHours: z.number(),
  submittedAt: z.date().optional(),
  approvedAt: z.date().optional(),
  approvedBy: z.string().optional(),
  rejectionReason: z.string().optional(),
  createdAt: z.date(),
  updatedAt: z.date()
});

export type Timesheet = z.infer<typeof TimesheetSchema>;

// Invoice Types
export enum InvoiceStatus {
  DRAFT = 'DRAFT',
  SENT = 'SENT',
  PAID = 'PAID',
  OVERDUE = 'OVERDUE',
  CANCELLED = 'CANCELLED'
}

export const InvoiceSchema = z.object({
  id: z.string(),
  invoiceNumber: z.string(),
  projectId: z.string(),
  resourceId: z.string(),
  timesheetId: z.string(),
  status: z.nativeEnum(InvoiceStatus),
  issueDate: z.date(),
  dueDate: z.date(),
  subtotal: z.number(),
  tax: z.number(),
  penalties: z.number().default(0),
  total: z.number(),
  paidAt: z.date().optional(),
  createdAt: z.date(),
  updatedAt: z.date()
});

export type Invoice = z.infer<typeof InvoiceSchema>;

// Vendor Types
export enum VendorStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  BLACKLISTED = 'BLACKLISTED'
}

export const VendorSchema = z.object({
  id: z.string(),
  name: z.string(),
  contactPerson: z.string(),
  email: z.string().email(),
  phone: z.string(),
  address: z.string(),
  panNumber: z.string(),
  gstNumber: z.string().optional(),
  bankDetails: z.string(),
  status: z.nativeEnum(VendorStatus),
  createdAt: z.date(),
  updatedAt: z.date()
});

export type Vendor = z.infer<typeof VendorSchema>;

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Form Types
export interface LoginForm {
  email: string;
  password: string;
}

export interface CreateContractForm {
  title: string;
  description: string;
  clientId: string;
  type: ContractType;
  startDate: Date;
  endDate: Date;
  value: number;
  currency: string;
  terms: string;
}

export interface CreateProjectForm {
  name: string;
  description: string;
  contractId: string;
  managerId: string;
  startDate: Date;
  endDate: Date;
  budget: number;
}

export interface CreateTaskForm {
  title: string;
  description: string;
  projectId: string;
  assignedToId: string;
  priority: TaskPriority;
  estimatedHours: number;
  startDate: Date;
  dueDate: Date;
}

export interface TimesheetEntryForm {
  taskId: string;
  date: Date;
  hours: number;
  description: string;
}
