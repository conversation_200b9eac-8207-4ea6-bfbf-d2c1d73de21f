#!/bin/bash

# Agentic Talent Pro Setup Script
echo "🚀 Setting up Agentic Talent Pro..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ and try again."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version 18 or higher is required. Current version: $(node -v)"
    exit 1
fi

# Check if PostgreSQL is installed
if ! command -v psql &> /dev/null; then
    echo "⚠️  PostgreSQL is not installed. Please install PostgreSQL 14+ and create a database."
    echo "   You can install PostgreSQL from: https://www.postgresql.org/download/"
    echo "   After installation, create a database named 'agentic_talent_pro'"
fi

echo "📦 Installing dependencies..."

# Install root dependencies
npm install

# Install shared package dependencies
echo "📦 Installing shared package dependencies..."
cd shared && npm install && npm run build && cd ..

# Install backend dependencies
echo "📦 Installing backend dependencies..."
cd backend && npm install && cd ..

# Install frontend dependencies
echo "📦 Installing frontend dependencies..."
cd frontend && npm install && cd ..

echo "⚙️  Setting up environment files..."

# Copy environment files
if [ ! -f backend/.env ]; then
    cp backend/.env.example backend/.env
    echo "📝 Created backend/.env from example. Please update with your database credentials."
fi

if [ ! -f frontend/.env.local ]; then
    cp frontend/.env.example frontend/.env.local
    echo "📝 Created frontend/.env.local from example."
fi

echo "🗄️  Setting up database..."

# Check if DATABASE_URL is set
if grep -q "postgresql://username:password@localhost:5432/agentic_talent_pro" backend/.env; then
    echo "⚠️  Please update the DATABASE_URL in backend/.env with your PostgreSQL credentials"
    echo "   Example: DATABASE_URL=\"postgresql://your_username:your_password@localhost:5432/agentic_talent_pro\""
    echo ""
    echo "   After updating the DATABASE_URL, run:"
    echo "   npm run db:migrate"
    echo "   npm run db:seed"
else
    echo "🔄 Running database migrations..."
    cd backend && npm run db:migrate
    
    echo "🌱 Seeding database with sample data..."
    npm run db:seed
    cd ..
fi

echo ""
echo "✅ Setup complete!"
echo ""
echo "🚀 To start the application:"
echo "   npm run dev"
echo ""
echo "📚 The application will be available at:"
echo "   Frontend: http://localhost:3002"
echo "   Backend API: http://localhost:3003"
echo "   API Documentation: http://localhost:3003/api/docs"
echo "   Database Studio: npm run db:studio"
echo ""
echo "🔐 Demo login credentials:"
echo "   Admin: <EMAIL> / admin123"
echo "   Project Manager: <EMAIL> / pm123"
echo "   HR Manager: <EMAIL> / hr123"
echo "   Billing Manager: <EMAIL> / billing123"
echo "   Resource: <EMAIL> / resource123"
echo "   Client: <EMAIL> / client123"
echo ""
echo "📖 For more information, see README.md"
