# Agentic Talent Pro

A comprehensive production-ready application for Contract Management, Project Management, HRMS, and Billing Management.

## Features

### 🔐 Contract Management
- CRUD operations for contracts
- Contract templates and workflows
- Client management
- Contract analytics

### 📊 Project Management
- Project creation from contracts
- Resource planning with skills matching
- Task management and assignment
- Resource allocation and utilization tracking

### 👥 Manpower Solution (HRMS)
- Workforce management with comprehensive employee profiles
- Recruitment and hiring workflows
- Resource utilization tracking
- Calendar-based timesheet management
- Approval workflows
- Vendor onboarding and management

### 💰 Billing Management
- Automated invoice generation
- Payroll processing
- Contract-based billing
- Penalties and arrears management

## Technology Stack

- **Frontend**: Next.js 14, React 18, TypeScript, Tailwind CSS
- **Backend**: Node.js, Express, TypeScript
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: NextAuth.js
- **UI Components**: shadcn/ui
- **Calendar**: FullCalendar
- **Charts**: Recharts

## Quick Start

### Prerequisites
- Node.js 18+
- PostgreSQL 14+
- npm 9+

### Automated Setup

The easiest way to get started is using our setup script:

```bash
# Make the setup script executable and run it
chmod +x setup.sh
./setup.sh
```

This script will:
- Install all dependencies
- Set up environment files
- Run database migrations
- Seed the database with sample data

### Manual Installation

If you prefer to set up manually:

1. **Clone the repository**
```bash
git clone <repository-url>
cd agentic-talent-pro
```

2. **Install dependencies**
```bash
npm install
```

3. **Set up environment variables**
```bash
cp backend/.env.example backend/.env
cp frontend/.env.example frontend/.env.local
```

4. **Configure your database**
Edit `backend/.env` and update the `DATABASE_URL`:
```env
DATABASE_URL="postgresql://your_username:your_password@localhost:5432/agentic_talent_pro"
```

5. **Set up the database**
```bash
npm run db:migrate
npm run db:seed
```

6. **Start the development servers**
```bash
npm run dev
```

### Using Docker

For a containerized setup:

```bash
# Start all services with Docker Compose
docker-compose up -d

# Run database migrations and seed
docker-compose exec backend npm run db:migrate
docker-compose exec backend npm run db:seed
```

### Access Points

The application will be available at:
- **Frontend**: http://localhost:3002
- **Backend API**: http://localhost:3003
- **API Documentation**: http://localhost:3003/api/docs
- **Database Studio**: http://localhost:5555 (run `npm run db:studio`)

## Project Structure

```
agentic-talent-pro/
├── frontend/          # Next.js frontend application
├── backend/           # Express.js backend API
├── shared/            # Shared TypeScript types and utilities
├── docs/              # API documentation
└── database/          # Database migrations and seeds
```

## Development

### Running Tests
```bash
npm run test
```

### Linting
```bash
npm run lint
```

### Database Operations
```bash
npm run db:migrate     # Run migrations
npm run db:generate    # Generate Prisma client
npm run db:studio      # Open Prisma Studio
npm run db:seed        # Seed database with sample data
```

## Deployment

### Production Build
```bash
npm run build
```

### Environment Variables
See `.env.example` files in backend and frontend directories for required environment variables.

## API Documentation

API documentation is available at `/api/docs` when running the backend server.

## Demo Credentials

The seeded database includes the following demo accounts:

| Role | Email | Password | Description |
|------|-------|----------|-------------|
| Admin | <EMAIL> | admin123 | Full system access |
| Project Manager | <EMAIL> | pm123 | Project management access |
| HR Manager | <EMAIL> | hr123 | HR and resource management |
| Billing Manager | <EMAIL> | billing123 | Billing and invoice management |
| Resource | <EMAIL> | resource123 | Employee/contractor access |
| Client | <EMAIL> | client123 | Client portal access |

## Key Features Implemented

### 🔐 Contract Management
- ✅ CRUD operations for contracts
- ✅ Contract status tracking (Draft, Active, Completed, Terminated)
- ✅ Client management integration
- ✅ Contract-based project creation

### 📊 Project Management
- ✅ Project creation from contracts
- ✅ Resource planning and allocation
- ✅ Task management and assignment
- ✅ Project status tracking
- ✅ Resource matching based on skills

### 👥 Manpower Solution (HRMS)
- ✅ Comprehensive employee profiles
- ✅ Skills and certification tracking
- ✅ Resource availability management
- ✅ Vendor management
- ✅ Employment type handling (FTE, Contractor, Vendor)

### ⏰ Timesheet Management
- ✅ Calendar-based timesheet interface
- ✅ Weekly timesheet submission
- ✅ Approval workflow (Project Manager → Approved)
- ✅ Task-based time tracking
- ✅ Timesheet status management

### 💰 Billing Management
- ✅ Automated invoice generation from timesheets
- ✅ Contract-based billing rates
- ✅ Tax and penalty calculations
- ✅ Invoice status tracking
- ✅ Payroll processing workflows

### 🔒 Security & Access Control
- ✅ Role-based access control (RBAC)
- ✅ JWT-based authentication
- ✅ Route-level permissions
- ✅ Data access restrictions

### 📈 Analytics & Reporting
- ✅ Dashboard with key metrics
- ✅ Project status distribution
- ✅ Resource utilization charts
- ✅ Monthly invoice trends
- ✅ Role-specific dashboards

## Workflow Implementation

The application implements the complete workflow as requested:

1. **Contract Creation** → Admin/PM creates contracts with clients
2. **Project Setup** → Projects are created from active contracts
3. **Resource Planning** → Define required skills and roles for projects
4. **Resource Allocation** → Assign available resources or request hiring
5. **Task Management** → PM creates tasks and assigns to resources
6. **Time Tracking** → Resources log time using calendar interface
7. **Approval Process** → PM approves submitted timesheets
8. **Invoice Generation** → Billing manager generates invoices from approved timesheets
9. **Payroll Processing** → Process payments based on invoices

## Technology Stack

- **Frontend**: Next.js 14, React 18, TypeScript, Tailwind CSS
- **Backend**: Node.js, Express, TypeScript, Prisma ORM
- **Database**: PostgreSQL
- **Authentication**: JWT with role-based access control
- **UI Components**: Radix UI with shadcn/ui
- **Charts**: Recharts for analytics
- **Calendar**: FullCalendar for timesheet management
- **State Management**: Zustand + React Query
- **Validation**: Zod schemas
- **API Documentation**: Swagger/OpenAPI

## Production Deployment

### Environment Variables

**Backend (.env)**:
```env
DATABASE_URL="postgresql://username:password@host:port/database"
JWT_SECRET="your-super-secret-jwt-key"
JWT_REFRESH_SECRET="your-super-secret-refresh-key"
NODE_ENV="production"
PORT=3001
CORS_ORIGIN="https://your-frontend-domain.com"
```

**Frontend (.env.local)**:
```env
NEXT_PUBLIC_API_URL="https://your-api-domain.com/api"
NEXTAUTH_URL="https://your-frontend-domain.com"
NEXTAUTH_SECRET="your-nextauth-secret"
```

### Docker Deployment

```bash
# Production deployment with Docker
docker-compose -f docker-compose.prod.yml up -d
```

### Manual Deployment

```bash
# Build all packages
npm run build

# Start production server
npm run start
```

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## Support

For support and questions:
- Create an issue in the repository
- Check the API documentation at `/api/docs`
- Review the codebase documentation

## License

Private - All rights reserved
